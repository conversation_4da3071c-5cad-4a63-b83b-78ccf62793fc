class FleetOverview {
  final int totalTruckHeads;
  final int availableTruckHeads;
  final int assignedTruckHeads;
  final int inTransitTruckHeads;
  final int maintenanceTruckHeads;
  final int totalTrailers;
  final int availableTrailers;
  final int assignedTrailers;
  final int inTransitTrailers;
  final int maintenanceTrailers;
  final int totalAssignments;
  final int activeAssignments;
  final int plannedAssignments;
  final int completedAssignments;
  final int truckHeadsWithExpiringInsurance;
  final int truckHeadsWithExpiringFitness;
  final int truckHeadsWithExpiringPermits;
  final int truckHeadsWithExpiringTax;
  final int trailersWithExpiringInsurance;
  final int trailersWithExpiringFitness;
  final int trailersWithExpiringPermits;

  FleetOverview({
    required this.totalTruckHeads,
    required this.availableTruckHeads,
    required this.assignedTruckHeads,
    required this.inTransitTruckHeads,
    required this.maintenanceTruckHeads,
    required this.totalTrailers,
    required this.availableTrailers,
    required this.assignedTrailers,
    required this.inTransitTrailers,
    required this.maintenanceTrailers,
    required this.totalAssignments,
    required this.activeAssignments,
    required this.plannedAssignments,
    required this.completedAssignments,
    required this.truckHeadsWithExpiringInsurance,
    required this.truckHeadsWithExpiringFitness,
    required this.truckHeadsWithExpiringPermits,
    required this.truckHeadsWithExpiringTax,
    required this.trailersWithExpiringInsurance,
    required this.trailersWithExpiringFitness,
    required this.trailersWithExpiringPermits,
  });

  double get truckHeadUtilizationRate {
    return totalTruckHeads > 0 
        ? (assignedTruckHeads + inTransitTruckHeads) / totalTruckHeads * 100 
        : 0;
  }

  double get trailerUtilizationRate {
    return totalTrailers > 0 
        ? (assignedTrailers + inTransitTrailers) / totalTrailers * 100 
        : 0;
  }

  int get totalComplianceAlerts {
    return truckHeadsWithExpiringInsurance + 
           truckHeadsWithExpiringFitness + 
           truckHeadsWithExpiringPermits + 
           truckHeadsWithExpiringTax +
           trailersWithExpiringInsurance + 
           trailersWithExpiringFitness + 
           trailersWithExpiringPermits;
  }

  factory FleetOverview.fromJson(Map<String, dynamic> json) {
    return FleetOverview(
      totalTruckHeads: json['totalTruckHeads'] ?? 0,
      availableTruckHeads: json['availableTruckHeads'] ?? 0,
      assignedTruckHeads: json['assignedTruckHeads'] ?? 0,
      inTransitTruckHeads: json['inTransitTruckHeads'] ?? 0,
      maintenanceTruckHeads: json['maintenanceTruckHeads'] ?? 0,
      totalTrailers: json['totalTrailers'] ?? 0,
      availableTrailers: json['availableTrailers'] ?? 0,
      assignedTrailers: json['assignedTrailers'] ?? 0,
      inTransitTrailers: json['inTransitTrailers'] ?? 0,
      maintenanceTrailers: json['maintenanceTrailers'] ?? 0,
      totalAssignments: json['totalAssignments'] ?? 0,
      activeAssignments: json['activeAssignments'] ?? 0,
      plannedAssignments: json['plannedAssignments'] ?? 0,
      completedAssignments: json['completedAssignments'] ?? 0,
      truckHeadsWithExpiringInsurance: json['truckHeadsWithExpiringInsurance'] ?? 0,
      truckHeadsWithExpiringFitness: json['truckHeadsWithExpiringFitness'] ?? 0,
      truckHeadsWithExpiringPermits: json['truckHeadsWithExpiringPermits'] ?? 0,
      truckHeadsWithExpiringTax: json['truckHeadsWithExpiringTax'] ?? 0,
      trailersWithExpiringInsurance: json['trailersWithExpiringInsurance'] ?? 0,
      trailersWithExpiringFitness: json['trailersWithExpiringFitness'] ?? 0,
      trailersWithExpiringPermits: json['trailersWithExpiringPermits'] ?? 0,
    );
  }
}

class ComplianceAlert {
  final String alertType;
  final String severity;
  final String title;
  final String message;
  final String resourceType;
  final int resourceId;
  final String resourceName;
  final DateTime expiryDate;
  final int daysToExpiry;

  ComplianceAlert({
    required this.alertType,
    required this.severity,
    required this.title,
    required this.message,
    required this.resourceType,
    required this.resourceId,
    required this.resourceName,
    required this.expiryDate,
    required this.daysToExpiry,
  });

  String get severityColor {
    switch (severity) {
      case 'CRITICAL':
        return '#FF0000'; // Red
      case 'HIGH':
        return '#FF8C00'; // Dark Orange
      case 'MEDIUM':
        return '#FFD700'; // Gold
      case 'LOW':
        return '#32CD32'; // Lime Green
      default:
        return '#808080'; // Gray
    }
  }

  String get alertIcon {
    switch (alertType) {
      case 'INSURANCE_EXPIRY':
        return 'shield-alert';
      case 'TAX_CLEARANCE_EXPIRY':
        return 'receipt-tax';
      case 'PERMIT_EXPIRY':
        return 'file-certificate';
      case 'FITNESS_EXPIRY':
        return 'clipboard-check';
      default:
        return 'alert-triangle';
    }
  }

  factory ComplianceAlert.fromJson(Map<String, dynamic> json) {
    return ComplianceAlert(
      alertType: json['alertType'] ?? '',
      severity: json['severity'] ?? '',
      title: json['title'] ?? '',
      message: json['message'] ?? '',
      resourceType: json['resourceType'] ?? '',
      resourceId: json['resourceId'] ?? 0,
      resourceName: json['resourceName'] ?? '',
      expiryDate: DateTime.parse(json['expiryDate']),
      daysToExpiry: json['daysToExpiry'] ?? 0,
    );
  }
}

class TruckHead {
  final int id;
  final String registrationNumber;
  final String make;
  final String model;
  final int? year;
  final String? engineNumber;
  final String? chassisNumber;
  final String status;
  final String? fuelType;
  final double? fuelCapacity;
  final double? maxTowingCapacity;
  final String? capacityUnit;
  final String? description;
  final String? imageUrl;
  final String? currentLocation;
  final double? currentLatitude;
  final double? currentLongitude;
  final bool hasInsurance;
  final bool hasFitnessCertificate;
  final bool hasRoadPermit;
  final bool hasTaxClearance;
  final DateTime? insuranceExpiryDate;
  final DateTime? fitnessExpiryDate;
  final DateTime? roadPermitExpiryDate;
  final DateTime? taxClearanceExpiryDate;
  final DateTime? lastMaintenanceDate;
  final DateTime? nextMaintenanceDate;
  final int? companyId;
  final String? companyName;
  final int? currentDriverId;
  final String? currentDriverName;
  final DateTime createdAt;
  final DateTime updatedAt;

  TruckHead({
    required this.id,
    required this.registrationNumber,
    required this.make,
    required this.model,
    this.year,
    this.engineNumber,
    this.chassisNumber,
    required this.status,
    this.fuelType,
    this.fuelCapacity,
    this.maxTowingCapacity,
    this.capacityUnit,
    this.description,
    this.imageUrl,
    this.currentLocation,
    this.currentLatitude,
    this.currentLongitude,
    required this.hasInsurance,
    required this.hasFitnessCertificate,
    required this.hasRoadPermit,
    required this.hasTaxClearance,
    this.insuranceExpiryDate,
    this.fitnessExpiryDate,
    this.roadPermitExpiryDate,
    this.taxClearanceExpiryDate,
    this.lastMaintenanceDate,
    this.nextMaintenanceDate,
    this.companyId,
    this.companyName,
    this.currentDriverId,
    this.currentDriverName,
    required this.createdAt,
    required this.updatedAt,
  });

  factory TruckHead.fromJson(Map<String, dynamic> json) {
    return TruckHead(
      id: json['id'],
      registrationNumber: json['registrationNumber'],
      make: json['make'],
      model: json['model'],
      year: json['year'],
      engineNumber: json['engineNumber'],
      chassisNumber: json['chassisNumber'],
      status: json['status'],
      fuelType: json['fuelType'],
      fuelCapacity: json['fuelCapacity']?.toDouble(),
      maxTowingCapacity: json['maxTowingCapacity']?.toDouble(),
      capacityUnit: json['capacityUnit'],
      description: json['description'],
      imageUrl: json['imageUrl'],
      currentLocation: json['currentLocation'],
      currentLatitude: json['currentLatitude']?.toDouble(),
      currentLongitude: json['currentLongitude']?.toDouble(),
      hasInsurance: json['hasInsurance'] ?? false,
      hasFitnessCertificate: json['hasFitnessCertificate'] ?? false,
      hasRoadPermit: json['hasRoadPermit'] ?? false,
      hasTaxClearance: json['hasTaxClearance'] ?? false,
      insuranceExpiryDate: json['insuranceExpiryDate'] != null 
          ? DateTime.parse(json['insuranceExpiryDate']) 
          : null,
      fitnessExpiryDate: json['fitnessExpiryDate'] != null 
          ? DateTime.parse(json['fitnessExpiryDate']) 
          : null,
      roadPermitExpiryDate: json['roadPermitExpiryDate'] != null 
          ? DateTime.parse(json['roadPermitExpiryDate']) 
          : null,
      taxClearanceExpiryDate: json['taxClearanceExpiryDate'] != null 
          ? DateTime.parse(json['taxClearanceExpiryDate']) 
          : null,
      lastMaintenanceDate: json['lastMaintenanceDate'] != null 
          ? DateTime.parse(json['lastMaintenanceDate']) 
          : null,
      nextMaintenanceDate: json['nextMaintenanceDate'] != null 
          ? DateTime.parse(json['nextMaintenanceDate']) 
          : null,
      companyId: json['companyId'],
      companyName: json['companyName'],
      currentDriverId: json['currentDriverId'],
      currentDriverName: json['currentDriverName'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }
}

class FleetAssignment {
  final int id;
  final int loadId;
  final String? loadTitle;
  final String? loadTrackingNumber;
  final int? bidId;
  final int? truckHeadId;
  final String? truckHeadRegistration;
  final String? truckHeadMake;
  final String? truckHeadModel;
  final int? trailer1Id;
  final String? trailer1Registration;
  final String? trailer1Type;
  final int? trailer2Id;
  final String? trailer2Registration;
  final String? trailer2Type;
  final int? primaryDriverId;
  final String? primaryDriverName;
  final int? secondaryDriverId;
  final String? secondaryDriverName;
  final int companyId;
  final String companyName;
  final String status;
  final DateTime? assignedAt;
  final DateTime scheduledStartTime;
  final DateTime? actualStartTime;
  final DateTime scheduledEndTime;
  final DateTime? actualEndTime;
  final String? notes;
  final int? assignedById;
  final String? assignedByName;
  final DateTime createdAt;
  final DateTime updatedAt;

  FleetAssignment({
    required this.id,
    required this.loadId,
    this.loadTitle,
    this.loadTrackingNumber,
    this.bidId,
    this.truckHeadId,
    this.truckHeadRegistration,
    this.truckHeadMake,
    this.truckHeadModel,
    this.trailer1Id,
    this.trailer1Registration,
    this.trailer1Type,
    this.trailer2Id,
    this.trailer2Registration,
    this.trailer2Type,
    this.primaryDriverId,
    this.primaryDriverName,
    this.secondaryDriverId,
    this.secondaryDriverName,
    required this.companyId,
    required this.companyName,
    required this.status,
    this.assignedAt,
    required this.scheduledStartTime,
    this.actualStartTime,
    required this.scheduledEndTime,
    this.actualEndTime,
    this.notes,
    this.assignedById,
    this.assignedByName,
    required this.createdAt,
    required this.updatedAt,
  });

  factory FleetAssignment.fromJson(Map<String, dynamic> json) {
    return FleetAssignment(
      id: json['id'],
      loadId: json['loadId'],
      loadTitle: json['loadTitle'],
      loadTrackingNumber: json['loadTrackingNumber'],
      bidId: json['bidId'],
      truckHeadId: json['truckHeadId'],
      truckHeadRegistration: json['truckHeadRegistration'],
      truckHeadMake: json['truckHeadMake'],
      truckHeadModel: json['truckHeadModel'],
      trailer1Id: json['trailer1Id'],
      trailer1Registration: json['trailer1Registration'],
      trailer1Type: json['trailer1Type'],
      trailer2Id: json['trailer2Id'],
      trailer2Registration: json['trailer2Registration'],
      trailer2Type: json['trailer2Type'],
      primaryDriverId: json['primaryDriverId'],
      primaryDriverName: json['primaryDriverName'],
      secondaryDriverId: json['secondaryDriverId'],
      secondaryDriverName: json['secondaryDriverName'],
      companyId: json['companyId'],
      companyName: json['companyName'],
      status: json['status'],
      assignedAt: json['assignedAt'] != null 
          ? DateTime.parse(json['assignedAt']) 
          : null,
      scheduledStartTime: DateTime.parse(json['scheduledStartTime']),
      actualStartTime: json['actualStartTime'] != null 
          ? DateTime.parse(json['actualStartTime']) 
          : null,
      scheduledEndTime: DateTime.parse(json['scheduledEndTime']),
      actualEndTime: json['actualEndTime'] != null 
          ? DateTime.parse(json['actualEndTime']) 
          : null,
      notes: json['notes'],
      assignedById: json['assignedById'],
      assignedByName: json['assignedByName'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }
}
