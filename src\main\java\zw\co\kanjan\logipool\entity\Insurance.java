package zw.co.kanjan.logipool.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "insurances")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Insurance {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank
    @Size(max = 50)
    private String policyNumber;
    
    @NotBlank
    @Size(max = 100)
    private String insuranceProvider;
    
    @Enumerated(EnumType.STRING)
    @Column(length = 30)
    @NotNull
    private InsuranceType type;
    
    @Enumerated(EnumType.STRING)
    @Column(length = 20)
    @Builder.Default
    private InsuranceStatus status = InsuranceStatus.ACTIVE;
    
    @NotNull
    private LocalDateTime startDate;
    
    @NotNull
    private LocalDateTime expiryDate;
    
    private BigDecimal premiumAmount;
    
    private BigDecimal coverageAmount;
    
    @Size(max = 20)
    private String currency = "USD";
    
    @Column(columnDefinition = "TEXT")
    private String coverageDetails;
    
    @Column(columnDefinition = "TEXT")
    private String terms;
    
    @Size(max = 100)
    private String brokerName;
    
    @Size(max = 20)
    private String brokerPhone;
    
    @Size(max = 100)
    private String brokerEmail;
    
    // Relationships - can be associated with different entities
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "company_id")
    private Company company;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "vehicle_id")
    private Vehicle vehicle;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "truck_head_id")
    private TruckHead truckHead;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "trailer_id")
    private Trailer trailer;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "equipment_id")
    private Equipment equipment;
    
    @OneToMany(mappedBy = "insurance", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @Builder.Default
    private List<Document> documents = new ArrayList<>();
    
    @CreationTimestamp
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    private LocalDateTime updatedAt;
    
    public enum InsuranceType {
        COMPREHENSIVE,      // Full coverage
        THIRD_PARTY,       // Third party only
        FIRE_THEFT,        // Fire and theft
        GOODS_IN_TRANSIT,  // Cargo insurance
        PUBLIC_LIABILITY,  // Public liability
        EMPLOYERS_LIABILITY, // Employers liability
        PROFESSIONAL_INDEMNITY // Professional indemnity
    }
    
    public enum InsuranceStatus {
        ACTIVE,     // Currently active
        EXPIRED,    // Has expired
        CANCELLED,  // Was cancelled
        SUSPENDED,  // Temporarily suspended
        PENDING     // Pending activation
    }
}
