import 'package:json_annotation/json_annotation.dart';

part 'driver_model.g.dart';

@JsonSerializable()
class DriverModel {
  final int? id;
  final String? firstName;
  final String? lastName;
  final String? email;
  final String? phoneNumber;
  final String? licenseNumber;
  final String? licenseClass;
  final DateTime? licenseExpiryDate;
  final String? nationalId;
  final String? passportNumber;
  final DriverVerificationStatus? verificationStatus;
  final CompanyInfo? company;
  final DateTime? createdAt;
  final DateTime? lastLoginAt;

  DriverModel({
    this.id,
    this.firstName,
    this.lastName,
    this.email,
    this.phoneNumber,
    this.licenseNumber,
    this.licenseClass,
    this.licenseExpiryDate,
    this.nationalId,
    this.passportNumber,
    this.verificationStatus,
    this.company,
    this.createdAt,
    this.lastLoginAt,
  });

  factory DriverModel.fromJson(Map<String, dynamic> json) => _$DriverModelFromJson(json);
  Map<String, dynamic> toJson() => _$DriverModelToJson(this);

  String get fullName => '${firstName ?? ''} ${lastName ?? ''}'.trim();
}

@JsonSerializable()
class DriverRegistrationRequest {
  final String firstName;
  final String lastName;
  final String email;
  final String password;
  final String? phoneNumber;
  final String? licenseNumber;
  final String? licenseClass;
  final DateTime? licenseExpiryDate;
  final String? nationalId;
  final String? passportNumber;
  final int? companyId;

  DriverRegistrationRequest({
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.password,
    this.phoneNumber,
    this.licenseNumber,
    this.licenseClass,
    this.licenseExpiryDate,
    this.nationalId,
    this.passportNumber,
    this.companyId,
  });

  factory DriverRegistrationRequest.fromJson(Map<String, dynamic> json) => 
      _$DriverRegistrationRequestFromJson(json);
  Map<String, dynamic> toJson() => _$DriverRegistrationRequestToJson(this);
}

@JsonSerializable()
class DriverUpdateRequest {
  final String? firstName;
  final String? lastName;
  final String? phoneNumber;
  final String? licenseNumber;
  final String? licenseClass;
  final DateTime? licenseExpiryDate;
  final String? nationalId;
  final String? passportNumber;

  DriverUpdateRequest({
    this.firstName,
    this.lastName,
    this.phoneNumber,
    this.licenseNumber,
    this.licenseClass,
    this.licenseExpiryDate,
    this.nationalId,
    this.passportNumber,
  });

  factory DriverUpdateRequest.fromJson(Map<String, dynamic> json) => 
      _$DriverUpdateRequestFromJson(json);
  Map<String, dynamic> toJson() => _$DriverUpdateRequestToJson(this);
}

@JsonSerializable()
class AssignToCompanyRequest {
  final int driverId;
  final int companyId;
  final String? role;
  final bool? canUpdateLoadStatus;
  final bool? canTrackLocation;

  AssignToCompanyRequest({
    required this.driverId,
    required this.companyId,
    this.role = 'DRIVER',
    this.canUpdateLoadStatus = true,
    this.canTrackLocation = true,
  });

  factory AssignToCompanyRequest.fromJson(Map<String, dynamic> json) => 
      _$AssignToCompanyRequestFromJson(json);
  Map<String, dynamic> toJson() => _$AssignToCompanyRequestToJson(this);
}

@JsonSerializable()
class DriverVerificationStatus {
  final VerificationTaskStatus? licenseVerification;
  final VerificationTaskStatus? defensiveTrainingVerification;
  final VerificationTaskStatus? nationalIdVerification;
  final VerificationTaskStatus? passportVerification;
  final VerificationTaskStatus? phoneVerification;
  final OverallVerificationStatus? overallStatus;

  DriverVerificationStatus({
    this.licenseVerification,
    this.defensiveTrainingVerification,
    this.nationalIdVerification,
    this.passportVerification,
    this.phoneVerification,
    this.overallStatus,
  });

  factory DriverVerificationStatus.fromJson(Map<String, dynamic> json) => 
      _$DriverVerificationStatusFromJson(json);
  Map<String, dynamic> toJson() => _$DriverVerificationStatusToJson(this);
}

@JsonSerializable()
class VerificationTaskStatus {
  final VerificationStatus? status;
  final String? notes;
  final int? verifiedBy;
  final DateTime? verifiedAt;

  VerificationTaskStatus({
    this.status,
    this.notes,
    this.verifiedBy,
    this.verifiedAt,
  });

  factory VerificationTaskStatus.fromJson(Map<String, dynamic> json) => 
      _$VerificationTaskStatusFromJson(json);
  Map<String, dynamic> toJson() => _$VerificationTaskStatusToJson(this);
}

@JsonSerializable()
class CompanyInfo {
  final int? id;
  final String? name;
  final String? role;
  final String? status;

  CompanyInfo({
    this.id,
    this.name,
    this.role,
    this.status,
  });

  factory CompanyInfo.fromJson(Map<String, dynamic> json) => 
      _$CompanyInfoFromJson(json);
  Map<String, dynamic> toJson() => _$CompanyInfoToJson(this);
}

@JsonSerializable()
class DriverVerificationSummary {
  final int? driverId;
  final String? driverName;
  final String? driverEmail;
  final OverallVerificationStatus? overallStatus;
  final List<VerificationTaskResponse>? verificationTasks;
  final int? totalTasks;
  final int? completedTasks;
  final int? pendingTasks;
  final int? rejectedTasks;
  final double? completionPercentage;
  final DateTime? lastUpdated;

  DriverVerificationSummary({
    this.driverId,
    this.driverName,
    this.driverEmail,
    this.overallStatus,
    this.verificationTasks,
    this.totalTasks,
    this.completedTasks,
    this.pendingTasks,
    this.rejectedTasks,
    this.completionPercentage,
    this.lastUpdated,
  });

  factory DriverVerificationSummary.fromJson(Map<String, dynamic> json) => 
      _$DriverVerificationSummaryFromJson(json);
  Map<String, dynamic> toJson() => _$DriverVerificationSummaryToJson(this);
}

@JsonSerializable()
class VerificationTaskResponse {
  final int? id;
  final int? driverId;
  final String? driverName;
  final VerificationType? verificationType;
  final String? taskName;
  final String? taskDescription;
  final VerificationStatus? status;
  final String? notes;
  final int? verifiedBy;
  final String? verifiedByName;
  final DateTime? verifiedAt;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  VerificationTaskResponse({
    this.id,
    this.driverId,
    this.driverName,
    this.verificationType,
    this.taskName,
    this.taskDescription,
    this.status,
    this.notes,
    this.verifiedBy,
    this.verifiedByName,
    this.verifiedAt,
    this.createdAt,
    this.updatedAt,
  });

  factory VerificationTaskResponse.fromJson(Map<String, dynamic> json) => 
      _$VerificationTaskResponseFromJson(json);
  Map<String, dynamic> toJson() => _$VerificationTaskResponseToJson(this);
}

@JsonSerializable()
class UpdateVerificationRequest {
  final int driverId;
  final VerificationType verificationType;
  final VerificationStatus status;
  final String? notes;

  UpdateVerificationRequest({
    required this.driverId,
    required this.verificationType,
    required this.status,
    this.notes,
  });

  factory UpdateVerificationRequest.fromJson(Map<String, dynamic> json) => 
      _$UpdateVerificationRequestFromJson(json);
  Map<String, dynamic> toJson() => _$UpdateVerificationRequestToJson(this);
}

@JsonSerializable()
class VerificationStats {
  final int? totalDrivers;
  final int? fullyVerifiedDrivers;
  final int? partiallyVerifiedDrivers;
  final int? unverifiedDrivers;
  final int? expiredVerificationDrivers;
  final double? verificationCompletionRate;
  final List<VerificationTypeStats>? verificationTypeStats;

  VerificationStats({
    this.totalDrivers,
    this.fullyVerifiedDrivers,
    this.partiallyVerifiedDrivers,
    this.unverifiedDrivers,
    this.expiredVerificationDrivers,
    this.verificationCompletionRate,
    this.verificationTypeStats,
  });

  factory VerificationStats.fromJson(Map<String, dynamic> json) => 
      _$VerificationStatsFromJson(json);
  Map<String, dynamic> toJson() => _$VerificationStatsToJson(this);
}

@JsonSerializable()
class VerificationTypeStats {
  final VerificationType? verificationType;
  final String? taskName;
  final int? verifiedCount;
  final int? pendingCount;
  final int? rejectedCount;
  final int? expiredCount;
  final double? completionRate;

  VerificationTypeStats({
    this.verificationType,
    this.taskName,
    this.verifiedCount,
    this.pendingCount,
    this.rejectedCount,
    this.expiredCount,
    this.completionRate,
  });

  factory VerificationTypeStats.fromJson(Map<String, dynamic> json) => 
      _$VerificationTypeStatsFromJson(json);
  Map<String, dynamic> toJson() => _$VerificationTypeStatsToJson(this);
}

enum VerificationStatus {
  @JsonValue('PENDING')
  pending,
  @JsonValue('VERIFIED')
  verified,
  @JsonValue('REJECTED')
  rejected,
  @JsonValue('EXPIRED')
  expired,
  @JsonValue('NOT_REQUIRED')
  notRequired,
}

enum OverallVerificationStatus {
  @JsonValue('UNVERIFIED')
  unverified,
  @JsonValue('PARTIALLY_VERIFIED')
  partiallyVerified,
  @JsonValue('FULLY_VERIFIED')
  fullyVerified,
  @JsonValue('VERIFICATION_EXPIRED')
  verificationExpired,
}

enum VerificationType {
  @JsonValue('LICENSE')
  license,
  @JsonValue('DEFENSIVE_TRAINING')
  defensiveTraining,
  @JsonValue('NATIONAL_ID')
  nationalId,
  @JsonValue('PASSPORT')
  passport,
  @JsonValue('PHONE_NUMBER')
  phoneNumber,
}
