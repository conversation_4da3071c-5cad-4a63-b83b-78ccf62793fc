import 'package:flutter/material.dart';
import '../../../shared/models/driver_model.dart';
import '../../../shared/services/driver_service.dart';
import '../../../shared/widgets/loading_widget.dart';
import '../../../shared/widgets/error_widget.dart';
import '../../../core/di/service_locator.dart';

class DriverManagementScreen extends StatefulWidget {
  const DriverManagementScreen({Key? key}) : super(key: key);

  @override
  State<DriverManagementScreen> createState() => _DriverManagementScreenState();
}

class _DriverManagementScreenState extends State<DriverManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final DriverService _driverService = getIt<DriverService>();
  
  List<DriverModel> _allDrivers = [];
  List<DriverModel> _unassignedDrivers = [];
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final allDriversResponse = await _driverService.getAllDrivers();
      final unassignedDriversResponse = await _driverService.getUnassignedDrivers();
      
      setState(() {
        _allDrivers = allDriversResponse.content;
        _unassignedDrivers = unassignedDriversResponse.content;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Driver Management'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'All Drivers', icon: Icon(Icons.people)),
            Tab(text: 'Unassigned', icon: Icon(Icons.person_off)),
            Tab(text: 'Verification', icon: Icon(Icons.verified_user)),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
            tooltip: 'Refresh',
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _showAddDriverDialog,
            tooltip: 'Add Driver',
          ),
        ],
      ),
      body: _isLoading
          ? const LoadingWidget()
          : _error != null
              ? ErrorDisplayWidget(
                  message: _error!,
                  onRetry: _loadData,
                )
              : TabBarView(
                  controller: _tabController,
                  children: [
                    _buildAllDriversTab(),
                    _buildUnassignedDriversTab(),
                    _buildVerificationTab(),
                  ],
                ),
    );
  }

  Widget _buildAllDriversTab() {
    if (_allDrivers.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.people_outline, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No drivers found',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            SizedBox(height: 8),
            Text(
              'Add drivers to get started',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _allDrivers.length,
      itemBuilder: (context, index) {
        final driver = _allDrivers[index];
        return _buildDriverCard(driver);
      },
    );
  }

  Widget _buildUnassignedDriversTab() {
    if (_unassignedDrivers.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.check_circle_outline, size: 64, color: Colors.green),
            SizedBox(height: 16),
            Text(
              'All drivers are assigned',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            SizedBox(height: 8),
            Text(
              'Great! All drivers have been assigned to companies',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _unassignedDrivers.length,
      itemBuilder: (context, index) {
        final driver = _unassignedDrivers[index];
        return _buildDriverCard(driver, showAssignButton: true);
      },
    );
  }

  Widget _buildVerificationTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.verified_user, size: 64, color: Colors.blue),
          SizedBox(height: 16),
          Text(
            'Driver Verification',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Text(
            'Verification management coming soon',
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildDriverCard(DriverModel driver, {bool showAssignButton = false}) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getVerificationColor(driver.verificationStatus?.overallStatus),
          child: Text(
            driver.firstName?.substring(0, 1).toUpperCase() ?? 'D',
            style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),
        ),
        title: Text(
          driver.fullName,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(driver.email ?? ''),
            if (driver.phoneNumber != null) Text(driver.phoneNumber!),
            if (driver.company != null)
              Text(
                'Company: ${driver.company!.name}',
                style: const TextStyle(color: Colors.blue),
              ),
            _buildVerificationStatusChip(driver.verificationStatus?.overallStatus),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (showAssignButton)
              ElevatedButton.icon(
                onPressed: () => _showAssignToCompanyDialog(driver),
                icon: const Icon(Icons.business, size: 16),
                label: const Text('Assign'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                ),
              ),
            IconButton(
              icon: const Icon(Icons.more_vert),
              onPressed: () => _showDriverOptions(driver),
            ),
          ],
        ),
        onTap: () => _showDriverDetails(driver),
      ),
    );
  }

  Widget _buildVerificationStatusChip(OverallVerificationStatus? status) {
    if (status == null) return const SizedBox.shrink();

    Color color;
    String label;
    IconData icon;

    switch (status) {
      case OverallVerificationStatus.fullyVerified:
        color = Colors.green;
        label = 'Verified';
        icon = Icons.verified;
        break;
      case OverallVerificationStatus.partiallyVerified:
        color = Colors.orange;
        label = 'Partial';
        icon = Icons.pending;
        break;
      case OverallVerificationStatus.verificationExpired:
        color = Colors.red;
        label = 'Expired';
        icon = Icons.error;
        break;
      default:
        color = Colors.grey;
        label = 'Unverified';
        icon = Icons.help_outline;
    }

    return Padding(
      padding: const EdgeInsets.only(top: 4),
      child: Chip(
        avatar: Icon(icon, size: 16, color: Colors.white),
        label: Text(label, style: const TextStyle(color: Colors.white, fontSize: 12)),
        backgroundColor: color,
        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
      ),
    );
  }

  Color _getVerificationColor(OverallVerificationStatus? status) {
    switch (status) {
      case OverallVerificationStatus.fullyVerified:
        return Colors.green;
      case OverallVerificationStatus.partiallyVerified:
        return Colors.orange;
      case OverallVerificationStatus.verificationExpired:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  void _showAddDriverDialog() {
    // TODO: Implement add driver dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Add driver functionality coming soon')),
    );
  }

  void _showAssignToCompanyDialog(DriverModel driver) {
    // TODO: Implement assign to company dialog
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Assign ${driver.fullName} to company')),
    );
  }

  void _showDriverOptions(DriverModel driver) {
    // TODO: Implement driver options menu
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Options for ${driver.fullName}')),
    );
  }

  void _showDriverDetails(DriverModel driver) {
    // TODO: Navigate to driver details screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('View details for ${driver.fullName}')),
    );
  }
}
