import 'package:flutter/material.dart';
import '../../models/fleet_dashboard_model.dart';

class ComplianceAlertsCard extends StatelessWidget {
  final List<ComplianceAlert> alerts;

  const ComplianceAlertsCard({
    Key? key,
    required this.alerts,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (alerts.isEmpty) {
      return Card(
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Icon(
                Icons.check_circle,
                color: Colors.green,
                size: 24,
              ),
              const SizedBox(width: 12),
              const Expanded(
                child: Text(
                  'All compliance documents are up to date',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.warning,
                  color: _getHighestSeverityColor(),
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Compliance Alerts (${alerts.length})',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    // Navigate to full compliance screen
                  },
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...alerts.take(3).map((alert) => _buildAlertItem(alert)),
            if (alerts.length > 3) ...[
              const SizedBox(height: 8),
              Center(
                child: TextButton(
                  onPressed: () {
                    // Navigate to full compliance screen
                  },
                  child: Text('View ${alerts.length - 3} more alerts'),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAlertItem(ComplianceAlert alert) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: _getSeverityColor(alert.severity).withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _getSeverityColor(alert.severity).withOpacity(0.3),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: _getSeverityColor(alert.severity),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Icon(
              _getAlertIcon(alert.alertType),
              color: Colors.white,
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        alert.title,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: _getSeverityColor(alert.severity),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        alert.severity,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  alert.resourceName,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  alert.message,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[700],
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(
                      Icons.schedule,
                      size: 14,
                      color: Colors.grey[600],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _formatDaysToExpiry(alert.daysToExpiry),
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      _formatExpiryDate(alert.expiryDate),
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getSeverityColor(String severity) {
    switch (severity) {
      case 'CRITICAL':
        return Colors.red;
      case 'HIGH':
        return Colors.deepOrange;
      case 'MEDIUM':
        return Colors.orange;
      case 'LOW':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  Color _getHighestSeverityColor() {
    if (alerts.any((alert) => alert.severity == 'CRITICAL')) {
      return Colors.red;
    } else if (alerts.any((alert) => alert.severity == 'HIGH')) {
      return Colors.deepOrange;
    } else if (alerts.any((alert) => alert.severity == 'MEDIUM')) {
      return Colors.orange;
    } else {
      return Colors.green;
    }
  }

  IconData _getAlertIcon(String alertType) {
    switch (alertType) {
      case 'INSURANCE_EXPIRY':
        return Icons.shield;
      case 'TAX_CLEARANCE_EXPIRY':
        return Icons.receipt;
      case 'PERMIT_EXPIRY':
        return Icons.description;
      case 'FITNESS_EXPIRY':
        return Icons.verified;
      default:
        return Icons.warning;
    }
  }

  String _formatDaysToExpiry(int days) {
    if (days <= 0) {
      return 'Expired';
    } else if (days == 1) {
      return 'Expires tomorrow';
    } else if (days <= 7) {
      return 'Expires in $days days';
    } else if (days <= 30) {
      return 'Expires in ${(days / 7).round()} weeks';
    } else {
      return 'Expires in ${(days / 30).round()} months';
    }
  }

  String _formatExpiryDate(DateTime date) {
    final now = DateTime.now();
    final difference = date.difference(now).inDays;
    
    if (difference <= 0) {
      return 'Expired on ${_formatDate(date)}';
    } else {
      return 'Expires ${_formatDate(date)}';
    }
  }

  String _formatDate(DateTime date) {
    final months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }
}
