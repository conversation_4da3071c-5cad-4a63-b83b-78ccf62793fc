package zw.co.kanjan.logipool.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import zw.co.kanjan.logipool.dto.fleet.TruckHeadDto;
import zw.co.kanjan.logipool.entity.TruckHead;
import zw.co.kanjan.logipool.service.TruckHeadService;

import java.security.Principal;
import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/api/fleet/truck-heads")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Truck Head Management", description = "APIs for managing truck heads in the fleet")
public class TruckHeadController {
    
    private final TruckHeadService truckHeadService;
    
    @PostMapping
    @PreAuthorize("hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Create truck head", description = "Add a new truck head to the fleet")
    public ResponseEntity<TruckHeadDto.TruckHeadResponse> createTruckHead(
            @Valid @RequestBody TruckHeadDto.TruckHeadCreateRequest request,
            Principal principal) {
        
        log.info("Creating truck head with registration: {} by user: {}", 
                request.getRegistrationNumber(), principal.getName());
        
        TruckHeadDto.TruckHeadResponse response = truckHeadService.createTruckHead(request, principal.getName());
        return ResponseEntity.ok(response);
    }
    
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Update truck head", description = "Update truck head information")
    public ResponseEntity<TruckHeadDto.TruckHeadResponse> updateTruckHead(
            @Parameter(description = "Truck head ID") @PathVariable Long id,
            @Valid @RequestBody TruckHeadDto.TruckHeadUpdateRequest request,
            Principal principal) {
        
        log.info("Updating truck head: {} by user: {}", id, principal.getName());
        
        TruckHeadDto.TruckHeadResponse response = truckHeadService.updateTruckHead(id, request, principal.getName());
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Get truck head", description = "Get truck head details by ID")
    public ResponseEntity<TruckHeadDto.TruckHeadResponse> getTruckHead(
            @Parameter(description = "Truck head ID") @PathVariable Long id,
            Principal principal) {
        
        TruckHeadDto.TruckHeadResponse response = truckHeadService.getTruckHead(id, principal.getName());
        return ResponseEntity.ok(response);
    }
    
    @GetMapping
    @PreAuthorize("hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Get truck heads", description = "Get paginated list of truck heads with optional filters")
    public ResponseEntity<Page<TruckHeadDto.TruckHeadResponse>> getTruckHeads(
            @PageableDefault(size = 20, sort = "createdAt") Pageable pageable,
            @Parameter(description = "Filter by status") @RequestParam(required = false) TruckHead.TruckStatus status,
            @Parameter(description = "Filter by make") @RequestParam(required = false) String make,
            @Parameter(description = "Filter by model") @RequestParam(required = false) String model,
            @Parameter(description = "Filter by registration number") @RequestParam(required = false) String registrationNumber,
            Principal principal) {
        
        Page<TruckHeadDto.TruckHeadResponse> response = truckHeadService.getTruckHeads(
                principal.getName(), pageable, status, make, model, registrationNumber);
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/available")
    @PreAuthorize("hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Get available truck heads", description = "Get list of available truck heads for assignment")
    public ResponseEntity<List<TruckHeadDto.TruckHeadResponse>> getAvailableTruckHeads(Principal principal) {
        
        List<TruckHeadDto.TruckHeadResponse> response = truckHeadService.getAvailableTruckHeads(principal.getName());
        return ResponseEntity.ok(response);
    }
    
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Delete truck head", description = "Remove truck head from fleet")
    public ResponseEntity<Void> deleteTruckHead(
            @Parameter(description = "Truck head ID") @PathVariable Long id,
            Principal principal) {
        
        log.info("Deleting truck head: {} by user: {}", id, principal.getName());
        
        truckHeadService.deleteTruckHead(id, principal.getName());
        return ResponseEntity.noContent().build();
    }
    
    @PatchMapping("/{id}/status")
    @PreAuthorize("hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Update truck head status", description = "Update the status of a truck head")
    public ResponseEntity<TruckHeadDto.TruckHeadResponse> updateTruckHeadStatus(
            @Parameter(description = "Truck head ID") @PathVariable Long id,
            @Parameter(description = "New status") @RequestParam TruckHead.TruckStatus status,
            Principal principal) {
        
        log.info("Updating truck head status: {} to {} by user: {}", id, status, principal.getName());
        
        TruckHeadDto.TruckHeadResponse response = truckHeadService.updateTruckHeadStatus(id, status, principal.getName());
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/expiring")
    @PreAuthorize("hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Get expiring documents", description = "Get truck heads with documents expiring in date range")
    public ResponseEntity<List<TruckHeadDto.TruckHeadResponse>> getTruckHeadsExpiring(
            @Parameter(description = "Start date") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @Parameter(description = "End date") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate,
            @Parameter(description = "Document type (insurance, fitness, permit, tax)") @RequestParam String type,
            Principal principal) {
        
        List<TruckHeadDto.TruckHeadResponse> response = truckHeadService.getTruckHeadsExpiringBetween(startDate, endDate, type);
        return ResponseEntity.ok(response);
    }
}
