import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/fleet_dashboard_model.dart';
import '../utils/constants.dart';
import 'auth_service.dart';

class FleetService {
  final AuthService _authService = AuthService();

  Future<Map<String, String>> _getHeaders() async {
    final token = await _authService.getToken();
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token',
    };
  }

  // Fleet Dashboard APIs
  Future<FleetOverview> getFleetOverview() async {
    final headers = await _getHeaders();
    final response = await http.get(
      Uri.parse('${Constants.baseUrl}/api/fleet/dashboard/overview'),
      headers: headers,
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return FleetOverview.fromJson(data);
    } else {
      throw Exception('Failed to load fleet overview: ${response.body}');
    }
  }

  Future<List<ComplianceAlert>> getComplianceAlerts() async {
    final headers = await _getHeaders();
    final response = await http.get(
      Uri.parse('${Constants.baseUrl}/api/fleet/dashboard/compliance-alerts'),
      headers: headers,
    );

    if (response.statusCode == 200) {
      final List<dynamic> data = json.decode(response.body);
      return data.map((alert) => ComplianceAlert.fromJson(alert)).toList();
    } else {
      throw Exception('Failed to load compliance alerts: ${response.body}');
    }
  }

  // Truck Head APIs
  Future<List<TruckHead>> getTruckHeads({
    int page = 0,
    int size = 20,
    String? status,
    String? make,
    String? model,
    String? registrationNumber,
  }) async {
    final headers = await _getHeaders();
    
    final queryParams = <String, String>{
      'page': page.toString(),
      'size': size.toString(),
    };
    
    if (status != null) queryParams['status'] = status;
    if (make != null) queryParams['make'] = make;
    if (model != null) queryParams['model'] = model;
    if (registrationNumber != null) queryParams['registrationNumber'] = registrationNumber;
    
    final uri = Uri.parse('${Constants.baseUrl}/api/fleet/truck-heads')
        .replace(queryParameters: queryParams);
    
    final response = await http.get(uri, headers: headers);

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      final List<dynamic> content = data['content'] ?? [];
      return content.map((truck) => TruckHead.fromJson(truck)).toList();
    } else {
      throw Exception('Failed to load truck heads: ${response.body}');
    }
  }

  Future<List<TruckHead>> getAvailableTruckHeads() async {
    final headers = await _getHeaders();
    final response = await http.get(
      Uri.parse('${Constants.baseUrl}/api/fleet/truck-heads/available'),
      headers: headers,
    );

    if (response.statusCode == 200) {
      final List<dynamic> data = json.decode(response.body);
      return data.map((truck) => TruckHead.fromJson(truck)).toList();
    } else {
      throw Exception('Failed to load available truck heads: ${response.body}');
    }
  }

  Future<TruckHead> getTruckHead(int id) async {
    final headers = await _getHeaders();
    final response = await http.get(
      Uri.parse('${Constants.baseUrl}/api/fleet/truck-heads/$id'),
      headers: headers,
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return TruckHead.fromJson(data);
    } else {
      throw Exception('Failed to load truck head: ${response.body}');
    }
  }

  Future<TruckHead> createTruckHead(Map<String, dynamic> truckHeadData) async {
    final headers = await _getHeaders();
    final response = await http.post(
      Uri.parse('${Constants.baseUrl}/api/fleet/truck-heads'),
      headers: headers,
      body: json.encode(truckHeadData),
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return TruckHead.fromJson(data);
    } else {
      throw Exception('Failed to create truck head: ${response.body}');
    }
  }

  Future<TruckHead> updateTruckHead(int id, Map<String, dynamic> truckHeadData) async {
    final headers = await _getHeaders();
    final response = await http.put(
      Uri.parse('${Constants.baseUrl}/api/fleet/truck-heads/$id'),
      headers: headers,
      body: json.encode(truckHeadData),
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return TruckHead.fromJson(data);
    } else {
      throw Exception('Failed to update truck head: ${response.body}');
    }
  }

  Future<void> deleteTruckHead(int id) async {
    final headers = await _getHeaders();
    final response = await http.delete(
      Uri.parse('${Constants.baseUrl}/api/fleet/truck-heads/$id'),
      headers: headers,
    );

    if (response.statusCode != 204) {
      throw Exception('Failed to delete truck head: ${response.body}');
    }
  }

  Future<TruckHead> updateTruckHeadStatus(int id, String status) async {
    final headers = await _getHeaders();
    final response = await http.patch(
      Uri.parse('${Constants.baseUrl}/api/fleet/truck-heads/$id/status?status=$status'),
      headers: headers,
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return TruckHead.fromJson(data);
    } else {
      throw Exception('Failed to update truck head status: ${response.body}');
    }
  }

  // Fleet Assignment APIs
  Future<List<FleetAssignment>> getFleetAssignments({
    int page = 0,
    int size = 20,
    String? status,
  }) async {
    final headers = await _getHeaders();
    
    final queryParams = <String, String>{
      'page': page.toString(),
      'size': size.toString(),
    };
    
    if (status != null) queryParams['status'] = status;
    
    final uri = Uri.parse('${Constants.baseUrl}/api/fleet/assignments')
        .replace(queryParameters: queryParams);
    
    final response = await http.get(uri, headers: headers);

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      final List<dynamic> content = data['content'] ?? [];
      return content.map((assignment) => FleetAssignment.fromJson(assignment)).toList();
    } else {
      throw Exception('Failed to load fleet assignments: ${response.body}');
    }
  }

  Future<FleetAssignment> getFleetAssignment(int id) async {
    final headers = await _getHeaders();
    final response = await http.get(
      Uri.parse('${Constants.baseUrl}/api/fleet/assignments/$id'),
      headers: headers,
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return FleetAssignment.fromJson(data);
    } else {
      throw Exception('Failed to load fleet assignment: ${response.body}');
    }
  }

  Future<FleetAssignment?> getFleetAssignmentByLoad(int loadId) async {
    final headers = await _getHeaders();
    final response = await http.get(
      Uri.parse('${Constants.baseUrl}/api/fleet/assignments/by-load/$loadId'),
      headers: headers,
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return FleetAssignment.fromJson(data);
    } else if (response.statusCode == 404) {
      return null;
    } else {
      throw Exception('Failed to load fleet assignment by load: ${response.body}');
    }
  }

  Future<FleetAssignment> createFleetAssignment(Map<String, dynamic> assignmentData) async {
    final headers = await _getHeaders();
    final response = await http.post(
      Uri.parse('${Constants.baseUrl}/api/fleet/assignments'),
      headers: headers,
      body: json.encode(assignmentData),
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return FleetAssignment.fromJson(data);
    } else {
      throw Exception('Failed to create fleet assignment: ${response.body}');
    }
  }

  Future<FleetAssignment> updateFleetAssignment(int id, Map<String, dynamic> assignmentData) async {
    final headers = await _getHeaders();
    final response = await http.put(
      Uri.parse('${Constants.baseUrl}/api/fleet/assignments/$id'),
      headers: headers,
      body: json.encode(assignmentData),
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return FleetAssignment.fromJson(data);
    } else {
      throw Exception('Failed to update fleet assignment: ${response.body}');
    }
  }

  Future<void> deleteFleetAssignment(int id) async {
    final headers = await _getHeaders();
    final response = await http.delete(
      Uri.parse('${Constants.baseUrl}/api/fleet/assignments/$id'),
      headers: headers,
    );

    if (response.statusCode != 204) {
      throw Exception('Failed to delete fleet assignment: ${response.body}');
    }
  }

  Future<Map<String, dynamic>> checkFleetAvailability(
    String resourceType,
    int resourceId,
    DateTime startTime,
    DateTime endTime,
    {int? excludeAssignmentId}
  ) async {
    final headers = await _getHeaders();
    
    final requestData = {
      'startTime': startTime.toIso8601String(),
      'endTime': endTime.toIso8601String(),
      if (excludeAssignmentId != null) 'excludeAssignmentId': excludeAssignmentId,
    };
    
    final response = await http.post(
      Uri.parse('${Constants.baseUrl}/api/fleet/assignments/check-availability/$resourceType/$resourceId'),
      headers: headers,
      body: json.encode(requestData),
    );

    if (response.statusCode == 200) {
      return json.decode(response.body);
    } else {
      throw Exception('Failed to check fleet availability: ${response.body}');
    }
  }
}
