import 'package:flutter/foundation.dart';
import '../models/fleet_dashboard_model.dart';
import '../services/fleet_service.dart';

class FleetProvider with ChangeNotifier {
  final FleetService _fleetService = FleetService();

  FleetOverview? _fleetOverview;
  List<ComplianceAlert>? _complianceAlerts;
  List<TruckHead> _truckHeads = [];
  List<FleetAssignment> _fleetAssignments = [];
  
  bool _isLoading = false;
  String? _error;

  // Getters
  FleetOverview? get fleetOverview => _fleetOverview;
  List<ComplianceAlert>? get complianceAlerts => _complianceAlerts;
  List<TruckHead> get truckHeads => _truckHeads;
  List<FleetAssignment> get fleetAssignments => _fleetAssignments;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Load fleet overview data
  Future<void> loadFleetOverview() async {
    try {
      _setLoading(true);
      _fleetOverview = await _fleetService.getFleetOverview();
      _clearError();
    } catch (e) {
      _setError(e.toString());
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Load compliance alerts
  Future<void> loadComplianceAlerts() async {
    try {
      _complianceAlerts = await _fleetService.getComplianceAlerts();
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
      rethrow;
    }
  }

  // Load truck heads
  Future<void> loadTruckHeads({
    int page = 0,
    int size = 20,
    String? status,
    String? make,
    String? model,
    String? registrationNumber,
  }) async {
    try {
      _setLoading(true);
      final result = await _fleetService.getTruckHeads(
        page: page,
        size: size,
        status: status,
        make: make,
        model: model,
        registrationNumber: registrationNumber,
      );
      
      if (page == 0) {
        _truckHeads = result;
      } else {
        _truckHeads.addAll(result);
      }
      
      _clearError();
    } catch (e) {
      _setError(e.toString());
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Load available truck heads
  Future<List<TruckHead>> loadAvailableTruckHeads() async {
    try {
      return await _fleetService.getAvailableTruckHeads();
    } catch (e) {
      _setError(e.toString());
      rethrow;
    }
  }

  // Create truck head
  Future<TruckHead> createTruckHead(Map<String, dynamic> truckHeadData) async {
    try {
      _setLoading(true);
      final truckHead = await _fleetService.createTruckHead(truckHeadData);
      _truckHeads.insert(0, truckHead);
      _clearError();
      return truckHead;
    } catch (e) {
      _setError(e.toString());
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Update truck head
  Future<TruckHead> updateTruckHead(int id, Map<String, dynamic> truckHeadData) async {
    try {
      _setLoading(true);
      final updatedTruckHead = await _fleetService.updateTruckHead(id, truckHeadData);
      
      final index = _truckHeads.indexWhere((truck) => truck.id == id);
      if (index != -1) {
        _truckHeads[index] = updatedTruckHead;
      }
      
      _clearError();
      return updatedTruckHead;
    } catch (e) {
      _setError(e.toString());
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Delete truck head
  Future<void> deleteTruckHead(int id) async {
    try {
      _setLoading(true);
      await _fleetService.deleteTruckHead(id);
      _truckHeads.removeWhere((truck) => truck.id == id);
      _clearError();
    } catch (e) {
      _setError(e.toString());
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Update truck head status
  Future<TruckHead> updateTruckHeadStatus(int id, String status) async {
    try {
      final updatedTruckHead = await _fleetService.updateTruckHeadStatus(id, status);
      
      final index = _truckHeads.indexWhere((truck) => truck.id == id);
      if (index != -1) {
        _truckHeads[index] = updatedTruckHead;
        notifyListeners();
      }
      
      return updatedTruckHead;
    } catch (e) {
      _setError(e.toString());
      rethrow;
    }
  }

  // Load fleet assignments
  Future<void> loadFleetAssignments({
    int page = 0,
    int size = 20,
    String? status,
  }) async {
    try {
      _setLoading(true);
      final result = await _fleetService.getFleetAssignments(
        page: page,
        size: size,
        status: status,
      );
      
      if (page == 0) {
        _fleetAssignments = result;
      } else {
        _fleetAssignments.addAll(result);
      }
      
      _clearError();
    } catch (e) {
      _setError(e.toString());
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Create fleet assignment
  Future<FleetAssignment> createFleetAssignment(Map<String, dynamic> assignmentData) async {
    try {
      _setLoading(true);
      final assignment = await _fleetService.createFleetAssignment(assignmentData);
      _fleetAssignments.insert(0, assignment);
      _clearError();
      return assignment;
    } catch (e) {
      _setError(e.toString());
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Update fleet assignment
  Future<FleetAssignment> updateFleetAssignment(int id, Map<String, dynamic> assignmentData) async {
    try {
      _setLoading(true);
      final updatedAssignment = await _fleetService.updateFleetAssignment(id, assignmentData);
      
      final index = _fleetAssignments.indexWhere((assignment) => assignment.id == id);
      if (index != -1) {
        _fleetAssignments[index] = updatedAssignment;
      }
      
      _clearError();
      return updatedAssignment;
    } catch (e) {
      _setError(e.toString());
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Delete fleet assignment
  Future<void> deleteFleetAssignment(int id) async {
    try {
      _setLoading(true);
      await _fleetService.deleteFleetAssignment(id);
      _fleetAssignments.removeWhere((assignment) => assignment.id == id);
      _clearError();
    } catch (e) {
      _setError(e.toString());
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Get fleet assignment by load
  Future<FleetAssignment?> getFleetAssignmentByLoad(int loadId) async {
    try {
      return await _fleetService.getFleetAssignmentByLoad(loadId);
    } catch (e) {
      _setError(e.toString());
      return null;
    }
  }

  // Check fleet availability
  Future<Map<String, dynamic>> checkFleetAvailability(
    String resourceType,
    int resourceId,
    DateTime startTime,
    DateTime endTime,
    {int? excludeAssignmentId}
  ) async {
    try {
      return await _fleetService.checkFleetAvailability(
        resourceType,
        resourceId,
        startTime,
        endTime,
        excludeAssignmentId: excludeAssignmentId,
      );
    } catch (e) {
      _setError(e.toString());
      rethrow;
    }
  }

  // Refresh all fleet data
  Future<void> refreshFleetData() async {
    await Future.wait([
      loadFleetOverview(),
      loadComplianceAlerts(),
      loadTruckHeads(),
      loadFleetAssignments(),
    ]);
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  // Clear all data
  void clearData() {
    _fleetOverview = null;
    _complianceAlerts = null;
    _truckHeads.clear();
    _fleetAssignments.clear();
    _error = null;
    _isLoading = false;
    notifyListeners();
  }
}
