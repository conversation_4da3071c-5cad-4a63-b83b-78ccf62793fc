package zw.co.kanjan.logipool.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import zw.co.kanjan.logipool.dto.fleet.FleetAssignmentDto;
import zw.co.kanjan.logipool.entity.*;
import zw.co.kanjan.logipool.exception.BusinessException;
import zw.co.kanjan.logipool.exception.ResourceNotFoundException;
import zw.co.kanjan.logipool.mapper.fleet.FleetAssignmentMapper;
import zw.co.kanjan.logipool.repository.*;

import java.time.LocalDateTime;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class FleetManagementService {
    
    private final FleetAssignmentRepository fleetAssignmentRepository;
    private final LoadRepository loadRepository;
    private final BidRepository bidRepository;
    private final TruckHeadRepository truckHeadRepository;
    private final TrailerRepository trailerRepository;
    private final UserRepository userRepository;
    private final CompanyRepository companyRepository;
    private final FleetAssignmentMapper fleetAssignmentMapper;
    
    public FleetAssignmentDto.FleetAssignmentResponse createFleetAssignment(
            FleetAssignmentDto.FleetAssignmentCreateRequest request, String username) {
        
        User user = getUserByUsername(username);
        Company company = getCompanyByUser(user);
        
        // Validate load exists and is assigned to the company
        Load load = loadRepository.findById(request.getLoadId())
                .orElseThrow(() -> new ResourceNotFoundException("Load not found with id: " + request.getLoadId()));
        
        if (load.getAssignedCompany() == null || !load.getAssignedCompany().getId().equals(company.getId())) {
            throw new BusinessException("Load must be assigned to your company before creating fleet assignment");
        }
        
        // Validate bid if provided
        Bid bid = null;
        if (request.getBidId() != null) {
            bid = bidRepository.findById(request.getBidId())
                    .orElseThrow(() -> new ResourceNotFoundException("Bid not found with id: " + request.getBidId()));
            
            if (!bid.getCompany().getId().equals(company.getId())) {
                throw new BusinessException("Bid must belong to your company");
            }
        }
        
        // Validate fleet resources availability
        validateFleetAvailability(request, company);
        
        // Create fleet assignment
        FleetAssignment assignment = fleetAssignmentMapper.toEntity(request);
        assignment.setLoad(load);
        assignment.setBid(bid);
        assignment.setCompany(company);
        assignment.setAssignedBy(user);
        assignment.setAssignedAt(LocalDateTime.now());
        assignment.setStatus(FleetAssignment.AssignmentStatus.PLANNED);
        
        // Set fleet resources
        setFleetResources(assignment, request, company);
        
        FleetAssignment savedAssignment = fleetAssignmentRepository.save(assignment);
        
        // Update fleet resource statuses
        updateFleetResourceStatuses(savedAssignment, true);
        
        log.info("Fleet assignment created successfully for load: {} by company: {}", 
                load.getTrackingNumber(), company.getName());
        
        return fleetAssignmentMapper.toResponse(savedAssignment);
    }
    
    public FleetAssignmentDto.FleetAssignmentResponse updateFleetAssignment(
            Long id, FleetAssignmentDto.FleetAssignmentUpdateRequest request, String username) {
        
        User user = getUserByUsername(username);
        Company company = getCompanyByUser(user);
        
        FleetAssignment assignment = fleetAssignmentRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Fleet assignment not found with id: " + id));
        
        // Validate ownership
        if (!assignment.getCompany().getId().equals(company.getId())) {
            throw new BusinessException("You can only update your own fleet assignments");
        }
        
        // Store old resources for status updates
        TruckHead oldTruckHead = assignment.getTruckHead();
        Trailer oldTrailer1 = assignment.getTrailer1();
        Trailer oldTrailer2 = assignment.getTrailer2();
        
        // Update assignment
        updateFleetAssignmentFromRequest(assignment, request, company);
        
        FleetAssignment savedAssignment = fleetAssignmentRepository.save(assignment);
        
        // Update fleet resource statuses
        updateFleetResourceStatusesOnUpdate(savedAssignment, oldTruckHead, oldTrailer1, oldTrailer2);
        
        log.info("Fleet assignment updated successfully: {}", savedAssignment.getId());
        
        return fleetAssignmentMapper.toResponse(savedAssignment);
    }
    
    @Transactional(readOnly = true)
    public FleetAssignmentDto.FleetAssignmentResponse getFleetAssignment(Long id, String username) {
        User user = getUserByUsername(username);
        Company company = getCompanyByUser(user);
        
        FleetAssignment assignment = fleetAssignmentRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Fleet assignment not found with id: " + id));
        
        // Validate ownership
        if (!assignment.getCompany().getId().equals(company.getId())) {
            throw new BusinessException("You can only view your own fleet assignments");
        }
        
        return fleetAssignmentMapper.toResponse(assignment);
    }
    
    @Transactional(readOnly = true)
    public Page<FleetAssignmentDto.FleetAssignmentResponse> getFleetAssignments(
            String username, Pageable pageable, FleetAssignment.AssignmentStatus status) {
        
        User user = getUserByUsername(username);
        Company company = getCompanyByUser(user);
        
        Page<FleetAssignment> assignments;
        if (status != null) {
            assignments = fleetAssignmentRepository.findByCompanyAndStatus(company, status, pageable);
        } else {
            assignments = fleetAssignmentRepository.findByCompany(company, pageable);
        }
        
        return assignments.map(fleetAssignmentMapper::toResponse);
    }
    
    @Transactional(readOnly = true)
    public FleetAssignmentDto.FleetAssignmentResponse getFleetAssignmentByLoad(Long loadId, String username) {
        User user = getUserByUsername(username);
        Company company = getCompanyByUser(user);
        
        Load load = loadRepository.findById(loadId)
                .orElseThrow(() -> new ResourceNotFoundException("Load not found with id: " + loadId));
        
        FleetAssignment assignment = fleetAssignmentRepository.findByLoadAndStatus(load, FleetAssignment.AssignmentStatus.ACTIVE)
                .or(() -> fleetAssignmentRepository.findByLoadAndStatus(load, FleetAssignment.AssignmentStatus.PLANNED))
                .or(() -> fleetAssignmentRepository.findByLoadAndStatus(load, FleetAssignment.AssignmentStatus.IN_PROGRESS))
                .orElse(null);
        
        if (assignment == null) {
            return null;
        }
        
        // Validate ownership or client access
        if (!assignment.getCompany().getId().equals(company.getId()) && 
            !load.getClient().getId().equals(user.getId())) {
            throw new BusinessException("Access denied to fleet assignment");
        }
        
        return fleetAssignmentMapper.toResponse(assignment);
    }
    
    public FleetAssignmentDto.FleetAvailabilityResponse checkFleetAvailability(
            Long resourceId, String resourceType, FleetAssignmentDto.FleetAvailabilityRequest request, String username) {
        
        User user = getUserByUsername(username);
        Company company = getCompanyByUser(user);
        
        List<FleetAssignment> conflicts;
        String resourceName = "";
        
        switch (resourceType.toLowerCase()) {
            case "truck":
                TruckHead truckHead = truckHeadRepository.findById(resourceId)
                        .orElseThrow(() -> new ResourceNotFoundException("Truck head not found"));
                if (!truckHead.getCompany().getId().equals(company.getId())) {
                    throw new BusinessException("Truck head does not belong to your company");
                }
                conflicts = fleetAssignmentRepository.findConflictingTruckHeadAssignments(
                        truckHead, request.getStartTime(), request.getEndTime());
                resourceName = truckHead.getRegistrationNumber();
                break;
                
            case "trailer":
                Trailer trailer = trailerRepository.findById(resourceId)
                        .orElseThrow(() -> new ResourceNotFoundException("Trailer not found"));
                if (!trailer.getCompany().getId().equals(company.getId())) {
                    throw new BusinessException("Trailer does not belong to your company");
                }
                conflicts = fleetAssignmentRepository.findConflictingTrailer1Assignments(
                        trailer, request.getStartTime(), request.getEndTime());
                conflicts.addAll(fleetAssignmentRepository.findConflictingTrailer2Assignments(
                        trailer, request.getStartTime(), request.getEndTime()));
                resourceName = trailer.getRegistrationNumber();
                break;
                
            case "driver":
                User driver = userRepository.findById(resourceId)
                        .orElseThrow(() -> new ResourceNotFoundException("Driver not found"));
                conflicts = fleetAssignmentRepository.findConflictingDriverAssignments(
                        driver, request.getStartTime(), request.getEndTime());
                resourceName = driver.getFirstName() + " " + driver.getLastName();
                break;
                
            default:
                throw new BusinessException("Invalid resource type: " + resourceType);
        }
        
        // Filter out excluded assignment if provided
        if (request.getExcludeAssignmentId() != null) {
            conflicts = conflicts.stream()
                    .filter(assignment -> !assignment.getId().equals(request.getExcludeAssignmentId()))
                    .toList();
        }
        
        if (conflicts.isEmpty()) {
            return FleetAssignmentDto.FleetAvailabilityResponse.builder()
                    .isAvailable(true)
                    .build();
        } else {
            FleetAssignment conflict = conflicts.get(0);
            return FleetAssignmentDto.FleetAvailabilityResponse.builder()
                    .isAvailable(false)
                    .reason(resourceName + " is already assigned to another load during this time period")
                    .conflictStartTime(conflict.getScheduledStartTime())
                    .conflictEndTime(conflict.getScheduledEndTime())
                    .conflictingAssignmentId(conflict.getId())
                    .conflictingLoadTitle(conflict.getLoad().getTitle())
                    .build();
        }
    }
    
    public void deleteFleetAssignment(Long id, String username) {
        User user = getUserByUsername(username);
        Company company = getCompanyByUser(user);
        
        FleetAssignment assignment = fleetAssignmentRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Fleet assignment not found with id: " + id));
        
        // Validate ownership
        if (!assignment.getCompany().getId().equals(company.getId())) {
            throw new BusinessException("You can only delete your own fleet assignments");
        }
        
        // Check if assignment can be deleted
        if (assignment.getStatus() == FleetAssignment.AssignmentStatus.ACTIVE ||
            assignment.getStatus() == FleetAssignment.AssignmentStatus.IN_PROGRESS) {
            throw new BusinessException("Cannot delete active or in-progress fleet assignment");
        }
        
        // Update fleet resource statuses
        updateFleetResourceStatuses(assignment, false);
        
        fleetAssignmentRepository.delete(assignment);
        log.info("Fleet assignment deleted successfully: {}", assignment.getId());
    }
    
    private User getUserByUsername(String username) {
        return userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
    }
    
    private Company getCompanyByUser(User user) {
        if (user.getCompany() == null) {
            throw new BusinessException("User must be associated with a company");
        }
        return user.getCompany();
    }
    
    private void validateFleetAvailability(FleetAssignmentDto.FleetAssignmentCreateRequest request, Company company) {
        // Validate truck head availability
        if (request.getTruckHeadId() != null) {
            FleetAssignmentDto.FleetAvailabilityRequest availabilityRequest = 
                    FleetAssignmentDto.FleetAvailabilityRequest.builder()
                            .startTime(request.getScheduledStartTime())
                            .endTime(request.getScheduledEndTime())
                            .build();
            
            FleetAssignmentDto.FleetAvailabilityResponse availability = 
                    checkFleetAvailability(request.getTruckHeadId(), "truck", availabilityRequest, company.getUser().getUsername());
            
            if (!availability.isAvailable()) {
                throw new BusinessException("Truck head is not available: " + availability.getReason());
            }
        }
        
        // Similar validation for trailers and drivers...
    }
    
    private void setFleetResources(FleetAssignment assignment, FleetAssignmentDto.FleetAssignmentCreateRequest request, Company company) {
        if (request.getTruckHeadId() != null) {
            TruckHead truckHead = truckHeadRepository.findById(request.getTruckHeadId())
                    .orElseThrow(() -> new ResourceNotFoundException("Truck head not found"));
            if (!truckHead.getCompany().getId().equals(company.getId())) {
                throw new BusinessException("Truck head does not belong to your company");
            }
            assignment.setTruckHead(truckHead);
        }
        
        if (request.getTrailer1Id() != null) {
            Trailer trailer1 = trailerRepository.findById(request.getTrailer1Id())
                    .orElseThrow(() -> new ResourceNotFoundException("Trailer 1 not found"));
            if (!trailer1.getCompany().getId().equals(company.getId())) {
                throw new BusinessException("Trailer 1 does not belong to your company");
            }
            assignment.setTrailer1(trailer1);
        }
        
        if (request.getTrailer2Id() != null) {
            Trailer trailer2 = trailerRepository.findById(request.getTrailer2Id())
                    .orElseThrow(() -> new ResourceNotFoundException("Trailer 2 not found"));
            if (!trailer2.getCompany().getId().equals(company.getId())) {
                throw new BusinessException("Trailer 2 does not belong to your company");
            }
            assignment.setTrailer2(trailer2);
        }
        
        if (request.getPrimaryDriverId() != null) {
            User primaryDriver = userRepository.findById(request.getPrimaryDriverId())
                    .orElseThrow(() -> new ResourceNotFoundException("Primary driver not found"));
            assignment.setPrimaryDriver(primaryDriver);
        }
        
        if (request.getSecondaryDriverId() != null) {
            User secondaryDriver = userRepository.findById(request.getSecondaryDriverId())
                    .orElseThrow(() -> new ResourceNotFoundException("Secondary driver not found"));
            assignment.setSecondaryDriver(secondaryDriver);
        }
    }
    
    private void updateFleetResourceStatuses(FleetAssignment assignment, boolean assign) {
        if (assignment.getTruckHead() != null) {
            TruckHead.TruckStatus newStatus = assign ? TruckHead.TruckStatus.ASSIGNED : TruckHead.TruckStatus.AVAILABLE;
            assignment.getTruckHead().setStatus(newStatus);
            truckHeadRepository.save(assignment.getTruckHead());
        }
        
        if (assignment.getTrailer1() != null) {
            Trailer.TrailerStatus newStatus = assign ? Trailer.TrailerStatus.ASSIGNED : Trailer.TrailerStatus.AVAILABLE;
            assignment.getTrailer1().setStatus(newStatus);
            trailerRepository.save(assignment.getTrailer1());
        }
        
        if (assignment.getTrailer2() != null) {
            Trailer.TrailerStatus newStatus = assign ? Trailer.TrailerStatus.ASSIGNED : Trailer.TrailerStatus.AVAILABLE;
            assignment.getTrailer2().setStatus(newStatus);
            trailerRepository.save(assignment.getTrailer2());
        }
    }
    
    private void updateFleetAssignmentFromRequest(FleetAssignment assignment, 
                                                 FleetAssignmentDto.FleetAssignmentUpdateRequest request, Company company) {
        if (request.getStatus() != null) assignment.setStatus(request.getStatus());
        if (request.getScheduledStartTime() != null) assignment.setScheduledStartTime(request.getScheduledStartTime());
        if (request.getScheduledEndTime() != null) assignment.setScheduledEndTime(request.getScheduledEndTime());
        if (request.getActualStartTime() != null) assignment.setActualStartTime(request.getActualStartTime());
        if (request.getActualEndTime() != null) assignment.setActualEndTime(request.getActualEndTime());
        if (request.getNotes() != null) assignment.setNotes(request.getNotes());
        
        // Update fleet resources if changed
        if (request.getTruckHeadId() != null) {
            TruckHead truckHead = truckHeadRepository.findById(request.getTruckHeadId())
                    .orElseThrow(() -> new ResourceNotFoundException("Truck head not found"));
            if (!truckHead.getCompany().getId().equals(company.getId())) {
                throw new BusinessException("Truck head does not belong to your company");
            }
            assignment.setTruckHead(truckHead);
        }
        
        // Similar updates for trailers and drivers...
    }
    
    private void updateFleetResourceStatusesOnUpdate(FleetAssignment assignment, 
                                                    TruckHead oldTruckHead, Trailer oldTrailer1, Trailer oldTrailer2) {
        // Free up old resources
        if (oldTruckHead != null && (assignment.getTruckHead() == null || !oldTruckHead.getId().equals(assignment.getTruckHead().getId()))) {
            oldTruckHead.setStatus(TruckHead.TruckStatus.AVAILABLE);
            truckHeadRepository.save(oldTruckHead);
        }
        
        // Assign new resources
        updateFleetResourceStatuses(assignment, true);
    }
}
