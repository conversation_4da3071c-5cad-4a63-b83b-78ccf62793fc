import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/fleet_provider.dart';
import '../../models/fleet_dashboard_model.dart';
import '../../widgets/fleet/fleet_overview_card.dart';
import '../../widgets/fleet/compliance_alerts_card.dart';
import '../../widgets/fleet/fleet_utilization_chart.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/common/error_widget.dart';
import 'truck_heads_screen.dart';
import 'trailers_screen.dart';
import 'fleet_assignments_screen.dart';
import 'compliance_screen.dart';

class FleetDashboardScreen extends StatefulWidget {
  const FleetDashboardScreen({Key? key}) : super(key: key);

  @override
  State<FleetDashboardScreen> createState() => _FleetDashboardScreenState();
}

class _FleetDashboardScreenState extends State<FleetDashboardScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadFleetData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadFleetData() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final fleetProvider = Provider.of<FleetProvider>(context, listen: false);
      await fleetProvider.loadFleetOverview();
      await fleetProvider.loadComplianceAlerts();

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _error = e.toString();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Fleet Management'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadFleetData,
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'export':
                  _exportFleetData();
                  break;
                case 'settings':
                  _openFleetSettings();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'export',
                child: Row(
                  children: [
                    Icon(Icons.download),
                    SizedBox(width: 8),
                    Text('Export Data'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'settings',
                child: Row(
                  children: [
                    Icon(Icons.settings),
                    SizedBox(width: 8),
                    Text('Fleet Settings'),
                  ],
                ),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'Overview'),
            Tab(icon: Icon(Icons.local_shipping), text: 'Vehicles'),
            Tab(icon: Icon(Icons.assignment), text: 'Assignments'),
            Tab(icon: Icon(Icons.verified_user), text: 'Compliance'),
          ],
        ),
      ),
      body: _isLoading
          ? const LoadingWidget()
          : _error != null
              ? CustomErrorWidget(
                  message: _error!,
                  onRetry: _loadFleetData,
                )
              : TabBarView(
                  controller: _tabController,
                  children: [
                    _buildOverviewTab(),
                    _buildVehiclesTab(),
                    _buildAssignmentsTab(),
                    _buildComplianceTab(),
                  ],
                ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showQuickActions,
        icon: const Icon(Icons.add),
        label: const Text('Quick Add'),
        backgroundColor: Theme.of(context).primaryColor,
      ),
    );
  }

  Widget _buildOverviewTab() {
    return Consumer<FleetProvider>(
      builder: (context, fleetProvider, child) {
        final overview = fleetProvider.fleetOverview;
        final alerts = fleetProvider.complianceAlerts;

        if (overview == null) {
          return const Center(child: Text('No fleet data available'));
        }

        return RefreshIndicator(
          onRefresh: _loadFleetData,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                FleetOverviewCard(overview: overview),
                const SizedBox(height: 16),
                if (alerts != null && alerts.isNotEmpty) ...[
                  ComplianceAlertsCard(alerts: alerts),
                  const SizedBox(height: 16),
                ],
                _buildQuickStatsGrid(overview),
                const SizedBox(height: 16),
                _buildRecentActivityCard(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildVehiclesTab() {
    return DefaultTabController(
      length: 2,
      child: Column(
        children: [
          Container(
            color: Colors.grey[100],
            child: const TabBar(
              labelColor: Colors.black,
              indicatorColor: Colors.blue,
              tabs: [
                Tab(text: 'Truck Heads'),
                Tab(text: 'Trailers'),
              ],
            ),
          ),
          const Expanded(
            child: TabBarView(
              children: [
                TruckHeadsScreen(),
                TrailersScreen(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAssignmentsTab() {
    return const FleetAssignmentsScreen();
  }

  Widget _buildComplianceTab() {
    return const ComplianceScreen();
  }

  Widget _buildQuickStatsGrid(FleetOverview overview) {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      childAspectRatio: 1.5,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      children: [
        _buildStatCard(
          'Utilization Rate',
          '${overview.truckHeadUtilizationRate.toStringAsFixed(1)}%',
          Icons.trending_up,
          _getUtilizationColor(overview.truckHeadUtilizationRate),
        ),
        _buildStatCard(
          'Active Assignments',
          '${overview.activeAssignments}',
          Icons.assignment_turned_in,
          Colors.green,
        ),
        _buildStatCard(
          'Compliance Alerts',
          '${overview.totalComplianceAlerts}',
          Icons.warning,
          overview.totalComplianceAlerts > 0 ? Colors.red : Colors.green,
        ),
        _buildStatCard(
          'Available Vehicles',
          '${overview.availableTruckHeads + overview.availableTrailers}',
          Icons.check_circle,
          Colors.blue,
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentActivityCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Recent Activity',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            // This would be populated with actual recent activity data
            const Text('No recent activity'),
          ],
        ),
      ),
    );
  }

  Color _getUtilizationColor(double utilization) {
    if (utilization >= 80) return Colors.green;
    if (utilization >= 60) return Colors.orange;
    return Colors.red;
  }

  void _showQuickActions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Quick Actions',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.local_shipping),
              title: const Text('Add Truck Head'),
              onTap: () {
                Navigator.pop(context);
                // Navigate to add truck head screen
              },
            ),
            ListTile(
              leading: const Icon(Icons.trailer_truck),
              title: const Text('Add Trailer'),
              onTap: () {
                Navigator.pop(context);
                // Navigate to add trailer screen
              },
            ),
            ListTile(
              leading: const Icon(Icons.assignment),
              title: const Text('Create Assignment'),
              onTap: () {
                Navigator.pop(context);
                // Navigate to create assignment screen
              },
            ),
            ListTile(
              leading: const Icon(Icons.upload_file),
              title: const Text('Upload Document'),
              onTap: () {
                Navigator.pop(context);
                // Navigate to upload document screen
              },
            ),
          ],
        ),
      ),
    );
  }

  void _exportFleetData() {
    // Implement fleet data export functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Export functionality coming soon')),
    );
  }

  void _openFleetSettings() {
    // Navigate to fleet settings screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Fleet settings coming soon')),
    );
  }
}
