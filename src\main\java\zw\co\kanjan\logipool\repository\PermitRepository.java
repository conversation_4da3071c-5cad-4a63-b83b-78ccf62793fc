package zw.co.kanjan.logipool.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import zw.co.kanjan.logipool.entity.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface PermitRepository extends JpaRepository<Permit, Long> {
    
    List<Permit> findByCompany(Company company);
    
    Page<Permit> findByCompany(Company company, Pageable pageable);
    
    List<Permit> findByCompanyAndStatus(Company company, Permit.PermitStatus status);
    
    List<Permit> findByCompanyAndPermitType(Company company, Permit.PermitType permitType);
    
    Page<Permit> findByCompanyAndPermitType(Company company, Permit.PermitType permitType, Pageable pageable);
    
    Optional<Permit> findByPermitNumber(String permitNumber);
    
    List<Permit> findByVehicle(Vehicle vehicle);
    
    List<Permit> findByTruckHead(TruckHead truckHead);
    
    List<Permit> findByTrailer(Trailer trailer);
    
    @Query("SELECT p FROM Permit p WHERE p.company = :company AND " +
           "(:status IS NULL OR p.status = :status) AND " +
           "(:permitType IS NULL OR p.permitType = :permitType) AND " +
           "(:authority IS NULL OR LOWER(p.issuingAuthority) LIKE LOWER(CONCAT('%', :authority, '%'))) AND " +
           "(:permitNumber IS NULL OR LOWER(p.permitNumber) LIKE LOWER(CONCAT('%', :permitNumber, '%')))")
    Page<Permit> findByCompanyWithFilters(
            @Param("company") Company company,
            @Param("status") Permit.PermitStatus status,
            @Param("permitType") Permit.PermitType permitType,
            @Param("authority") String authority,
            @Param("permitNumber") String permitNumber,
            Pageable pageable);
    
    @Query("SELECT p FROM Permit p WHERE p.expiryDate BETWEEN :startDate AND :endDate")
    List<Permit> findByExpiryDateBetween(
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT p FROM Permit p WHERE p.company = :company AND " +
           "p.expiryDate BETWEEN :startDate AND :endDate")
    List<Permit> findByCompanyAndExpiryDateBetween(
            @Param("company") Company company,
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT p FROM Permit p WHERE p.status = 'ACTIVE' AND p.expiryDate <= :date")
    List<Permit> findExpiredPermits(@Param("date") LocalDateTime date);
    
    @Query("SELECT p FROM Permit p WHERE p.company = :company AND p.status = 'ACTIVE' AND p.expiryDate <= :date")
    List<Permit> findExpiredPermitsByCompany(@Param("company") Company company, @Param("date") LocalDateTime date);
    
    @Query("SELECT COUNT(p) FROM Permit p WHERE p.company = :company")
    long countByCompany(@Param("company") Company company);
    
    @Query("SELECT COUNT(p) FROM Permit p WHERE p.company = :company AND p.status = :status")
    long countByCompanyAndStatus(@Param("company") Company company, @Param("status") Permit.PermitStatus status);
    
    @Query("SELECT COUNT(p) FROM Permit p WHERE p.company = :company AND p.permitType = :permitType")
    long countByCompanyAndPermitType(@Param("company") Company company, @Param("permitType") Permit.PermitType permitType);
    
    boolean existsByPermitNumber(String permitNumber);
}
