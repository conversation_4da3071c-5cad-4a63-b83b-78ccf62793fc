package zw.co.kanjan.logipool.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import zw.co.kanjan.logipool.dto.load.LoadResponse;
import zw.co.kanjan.logipool.dto.tracking.TrackingVerificationDto;
import zw.co.kanjan.logipool.entity.Load;
import zw.co.kanjan.logipool.entity.TrackingVerification;
import zw.co.kanjan.logipool.exception.BusinessException;
import zw.co.kanjan.logipool.exception.ResourceNotFoundException;
import zw.co.kanjan.logipool.mapper.LoadMapper;
import zw.co.kanjan.logipool.security.CustomUserDetailsService;
import zw.co.kanjan.logipool.security.JwtUtils;
import zw.co.kanjan.logipool.security.RateLimitingFilter;
import zw.co.kanjan.logipool.service.GuestLoadInquiryService;
import zw.co.kanjan.logipool.service.PublicBrowsingService;
import zw.co.kanjan.logipool.service.TrackingVerificationService;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(controllers = PublicController.class)
@WithMockUser
class PublicControllerTrackingTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private PublicBrowsingService publicBrowsingService;

    @MockBean
    private GuestLoadInquiryService guestLoadInquiryService;

    @MockBean
    private TrackingVerificationService trackingVerificationService;

    @MockBean
    private LoadMapper loadMapper;

    @MockBean
    private JwtUtils jwtUtils;

    @MockBean
    private CustomUserDetailsService customUserDetailsService;



    private TrackingVerificationDto.VerificationRequest verificationRequest;
    private TrackingVerificationDto.CodeVerificationRequest codeVerificationRequest;
    private LoadResponse loadResponse;

    @BeforeEach
    void setUp() {
        verificationRequest = TrackingVerificationDto.VerificationRequest.builder()
                .trackingNumber("LP123456789")
                .contact("<EMAIL>")
                .verificationMethod("EMAIL")
                .build();

        codeVerificationRequest = TrackingVerificationDto.CodeVerificationRequest.builder()
                .trackingNumber("LP123456789")
                .verificationCode("123456")
                .build();

        loadResponse = LoadResponse.builder()
                .id(1L)
                .trackingNumber("LP123456789")
                .title("Test Shipment")
                .status(Load.LoadStatus.IN_TRANSIT)
                .pickupLocation("Harare")
                .deliveryLocation("Bulawayo")
                .build();
    }

    @Test
    void requestTrackingVerification_ValidEmailRequest_ShouldReturnSuccess() throws Exception {
        // Arrange
        String expectedToken = "test-token-123";
        when(trackingVerificationService.requestTrackingVerification(
                anyString(), anyString(),
                any(TrackingVerification.VerificationMethod.class),
                anyString(), anyString()))
                .thenReturn(expectedToken);

        // Act & Assert
        mockMvc.perform(post("/api/public/tracking/request-verification")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(verificationRequest))
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("Verification sent successfully"))
                .andExpect(jsonPath("$.token").value(expectedToken))
                .andExpect(jsonPath("$.requiresCode").value(false))
                .andExpect(jsonPath("$.expiryMinutes").value(1440))
                .andExpect(jsonPath("$.maskedContact").value("te***@example.com"));
    }

    @Test
    void requestTrackingVerification_ValidSmsRequest_ShouldReturnSuccess() throws Exception {
        // Arrange
        verificationRequest.setContact("+263771234567");
        verificationRequest.setVerificationMethod("SMS");
        
        String expectedToken = "test-token-456";
        when(trackingVerificationService.requestTrackingVerification(
                anyString(), anyString(),
                any(TrackingVerification.VerificationMethod.class),
                anyString(), anyString()))
                .thenReturn(expectedToken);

        // Act & Assert
        mockMvc.perform(post("/api/public/tracking/request-verification")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(verificationRequest))
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("Verification sent successfully"))
                .andExpect(jsonPath("$.token").value(expectedToken))
                .andExpect(jsonPath("$.requiresCode").value(true))
                .andExpect(jsonPath("$.expiryMinutes").value(15))
                .andExpect(jsonPath("$.maskedContact").value("***4567"));
    }

    @Test
    void requestTrackingVerification_InvalidTrackingNumber_ShouldReturnNotFound() throws Exception {
        // Arrange
        when(trackingVerificationService.requestTrackingVerification(
                anyString(), anyString(), any(), anyString(), anyString()))
                .thenThrow(new ResourceNotFoundException("Load not found"));

        // Act & Assert
        mockMvc.perform(post("/api/public/tracking/request-verification")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(verificationRequest))
                        .with(csrf()))
                .andExpect(status().isNotFound());
    }

    @Test
    void requestTrackingVerification_RateLimitExceeded_ShouldReturnBadRequest() throws Exception {
        // Arrange
        when(trackingVerificationService.requestTrackingVerification(
                anyString(), anyString(), any(), anyString(), anyString()))
                .thenThrow(new BusinessException("Too many requests"));

        // Act & Assert
        mockMvc.perform(post("/api/public/tracking/request-verification")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(verificationRequest))
                        .with(csrf()))
                .andExpect(status().isBadRequest());
    }

    @Test
    void requestTrackingVerification_InvalidInput_ShouldReturnBadRequest() throws Exception {
        // Arrange
        verificationRequest.setTrackingNumber(""); // Invalid empty tracking number

        // Act & Assert
        mockMvc.perform(post("/api/public/tracking/request-verification")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(verificationRequest))
                        .with(csrf()))
                .andExpect(status().isBadRequest());
    }

    @Test
    void verifyTrackingCode_ValidCode_ShouldReturnSuccess() throws Exception {
        // Arrange
        String expectedToken = "verified-token-123";
        when(trackingVerificationService.verifyTrackingCode(
                eq("LP123456789"), eq("123456"), anyString()))
                .thenReturn(expectedToken);

        // Act & Assert
        mockMvc.perform(post("/api/public/tracking/verify-code")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(codeVerificationRequest))
                        .with(csrf()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("Verification successful"))
                .andExpect(jsonPath("$.token").value(expectedToken))
                .andExpect(jsonPath("$.validHours").value(24));
    }

    @Test
    void verifyTrackingCode_InvalidCode_ShouldReturnBadRequest() throws Exception {
        // Arrange
        when(trackingVerificationService.verifyTrackingCode(
                anyString(), anyString(), anyString()))
                .thenThrow(new BusinessException("Invalid verification code"));

        // Act & Assert
        mockMvc.perform(post("/api/public/tracking/verify-code")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(codeVerificationRequest))
                        .with(csrf()))
                .andExpect(status().isBadRequest());
    }

    @Test
    void verifyTrackingCode_InvalidCodeFormat_ShouldReturnBadRequest() throws Exception {
        // Arrange
        codeVerificationRequest.setVerificationCode("12345"); // Invalid format (5 digits instead of 6)

        // Act & Assert
        mockMvc.perform(post("/api/public/tracking/verify-code")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(codeVerificationRequest))
                        .with(csrf()))
                .andExpect(status().isBadRequest());
    }

    @Test
    void getSecureTrackingDetails_ValidToken_ShouldReturnTrackingData() throws Exception {
        // Arrange
        when(trackingVerificationService.getTrackingDetails(eq("valid-token"), anyString()))
                .thenReturn(loadResponse);

        // Act & Assert
        mockMvc.perform(get("/api/public/tracking/details")
                        .param("token", "valid-token"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.trackingNumber").value("LP123456789"))
                .andExpect(jsonPath("$.title").value("Test Shipment"))
                .andExpect(jsonPath("$.status").value("IN_TRANSIT"))
                .andExpect(jsonPath("$.pickupLocation").value("Harare"))
                .andExpect(jsonPath("$.deliveryLocation").value("Bulawayo"));
    }

    @Test
    void getSecureTrackingDetails_InvalidToken_ShouldReturnBadRequest() throws Exception {
        // Arrange
        when(trackingVerificationService.getTrackingDetails(eq("invalid-token"), anyString()))
                .thenThrow(new BusinessException("Invalid token"));

        // Act & Assert
        mockMvc.perform(get("/api/public/tracking/details")
                        .param("token", "invalid-token"))
                .andExpect(status().isBadRequest());
    }

    @Test
    void getSecureTrackingDetails_MissingToken_ShouldReturnBadRequest() throws Exception {
        // Act & Assert
        mockMvc.perform(get("/api/public/tracking/details"))
                .andExpect(status().isBadRequest());
    }

    @Test
    void requestTrackingVerification_InvalidVerificationMethod_ShouldReturnBadRequest() throws Exception {
        // Arrange - Test with null contact (required field)
        verificationRequest.setContact(null);

        // Act & Assert
        mockMvc.perform(post("/api/public/tracking/request-verification")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(verificationRequest))
                        .with(csrf()))
                .andExpect(status().isBadRequest());
    }
}
