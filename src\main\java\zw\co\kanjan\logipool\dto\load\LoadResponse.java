package zw.co.kanjan.logipool.dto.load;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import zw.co.kanjan.logipool.entity.Load;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LoadResponse {
    
    private Long id;
    private String trackingNumber;
    private String title;
    private String description;
    private String cargoType;
    private BigDecimal weight;
    private String weightUnit;
    private BigDecimal volume;
    private String volumeUnit;
    private String pickupLocation;
    private BigDecimal pickupLatitude;
    private BigDecimal pickupLongitude;
    private String deliveryLocation;
    private BigDecimal deliveryLatitude;
    private BigDecimal deliveryLongitude;
    private LocalDateTime pickupDate;
    private LocalDateTime deliveryDate;
    private BigDecimal estimatedDistance;
    private String distanceUnit;
    private Load.LoadType loadType;
    private Load.PaymentType paymentType;
    private BigDecimal paymentRate;
    private String paymentUnit;
    private BigDecimal estimatedValue;
    private Load.LoadStatus status;
    private Load.Priority priority;
    private Boolean isVerified;
    private Boolean requiresInsurance;
    private Boolean requiresSpecialHandling;
    private String specialInstructions;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private LocalDateTime biddingClosesAt;
    private Long clientId;
    private String clientName;
    private Long assignedCompanyId;
    private String assignedCompanyName;
    private Integer bidCount;

    // Fleet assignment information
    private Long fleetAssignmentId;
    private Long assignedTruckHeadId;
    private String assignedTruckHeadRegistration;
    private String assignedTruckHeadMake;
    private String assignedTruckHeadModel;
    private Long assignedTrailer1Id;
    private String assignedTrailer1Registration;
    private String assignedTrailer1Type;
    private Long assignedTrailer2Id;
    private String assignedTrailer2Registration;
    private String assignedTrailer2Type;
    private Long assignedPrimaryDriverId;
    private String assignedPrimaryDriverName;
    private Long assignedSecondaryDriverId;
    private String assignedSecondaryDriverName;
    private String fleetAssignmentStatus;
    private LocalDateTime fleetAssignmentScheduledStartTime;
    private LocalDateTime fleetAssignmentScheduledEndTime;
}
