package zw.co.kanjan.logipool.dto.fleet;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import zw.co.kanjan.logipool.entity.TruckHead;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class TruckHeadDto {
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TruckHeadCreateRequest {
        
        @NotBlank(message = "Registration number is required")
        @Size(max = 20, message = "Registration number must not exceed 20 characters")
        private String registrationNumber;
        
        @NotBlank(message = "Make is required")
        @Size(max = 50, message = "Make must not exceed 50 characters")
        private String make;
        
        @NotBlank(message = "Model is required")
        @Size(max = 50, message = "Model must not exceed 50 characters")
        private String model;
        
        @Min(value = 1900, message = "Year must be after 1900")
        @Max(value = 2030, message = "Year must not exceed 2030")
        private Integer year;
        
        @Size(max = 50, message = "Engine number must not exceed 50 characters")
        private String engineNumber;
        
        @Size(max = 50, message = "Chassis number must not exceed 50 characters")
        private String chassisNumber;
        
        private TruckHead.FuelType fuelType;
        
        @DecimalMin(value = "0.0", inclusive = false, message = "Fuel capacity must be positive")
        private BigDecimal fuelCapacity;
        
        @DecimalMin(value = "0.0", inclusive = false, message = "Max towing capacity must be positive")
        private BigDecimal maxTowingCapacity;
        
        @Size(max = 20, message = "Capacity unit must not exceed 20 characters")
        private String capacityUnit = "kg";
        
        @Size(max = 1000, message = "Description must not exceed 1000 characters")
        private String description;
        
        @Size(max = 500, message = "Image URL must not exceed 500 characters")
        private String imageUrl;
        
        @Size(max = 100, message = "Current location must not exceed 100 characters")
        private String currentLocation;
        
        private BigDecimal currentLatitude;
        
        private BigDecimal currentLongitude;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TruckHeadUpdateRequest {
        
        @Size(max = 50, message = "Make must not exceed 50 characters")
        private String make;
        
        @Size(max = 50, message = "Model must not exceed 50 characters")
        private String model;
        
        @Min(value = 1900, message = "Year must be after 1900")
        @Max(value = 2030, message = "Year must not exceed 2030")
        private Integer year;
        
        @Size(max = 50, message = "Engine number must not exceed 50 characters")
        private String engineNumber;
        
        @Size(max = 50, message = "Chassis number must not exceed 50 characters")
        private String chassisNumber;
        
        private TruckHead.TruckStatus status;
        
        private TruckHead.FuelType fuelType;
        
        @DecimalMin(value = "0.0", inclusive = false, message = "Fuel capacity must be positive")
        private BigDecimal fuelCapacity;
        
        @DecimalMin(value = "0.0", inclusive = false, message = "Max towing capacity must be positive")
        private BigDecimal maxTowingCapacity;
        
        @Size(max = 20, message = "Capacity unit must not exceed 20 characters")
        private String capacityUnit;
        
        @Size(max = 1000, message = "Description must not exceed 1000 characters")
        private String description;
        
        @Size(max = 500, message = "Image URL must not exceed 500 characters")
        private String imageUrl;
        
        @Size(max = 100, message = "Current location must not exceed 100 characters")
        private String currentLocation;
        
        private BigDecimal currentLatitude;
        
        private BigDecimal currentLongitude;
        
        private Boolean hasInsurance;
        
        private Boolean hasFitnessCertificate;
        
        private Boolean hasRoadPermit;
        
        private Boolean hasTaxClearance;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime insuranceExpiryDate;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime fitnessExpiryDate;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime roadPermitExpiryDate;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime taxClearanceExpiryDate;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime lastMaintenanceDate;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime nextMaintenanceDate;
        
        private Long currentDriverId;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TruckHeadResponse {
        
        private Long id;
        private String registrationNumber;
        private String make;
        private String model;
        private Integer year;
        private String engineNumber;
        private String chassisNumber;
        private TruckHead.TruckStatus status;
        private TruckHead.FuelType fuelType;
        private BigDecimal fuelCapacity;
        private BigDecimal maxTowingCapacity;
        private String capacityUnit;
        private String description;
        private String imageUrl;
        private String currentLocation;
        private BigDecimal currentLatitude;
        private BigDecimal currentLongitude;
        private Boolean hasInsurance;
        private Boolean hasFitnessCertificate;
        private Boolean hasRoadPermit;
        private Boolean hasTaxClearance;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime insuranceExpiryDate;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime fitnessExpiryDate;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime roadPermitExpiryDate;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime taxClearanceExpiryDate;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime lastMaintenanceDate;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime nextMaintenanceDate;
        
        private Long companyId;
        private String companyName;
        private Long currentDriverId;
        private String currentDriverName;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime createdAt;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime updatedAt;
    }
}
