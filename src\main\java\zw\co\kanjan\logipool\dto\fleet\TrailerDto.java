package zw.co.kanjan.logipool.dto.fleet;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import zw.co.kanjan.logipool.entity.Trailer;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class TrailerDto {
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TrailerCreateRequest {
        
        @NotBlank(message = "Registration number is required")
        @Size(max = 20, message = "Registration number must not exceed 20 characters")
        private String registrationNumber;
        
        @NotBlank(message = "Make is required")
        @Size(max = 50, message = "Make must not exceed 50 characters")
        private String make;
        
        @NotBlank(message = "Model is required")
        @Size(max = 50, message = "Model must not exceed 50 characters")
        private String model;
        
        @Min(value = 1900, message = "Year must be after 1900")
        @Max(value = 2030, message = "Year must not exceed 2030")
        private Integer year;
        
        @Size(max = 50, message = "Chassis number must not exceed 50 characters")
        private String chassisNumber;
        
        @NotNull(message = "Trailer type is required")
        private Trailer.TrailerType type;
        
        @DecimalMin(value = "0.0", inclusive = false, message = "Max weight must be positive")
        private BigDecimal maxWeight;
        
        @DecimalMin(value = "0.0", inclusive = false, message = "Max volume must be positive")
        private BigDecimal maxVolume;
        
        @Size(max = 20, message = "Weight unit must not exceed 20 characters")
        private String weightUnit = "kg";
        
        @Size(max = 20, message = "Volume unit must not exceed 20 characters")
        private String volumeUnit = "m3";
        
        @DecimalMin(value = "0.0", inclusive = false, message = "Length must be positive")
        private BigDecimal length;
        
        @DecimalMin(value = "0.0", inclusive = false, message = "Width must be positive")
        private BigDecimal width;
        
        @DecimalMin(value = "0.0", inclusive = false, message = "Height must be positive")
        private BigDecimal height;
        
        @Size(max = 20, message = "Dimension unit must not exceed 20 characters")
        private String dimensionUnit = "m";
        
        @Size(max = 1000, message = "Description must not exceed 1000 characters")
        private String description;
        
        @Size(max = 500, message = "Image URL must not exceed 500 characters")
        private String imageUrl;
        
        @Size(max = 100, message = "Current location must not exceed 100 characters")
        private String currentLocation;
        
        private BigDecimal currentLatitude;
        
        private BigDecimal currentLongitude;
        
        private Boolean hasRefrigeration = false;
        
        private Boolean hasTailLift = false;
        
        private Boolean hasSideLoader = false;
        
        private Boolean hasGPS = false;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TrailerUpdateRequest {
        
        @Size(max = 50, message = "Make must not exceed 50 characters")
        private String make;
        
        @Size(max = 50, message = "Model must not exceed 50 characters")
        private String model;
        
        @Min(value = 1900, message = "Year must be after 1900")
        @Max(value = 2030, message = "Year must not exceed 2030")
        private Integer year;
        
        @Size(max = 50, message = "Chassis number must not exceed 50 characters")
        private String chassisNumber;
        
        private Trailer.TrailerStatus status;
        
        private Trailer.TrailerType type;
        
        @DecimalMin(value = "0.0", inclusive = false, message = "Max weight must be positive")
        private BigDecimal maxWeight;
        
        @DecimalMin(value = "0.0", inclusive = false, message = "Max volume must be positive")
        private BigDecimal maxVolume;
        
        @Size(max = 20, message = "Weight unit must not exceed 20 characters")
        private String weightUnit;
        
        @Size(max = 20, message = "Volume unit must not exceed 20 characters")
        private String volumeUnit;
        
        @DecimalMin(value = "0.0", inclusive = false, message = "Length must be positive")
        private BigDecimal length;
        
        @DecimalMin(value = "0.0", inclusive = false, message = "Width must be positive")
        private BigDecimal width;
        
        @DecimalMin(value = "0.0", inclusive = false, message = "Height must be positive")
        private BigDecimal height;
        
        @Size(max = 20, message = "Dimension unit must not exceed 20 characters")
        private String dimensionUnit;
        
        @Size(max = 1000, message = "Description must not exceed 1000 characters")
        private String description;
        
        @Size(max = 500, message = "Image URL must not exceed 500 characters")
        private String imageUrl;
        
        @Size(max = 100, message = "Current location must not exceed 100 characters")
        private String currentLocation;
        
        private BigDecimal currentLatitude;
        
        private BigDecimal currentLongitude;
        
        private Boolean hasInsurance;
        
        private Boolean hasFitnessCertificate;
        
        private Boolean hasRoadPermit;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime insuranceExpiryDate;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime fitnessExpiryDate;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime roadPermitExpiryDate;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime lastMaintenanceDate;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime nextMaintenanceDate;
        
        private Boolean hasRefrigeration;
        
        private Boolean hasTailLift;
        
        private Boolean hasSideLoader;
        
        private Boolean hasGPS;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TrailerResponse {
        
        private Long id;
        private String registrationNumber;
        private String make;
        private String model;
        private Integer year;
        private String chassisNumber;
        private Trailer.TrailerStatus status;
        private Trailer.TrailerType type;
        private BigDecimal maxWeight;
        private BigDecimal maxVolume;
        private String weightUnit;
        private String volumeUnit;
        private BigDecimal length;
        private BigDecimal width;
        private BigDecimal height;
        private String dimensionUnit;
        private String description;
        private String imageUrl;
        private String currentLocation;
        private BigDecimal currentLatitude;
        private BigDecimal currentLongitude;
        private Boolean hasInsurance;
        private Boolean hasFitnessCertificate;
        private Boolean hasRoadPermit;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime insuranceExpiryDate;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime fitnessExpiryDate;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime roadPermitExpiryDate;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime lastMaintenanceDate;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime nextMaintenanceDate;
        
        private Boolean hasRefrigeration;
        private Boolean hasTailLift;
        private Boolean hasSideLoader;
        private Boolean hasGPS;
        private Long companyId;
        private String companyName;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime createdAt;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime updatedAt;
    }
}
