package zw.co.kanjan.logipool.mapper.fleet;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import zw.co.kanjan.logipool.dto.fleet.FleetAssignmentDto;
import zw.co.kanjan.logipool.entity.FleetAssignment;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface FleetAssignmentMapper {
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "load", ignore = true)
    @Mapping(target = "bid", ignore = true)
    @Mapping(target = "truckHead", ignore = true)
    @Mapping(target = "trailer1", ignore = true)
    @Mapping(target = "trailer2", ignore = true)
    @Mapping(target = "primaryDriver", ignore = true)
    @Mapping(target = "secondaryDriver", ignore = true)
    @Mapping(target = "company", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "assignedAt", ignore = true)
    @Mapping(target = "actualStartTime", ignore = true)
    @Mapping(target = "actualEndTime", ignore = true)
    @Mapping(target = "assignedBy", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    FleetAssignment toEntity(FleetAssignmentDto.FleetAssignmentCreateRequest request);
    
    @Mapping(source = "load.id", target = "loadId")
    @Mapping(source = "load.title", target = "loadTitle")
    @Mapping(source = "load.trackingNumber", target = "loadTrackingNumber")
    @Mapping(source = "bid.id", target = "bidId")
    @Mapping(source = "truckHead.id", target = "truckHeadId")
    @Mapping(source = "truckHead.registrationNumber", target = "truckHeadRegistration")
    @Mapping(source = "truckHead.make", target = "truckHeadMake")
    @Mapping(source = "truckHead.model", target = "truckHeadModel")
    @Mapping(source = "trailer1.id", target = "trailer1Id")
    @Mapping(source = "trailer1.registrationNumber", target = "trailer1Registration")
    @Mapping(expression = "java(fleetAssignment.getTrailer1() != null ? fleetAssignment.getTrailer1().getType().toString() : null)", target = "trailer1Type")
    @Mapping(source = "trailer2.id", target = "trailer2Id")
    @Mapping(source = "trailer2.registrationNumber", target = "trailer2Registration")
    @Mapping(expression = "java(fleetAssignment.getTrailer2() != null ? fleetAssignment.getTrailer2().getType().toString() : null)", target = "trailer2Type")
    @Mapping(source = "primaryDriver.id", target = "primaryDriverId")
    @Mapping(expression = "java(fleetAssignment.getPrimaryDriver() != null ? fleetAssignment.getPrimaryDriver().getFirstName() + \" \" + fleetAssignment.getPrimaryDriver().getLastName() : null)", target = "primaryDriverName")
    @Mapping(source = "secondaryDriver.id", target = "secondaryDriverId")
    @Mapping(expression = "java(fleetAssignment.getSecondaryDriver() != null ? fleetAssignment.getSecondaryDriver().getFirstName() + \" \" + fleetAssignment.getSecondaryDriver().getLastName() : null)", target = "secondaryDriverName")
    @Mapping(source = "company.id", target = "companyId")
    @Mapping(source = "company.name", target = "companyName")
    @Mapping(source = "assignedBy.id", target = "assignedById")
    @Mapping(expression = "java(fleetAssignment.getAssignedBy() != null ? fleetAssignment.getAssignedBy().getFirstName() + \" \" + fleetAssignment.getAssignedBy().getLastName() : null)", target = "assignedByName")
    FleetAssignmentDto.FleetAssignmentResponse toResponse(FleetAssignment fleetAssignment);
}
