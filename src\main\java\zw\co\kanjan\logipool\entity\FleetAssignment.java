package zw.co.kanjan.logipool.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

@Entity
@Table(name = "fleet_assignments")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FleetAssignment {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "load_id")
    private Load load;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "bid_id")
    private Bid bid;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "truck_head_id")
    private TruckHead truckHead;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "trailer1_id")
    private Trailer trailer1;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "trailer2_id")
    private Trailer trailer2;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "primary_driver_id")
    private User primaryDriver;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "secondary_driver_id")
    private User secondaryDriver;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "company_id")
    private Company company;
    
    @Enumerated(EnumType.STRING)
    @Column(length = 20)
    @Builder.Default
    private AssignmentStatus status = AssignmentStatus.PLANNED;
    
    private LocalDateTime assignedAt;
    
    private LocalDateTime scheduledStartTime;
    
    private LocalDateTime actualStartTime;
    
    private LocalDateTime scheduledEndTime;
    
    private LocalDateTime actualEndTime;
    
    @Column(columnDefinition = "TEXT")
    private String notes;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "assigned_by")
    private User assignedBy;
    
    @CreationTimestamp
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    private LocalDateTime updatedAt;
    
    public enum AssignmentStatus {
        PLANNED,        // Assignment planned but not started
        ACTIVE,         // Assignment is currently active
        COMPLETED,      // Assignment completed successfully
        CANCELLED,      // Assignment was cancelled
        DELAYED,        // Assignment is delayed
        IN_PROGRESS     // Assignment is in progress
    }
}
