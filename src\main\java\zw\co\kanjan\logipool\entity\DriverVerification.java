package zw.co.kanjan.logipool.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import zw.co.kanjan.logipool.dto.driver.DriverDto;
import zw.co.kanjan.logipool.dto.driver.DriverVerificationDto;

import java.time.LocalDateTime;

@Entity
@Table(name = "driver_verifications")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DriverVerification {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "driver_id", nullable = false)
    @NotNull
    private User driver;
    
    @Enumerated(EnumType.STRING)
    @Column(length = 30, nullable = false)
    @NotNull
    private DriverVerificationDto.VerificationType verificationType;
    
    @Enumerated(EnumType.STRING)
    @Column(length = 20)
    @Builder.Default
    private DriverDto.VerificationStatus status = DriverDto.VerificationStatus.PENDING;
    
    @Size(max = 500)
    @Column(columnDefinition = "TEXT")
    private String notes;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "verified_by")
    private User verifiedBy;
    
    private LocalDateTime verifiedAt;
    
    @CreationTimestamp
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    private LocalDateTime updatedAt;
    
    // Helper methods
    public String getTaskName() {
        return verificationType.getTaskName();
    }
    
    public String getTaskDescription() {
        return verificationType.getDescription();
    }
    
    public boolean isCompleted() {
        return status == DriverDto.VerificationStatus.VERIFIED;
    }
    
    public boolean isPending() {
        return status == DriverDto.VerificationStatus.PENDING;
    }
    
    public boolean isRejected() {
        return status == DriverDto.VerificationStatus.REJECTED;
    }
    
    public boolean isExpired() {
        return status == DriverDto.VerificationStatus.EXPIRED;
    }
}
