package zw.co.kanjan.logipool.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;
import zw.co.kanjan.logipool.dto.fleet.TruckHeadDto;
import zw.co.kanjan.logipool.entity.Company;
import zw.co.kanjan.logipool.entity.TruckHead;
import zw.co.kanjan.logipool.entity.User;
import zw.co.kanjan.logipool.repository.CompanyRepository;
import zw.co.kanjan.logipool.repository.TruckHeadRepository;
import zw.co.kanjan.logipool.repository.UserRepository;

import static org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers.springSecurity;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
class TruckHeadControllerIntegrationTest {

    @Autowired
    private WebApplicationContext context;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private CompanyRepository companyRepository;

    @Autowired
    private TruckHeadRepository truckHeadRepository;

    private MockMvc mockMvc;
    private User testUser;
    private Company testCompany;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders
                .webAppContextSetup(context)
                .apply(springSecurity())
                .build();

        // Create test company
        testCompany = Company.builder()
                .name("Test Transport Company")
                .email("<EMAIL>")
                .phoneNumber("+263123456789")
                .address("123 Test Street")
                .city("Harare")
                .country("Zimbabwe")
                .build();
        testCompany = companyRepository.save(testCompany);

        // Create test user
        testUser = User.builder()
                .username("testuser")
                .email("<EMAIL>")
                .password("password123") // Add required password field
                .firstName("Test")
                .lastName("User")
                .phoneNumber("+263987654321")
                .company(testCompany)
                .build();
        testUser = userRepository.save(testUser);
    }

    @Test
    @WithMockUser(username = "testuser", roles = {"TRANSPORTER"})
    void createTruckHead_Success() throws Exception {
        // Given
        TruckHeadDto.TruckHeadCreateRequest request = TruckHeadDto.TruckHeadCreateRequest.builder()
                .registrationNumber("ABC123")
                .make("Volvo")
                .model("FH16")
                .year(2020)
                .fuelType(TruckHead.FuelType.DIESEL)
                .description("Test truck head")
                .build();

        // When & Then
        mockMvc.perform(post("/api/fleet/truck-heads")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.registrationNumber").value("ABC123"))
                .andExpect(jsonPath("$.make").value("Volvo"))
                .andExpect(jsonPath("$.model").value("FH16"))
                .andExpect(jsonPath("$.year").value(2020))
                .andExpect(jsonPath("$.status").value("AVAILABLE"))
                .andExpect(jsonPath("$.companyId").value(testCompany.getId()));
    }

    @Test
    @WithMockUser(username = "testuser", roles = {"TRANSPORTER"})
    void createTruckHead_DuplicateRegistration_BadRequest() throws Exception {
        // Given
        TruckHead existingTruckHead = TruckHead.builder()
                .registrationNumber("ABC123")
                .make("Scania")
                .model("R500")
                .status(TruckHead.TruckStatus.AVAILABLE)
                .company(testCompany)
                .build();
        truckHeadRepository.save(existingTruckHead);

        TruckHeadDto.TruckHeadCreateRequest request = TruckHeadDto.TruckHeadCreateRequest.builder()
                .registrationNumber("ABC123")
                .make("Volvo")
                .model("FH16")
                .year(2020)
                .build();

        // When & Then
        mockMvc.perform(post("/api/fleet/truck-heads")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockUser(username = "testuser", roles = {"TRANSPORTER"})
    void getTruckHead_Success() throws Exception {
        // Given
        TruckHead truckHead = TruckHead.builder()
                .registrationNumber("ABC123")
                .make("Volvo")
                .model("FH16")
                .year(2020)
                .status(TruckHead.TruckStatus.AVAILABLE)
                .company(testCompany)
                .build();
        truckHead = truckHeadRepository.save(truckHead);

        // When & Then
        mockMvc.perform(get("/api/fleet/truck-heads/{id}", truckHead.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(truckHead.getId()))
                .andExpect(jsonPath("$.registrationNumber").value("ABC123"))
                .andExpect(jsonPath("$.make").value("Volvo"))
                .andExpect(jsonPath("$.model").value("FH16"));
    }

    @Test
    @WithMockUser(username = "testuser", roles = {"TRANSPORTER"})
    void getTruckHeads_Success() throws Exception {
        // Given
        TruckHead truckHead1 = TruckHead.builder()
                .registrationNumber("ABC123")
                .make("Volvo")
                .model("FH16")
                .status(TruckHead.TruckStatus.AVAILABLE)
                .company(testCompany)
                .build();
        
        TruckHead truckHead2 = TruckHead.builder()
                .registrationNumber("DEF456")
                .make("Scania")
                .model("R500")
                .status(TruckHead.TruckStatus.ASSIGNED)
                .company(testCompany)
                .build();
        
        truckHeadRepository.save(truckHead1);
        truckHeadRepository.save(truckHead2);

        // When & Then
        mockMvc.perform(get("/api/fleet/truck-heads")
                        .param("page", "0")
                        .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content.length()").value(2))
                .andExpect(jsonPath("$.totalElements").value(2));
    }

    @Test
    @WithMockUser(username = "testuser", roles = {"TRANSPORTER"})
    void getTruckHeads_WithStatusFilter_Success() throws Exception {
        // Given
        TruckHead availableTruck = TruckHead.builder()
                .registrationNumber("ABC123")
                .make("Volvo")
                .model("FH16")
                .status(TruckHead.TruckStatus.AVAILABLE)
                .company(testCompany)
                .build();
        
        TruckHead assignedTruck = TruckHead.builder()
                .registrationNumber("DEF456")
                .make("Scania")
                .model("R500")
                .status(TruckHead.TruckStatus.ASSIGNED)
                .company(testCompany)
                .build();
        
        truckHeadRepository.save(availableTruck);
        truckHeadRepository.save(assignedTruck);

        // When & Then
        mockMvc.perform(get("/api/fleet/truck-heads")
                        .param("status", "AVAILABLE"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content.length()").value(1))
                .andExpect(jsonPath("$.content[0].status").value("AVAILABLE"));
    }

    @Test
    @WithMockUser(username = "testuser", roles = {"TRANSPORTER"})
    void getAvailableTruckHeads_Success() throws Exception {
        // Given
        TruckHead availableTruck = TruckHead.builder()
                .registrationNumber("ABC123")
                .make("Volvo")
                .model("FH16")
                .status(TruckHead.TruckStatus.AVAILABLE)
                .company(testCompany)
                .build();
        
        TruckHead assignedTruck = TruckHead.builder()
                .registrationNumber("DEF456")
                .make("Scania")
                .model("R500")
                .status(TruckHead.TruckStatus.ASSIGNED)
                .company(testCompany)
                .build();
        
        truckHeadRepository.save(availableTruck);
        truckHeadRepository.save(assignedTruck);

        // When & Then
        mockMvc.perform(get("/api/fleet/truck-heads/available"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].status").value("AVAILABLE"));
    }

    @Test
    @WithMockUser(username = "testuser", roles = {"TRANSPORTER"})
    void updateTruckHead_Success() throws Exception {
        // Given
        TruckHead truckHead = TruckHead.builder()
                .registrationNumber("ABC123")
                .make("Volvo")
                .model("FH16")
                .status(TruckHead.TruckStatus.AVAILABLE)
                .company(testCompany)
                .build();
        truckHead = truckHeadRepository.save(truckHead);

        TruckHeadDto.TruckHeadUpdateRequest updateRequest = TruckHeadDto.TruckHeadUpdateRequest.builder()
                .make("Scania")
                .model("R500")
                .status(TruckHead.TruckStatus.MAINTENANCE)
                .build();

        // When & Then
        mockMvc.perform(put("/api/fleet/truck-heads/{id}", truckHead.getId())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(truckHead.getId()))
                .andExpect(jsonPath("$.make").value("Scania"))
                .andExpect(jsonPath("$.model").value("R500"))
                .andExpect(jsonPath("$.status").value("MAINTENANCE"));
    }

    @Test
    @WithMockUser(username = "testuser", roles = {"TRANSPORTER"})
    void updateTruckHeadStatus_Success() throws Exception {
        // Given
        TruckHead truckHead = TruckHead.builder()
                .registrationNumber("ABC123")
                .make("Volvo")
                .model("FH16")
                .status(TruckHead.TruckStatus.AVAILABLE)
                .company(testCompany)
                .build();
        truckHead = truckHeadRepository.save(truckHead);

        // When & Then
        mockMvc.perform(patch("/api/fleet/truck-heads/{id}/status", truckHead.getId())
                        .param("status", "MAINTENANCE"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(truckHead.getId()))
                .andExpect(jsonPath("$.status").value("MAINTENANCE"));
    }

    @Test
    @WithMockUser(username = "testuser", roles = {"TRANSPORTER"})
    void deleteTruckHead_Success() throws Exception {
        // Given
        TruckHead truckHead = TruckHead.builder()
                .registrationNumber("ABC123")
                .make("Volvo")
                .model("FH16")
                .status(TruckHead.TruckStatus.AVAILABLE)
                .company(testCompany)
                .build();
        truckHead = truckHeadRepository.save(truckHead);

        // When & Then
        mockMvc.perform(delete("/api/fleet/truck-heads/{id}", truckHead.getId()))
                .andExpect(status().isNoContent());
    }

    @Test
    @WithMockUser(username = "testuser", roles = {"TRANSPORTER"})
    void deleteTruckHead_AssignedTruck_BadRequest() throws Exception {
        // Given
        TruckHead truckHead = TruckHead.builder()
                .registrationNumber("ABC123")
                .make("Volvo")
                .model("FH16")
                .status(TruckHead.TruckStatus.ASSIGNED)
                .company(testCompany)
                .build();
        truckHead = truckHeadRepository.save(truckHead);

        // When & Then
        mockMvc.perform(delete("/api/fleet/truck-heads/{id}", truckHead.getId()))
                .andExpect(status().isBadRequest());
    }

    @Test
    @WithMockUser(username = "unauthorizeduser", roles = {"CLIENT"})
    void createTruckHead_Unauthorized_Forbidden() throws Exception {
        // Given
        TruckHeadDto.TruckHeadCreateRequest request = TruckHeadDto.TruckHeadCreateRequest.builder()
                .registrationNumber("ABC123")
                .make("Volvo")
                .model("FH16")
                .year(2020)
                .build();

        // When & Then
        mockMvc.perform(post("/api/fleet/truck-heads")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isForbidden());
    }
}
