import 'package:flutter/material.dart';
import '../../models/fleet_dashboard_model.dart';

class FleetOverviewCard extends StatelessWidget {
  final FleetOverview overview;

  const FleetOverviewCard({
    Key? key,
    required this.overview,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.dashboard,
                  color: Theme.of(context).primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Fleet Overview',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildFleetSummary(),
            const SizedBox(height: 16),
            _buildUtilizationSection(),
            const SizedBox(height: 16),
            _buildAssignmentSummary(),
          ],
        ),
      ),
    );
  }

  Widget _buildFleetSummary() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Fleet Summary',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildSummaryItem(
                'Truck Heads',
                overview.totalTruckHeads,
                Icons.local_shipping,
                Colors.blue,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildSummaryItem(
                'Trailers',
                overview.totalTrailers,
                Icons.trailer_truck,
                Colors.green,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSummaryItem(String title, int count, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            count.toString(),
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUtilizationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Utilization',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        _buildUtilizationBar(
          'Truck Heads',
          overview.truckHeadUtilizationRate,
          overview.availableTruckHeads,
          overview.assignedTruckHeads + overview.inTransitTruckHeads,
          overview.maintenanceTruckHeads,
        ),
        const SizedBox(height: 8),
        _buildUtilizationBar(
          'Trailers',
          overview.trailerUtilizationRate,
          overview.availableTrailers,
          overview.assignedTrailers + overview.inTransitTrailers,
          overview.maintenanceTrailers,
        ),
      ],
    );
  }

  Widget _buildUtilizationBar(
    String title,
    double utilizationRate,
    int available,
    int inUse,
    int maintenance,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
            ),
            Text(
              '${utilizationRate.toStringAsFixed(1)}%',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: _getUtilizationColor(utilizationRate),
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Container(
          height: 8,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color: Colors.grey[300],
          ),
          child: Row(
            children: [
              if (inUse > 0)
                Expanded(
                  flex: inUse,
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.horizontal(left: Radius.circular(4)),
                      color: _getUtilizationColor(utilizationRate),
                    ),
                  ),
                ),
              if (maintenance > 0)
                Expanded(
                  flex: maintenance,
                  child: Container(
                    color: Colors.orange,
                  ),
                ),
              if (available > 0)
                Expanded(
                  flex: available,
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: inUse == 0 && maintenance == 0
                          ? BorderRadius.circular(4)
                          : const BorderRadius.horizontal(right: Radius.circular(4)),
                      color: Colors.grey[300],
                    ),
                  ),
                ),
            ],
          ),
        ),
        const SizedBox(height: 4),
        Row(
          children: [
            _buildLegendItem('In Use', _getUtilizationColor(utilizationRate)),
            const SizedBox(width: 16),
            _buildLegendItem('Maintenance', Colors.orange),
            const SizedBox(width: 16),
            _buildLegendItem('Available', Colors.grey),
          ],
        ),
      ],
    );
  }

  Widget _buildLegendItem(String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 4),
        Text(
          label,
          style: const TextStyle(fontSize: 10),
        ),
      ],
    );
  }

  Widget _buildAssignmentSummary() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Assignments',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildAssignmentItem(
                'Active',
                overview.activeAssignments,
                Colors.green,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildAssignmentItem(
                'Planned',
                overview.plannedAssignments,
                Colors.blue,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildAssignmentItem(
                'Completed',
                overview.completedAssignments,
                Colors.grey,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAssignmentItem(String label, int count, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            count.toString(),
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Color _getUtilizationColor(double utilization) {
    if (utilization >= 80) return Colors.green;
    if (utilization >= 60) return Colors.orange;
    return Colors.red;
  }
}
