# Fleet Management API Documentation

## Overview

The Fleet Management API provides comprehensive functionality for managing transportation fleets, including truck heads, trailers, fleet assignments, compliance tracking, and reporting.

## Base URL

```
https://api.logipool.com/api/fleet
```

## Authentication

All fleet management endpoints require authentication using Bearer tokens. Users must have the `TRANSPORTER` or `ADMIN` role to access these endpoints.

```http
Authorization: Bearer <your-jwt-token>
```

## Truck Head Management

### Create Truck Head

Creates a new truck head in the fleet.

```http
POST /truck-heads
```

**Request Body:**
```json
{
  "registrationNumber": "ABC123",
  "make": "Volvo",
  "model": "FH16",
  "year": 2020,
  "engineNumber": "ENG123456",
  "chassisNumber": "CHS789012",
  "fuelType": "DIESEL",
  "fuelCapacity": 500.0,
  "maxTowingCapacity": 40000.0,
  "capacityUnit": "kg",
  "description": "Heavy duty truck head for long haul",
  "currentLocation": "Harare Depot"
}
```

**Response:**
```json
{
  "id": 1,
  "registrationNumber": "ABC123",
  "make": "Volvo",
  "model": "FH16",
  "year": 2020,
  "status": "AVAILABLE",
  "fuelType": "DIESEL",
  "fuelCapacity": 500.0,
  "maxTowingCapacity": 40000.0,
  "companyId": 1,
  "companyName": "Transport Company Ltd",
  "hasInsurance": false,
  "hasFitnessCertificate": false,
  "hasRoadPermit": false,
  "hasTaxClearance": false,
  "createdAt": "2024-01-15T10:30:00",
  "updatedAt": "2024-01-15T10:30:00"
}
```

### Get Truck Heads

Retrieves a paginated list of truck heads with optional filtering.

```http
GET /truck-heads?page=0&size=20&status=AVAILABLE&make=Volvo
```

**Query Parameters:**
- `page` (optional): Page number (default: 0)
- `size` (optional): Page size (default: 20)
- `status` (optional): Filter by status (AVAILABLE, ASSIGNED, IN_TRANSIT, MAINTENANCE, OUT_OF_SERVICE)
- `make` (optional): Filter by make
- `model` (optional): Filter by model
- `registrationNumber` (optional): Filter by registration number

**Response:**
```json
{
  "content": [
    {
      "id": 1,
      "registrationNumber": "ABC123",
      "make": "Volvo",
      "model": "FH16",
      "status": "AVAILABLE",
      "companyId": 1,
      "companyName": "Transport Company Ltd"
    }
  ],
  "pageable": {
    "pageNumber": 0,
    "pageSize": 20
  },
  "totalElements": 1,
  "totalPages": 1
}
```

### Update Truck Head

Updates an existing truck head.

```http
PUT /truck-heads/{id}
```

**Request Body:**
```json
{
  "make": "Scania",
  "model": "R500",
  "status": "MAINTENANCE",
  "hasInsurance": true,
  "insuranceExpiryDate": "2024-12-31T23:59:59",
  "currentDriverId": 5
}
```

### Update Truck Head Status

Updates only the status of a truck head.

```http
PATCH /truck-heads/{id}/status?status=MAINTENANCE
```

### Delete Truck Head

Deletes a truck head from the fleet.

```http
DELETE /truck-heads/{id}
```

**Note:** Cannot delete truck heads that are currently assigned or in transit.

## Fleet Assignment Management

### Create Fleet Assignment

Assigns fleet resources to a load.

```http
POST /assignments
```

**Request Body:**
```json
{
  "loadId": 1,
  "bidId": 1,
  "truckHeadId": 1,
  "trailer1Id": 2,
  "trailer2Id": null,
  "primaryDriverId": 5,
  "secondaryDriverId": 6,
  "scheduledStartTime": "2024-01-20T08:00:00",
  "scheduledEndTime": "2024-01-22T18:00:00",
  "notes": "Handle with care - fragile cargo"
}
```

**Response:**
```json
{
  "id": 1,
  "loadId": 1,
  "loadTitle": "Electronics Shipment",
  "loadTrackingNumber": "TN001",
  "truckHeadId": 1,
  "truckHeadRegistration": "ABC123",
  "trailer1Id": 2,
  "trailer1Registration": "TRL001",
  "primaryDriverId": 5,
  "primaryDriverName": "John Doe",
  "status": "PLANNED",
  "scheduledStartTime": "2024-01-20T08:00:00",
  "scheduledEndTime": "2024-01-22T18:00:00",
  "assignedAt": "2024-01-15T10:30:00"
}
```

### Check Fleet Availability

Checks if a fleet resource is available for a specific time period.

```http
POST /assignments/check-availability/{resourceType}/{resourceId}
```

**Path Parameters:**
- `resourceType`: Type of resource (truck, trailer, driver)
- `resourceId`: ID of the resource

**Request Body:**
```json
{
  "startTime": "2024-01-20T08:00:00",
  "endTime": "2024-01-22T18:00:00",
  "excludeAssignmentId": null
}
```

**Response:**
```json
{
  "isAvailable": false,
  "reason": "ABC123 is already assigned to another load during this time period",
  "conflictStartTime": "2024-01-20T06:00:00",
  "conflictEndTime": "2024-01-21T20:00:00",
  "conflictingAssignmentId": 5,
  "conflictingLoadTitle": "Existing Load"
}
```

## Fleet Dashboard

### Get Fleet Overview

Retrieves comprehensive fleet statistics and overview.

```http
GET /dashboard/overview
```

**Response:**
```json
{
  "totalTruckHeads": 25,
  "availableTruckHeads": 15,
  "assignedTruckHeads": 8,
  "inTransitTruckHeads": 2,
  "maintenanceTruckHeads": 0,
  "totalTrailers": 40,
  "availableTrailers": 25,
  "assignedTrailers": 12,
  "inTransitTrailers": 3,
  "maintenanceTrailers": 0,
  "totalAssignments": 150,
  "activeAssignments": 10,
  "plannedAssignments": 5,
  "completedAssignments": 135,
  "truckHeadUtilizationRate": 40.0,
  "trailerUtilizationRate": 37.5,
  "totalComplianceAlerts": 3
}
```

### Get Compliance Alerts

Retrieves compliance alerts for expiring documents.

```http
GET /dashboard/compliance-alerts
```

**Response:**
```json
[
  {
    "alertType": "INSURANCE_EXPIRY",
    "severity": "CRITICAL",
    "title": "URGENT: Insurance Expiring Soon",
    "message": "Insurance policy POL123 for Truck Head ABC123 expires on 2024-01-20. Please renew immediately.",
    "resourceType": "TRUCK_HEAD",
    "resourceId": 1,
    "resourceName": "ABC123",
    "expiryDate": "2024-01-20T23:59:59",
    "daysToExpiry": 5,
    "severityColor": "#FF0000",
    "alertIcon": "shield-alert"
  }
]
```

### Get Fleet Utilization

Retrieves fleet utilization statistics for a date range.

```http
GET /dashboard/utilization?startDate=2024-01-01T00:00:00&endDate=2024-01-31T23:59:59
```

**Response:**
```json
[
  {
    "resourceType": "TRUCK_HEAD",
    "resourceId": 1,
    "resourceName": "ABC123",
    "totalHours": 744,
    "assignedHours": 320,
    "utilizationPercentage": 43.0,
    "totalAssignments": 8,
    "idleHours": 424,
    "utilizationStatus": "LOW",
    "utilizationColor": "#FF8C00"
  }
]
```

## Data Export

### Export Fleet Overview

Exports comprehensive fleet data to Excel.

```http
GET /export/fleet-overview
```

**Response:** Excel file download

### Export Compliance Report

Exports compliance report for a date range.

```http
GET /export/compliance-report?startDate=2024-01-01T00:00:00&endDate=2024-01-31T23:59:59
```

**Response:** Excel file download

### Export Utilization Report

Exports fleet utilization report.

```http
GET /export/utilization-report?startDate=2024-01-01T00:00:00&endDate=2024-01-31T23:59:59
```

**Response:** Excel file download

## Error Responses

### 400 Bad Request
```json
{
  "error": "Bad Request",
  "message": "Truck head with registration number ABC123 already exists",
  "timestamp": "2024-01-15T10:30:00"
}
```

### 403 Forbidden
```json
{
  "error": "Forbidden",
  "message": "You can only update your own truck heads",
  "timestamp": "2024-01-15T10:30:00"
}
```

### 404 Not Found
```json
{
  "error": "Not Found",
  "message": "Truck head not found with id: 999",
  "timestamp": "2024-01-15T10:30:00"
}
```

## Rate Limiting

API requests are limited to 1000 requests per hour per user. Rate limit headers are included in responses:

```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1642248000
```

## Webhooks

Fleet management events can trigger webhooks to notify external systems:

### Fleet Assignment Created
```json
{
  "event": "fleet.assignment.created",
  "data": {
    "assignmentId": 1,
    "loadId": 1,
    "truckHeadId": 1,
    "status": "PLANNED"
  },
  "timestamp": "2024-01-15T10:30:00"
}
```

### Compliance Alert
```json
{
  "event": "fleet.compliance.alert",
  "data": {
    "alertType": "INSURANCE_EXPIRY",
    "severity": "CRITICAL",
    "resourceId": 1,
    "resourceType": "TRUCK_HEAD",
    "daysToExpiry": 5
  },
  "timestamp": "2024-01-15T10:30:00"
}
```

## SDK Examples

### JavaScript/Node.js
```javascript
const logipool = require('@logipool/sdk');

const client = new logipool.FleetClient({
  apiKey: 'your-api-key',
  baseUrl: 'https://api.logipool.com'
});

// Create truck head
const truckHead = await client.truckHeads.create({
  registrationNumber: 'ABC123',
  make: 'Volvo',
  model: 'FH16',
  year: 2020
});

// Get fleet overview
const overview = await client.dashboard.getOverview();
```

### Python
```python
from logipool import FleetClient

client = FleetClient(
    api_key='your-api-key',
    base_url='https://api.logipool.com'
)

# Create truck head
truck_head = client.truck_heads.create({
    'registrationNumber': 'ABC123',
    'make': 'Volvo',
    'model': 'FH16',
    'year': 2020
})

# Get fleet overview
overview = client.dashboard.get_overview()
```
