package zw.co.kanjan.logipool.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "trailers")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Trailer {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank
    @Size(max = 20)
    @Column(unique = true)
    private String registrationNumber;
    
    @NotBlank
    @Size(max = 50)
    private String make;
    
    @NotBlank
    @Size(max = 50)
    private String model;
    
    @Column(name = "manufacture_year")
    private Integer year;
    
    @Size(max = 50)
    private String chassisNumber;
    
    @Enumerated(EnumType.STRING)
    @Column(length = 20)
    @Builder.Default
    private TrailerStatus status = TrailerStatus.AVAILABLE;
    
    @Enumerated(EnumType.STRING)
    @Column(length = 30)
    private TrailerType type;
    
    private BigDecimal maxWeight;
    
    private BigDecimal maxVolume;
    
    @Size(max = 20)
    private String weightUnit = "kg";
    
    @Size(max = 20)
    private String volumeUnit = "m3";
    
    private BigDecimal length;
    
    private BigDecimal width;
    
    private BigDecimal height;
    
    @Size(max = 20)
    private String dimensionUnit = "m";
    
    @Column(columnDefinition = "TEXT")
    private String description;
    
    @Size(max = 500)
    private String imageUrl;
    
    // Current location
    @Size(max = 100)
    private String currentLocation;
    
    private BigDecimal currentLatitude;
    
    private BigDecimal currentLongitude;
    
    // Maintenance and compliance
    @Builder.Default
    private Boolean hasInsurance = false;
    
    @Builder.Default
    private Boolean hasFitnessCertificate = false;
    
    @Builder.Default
    private Boolean hasRoadPermit = false;
    
    private LocalDateTime insuranceExpiryDate;
    
    private LocalDateTime fitnessExpiryDate;
    
    private LocalDateTime roadPermitExpiryDate;
    
    private LocalDateTime lastMaintenanceDate;
    
    private LocalDateTime nextMaintenanceDate;
    
    // Special features
    @Builder.Default
    private Boolean hasRefrigeration = false;
    
    @Builder.Default
    private Boolean hasTailLift = false;
    
    @Builder.Default
    private Boolean hasSideLoader = false;
    
    @Builder.Default
    private Boolean hasGPS = false;
    
    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "company_id")
    private Company company;
    
    @OneToMany(mappedBy = "trailer", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @Builder.Default
    private List<Document> documents = new ArrayList<>();
    
    @OneToMany(mappedBy = "trailer1", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @Builder.Default
    private List<FleetAssignment> assignmentsAsTrailer1 = new ArrayList<>();
    
    @OneToMany(mappedBy = "trailer2", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @Builder.Default
    private List<FleetAssignment> assignmentsAsTrailer2 = new ArrayList<>();
    
    @CreationTimestamp
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    private LocalDateTime updatedAt;
    
    public enum TrailerStatus {
        AVAILABLE, ASSIGNED, IN_TRANSIT, MAINTENANCE, OUT_OF_SERVICE, RETIRED
    }
    
    public enum TrailerType {
        FLATBED, CURTAIN_SIDE, BOX_TRAILER, REFRIGERATED, TANKER, 
        LOWLOADER, CONTAINER, TIPPER, LIVESTOCK, CAR_CARRIER
    }
}
