package zw.co.kanjan.logipool.mapper;

import org.springframework.stereotype.Component;
import zw.co.kanjan.logipool.dto.driver.DriverDto;
import zw.co.kanjan.logipool.dto.driver.DriverVerificationDto;
import zw.co.kanjan.logipool.entity.CompanyMember;
import zw.co.kanjan.logipool.entity.DriverVerification;
import zw.co.kanjan.logipool.entity.User;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class DriverMapper {
    
    public DriverDto.DriverResponse toResponse(User driver) {
        if (driver == null) {
            return null;
        }
        
        return DriverDto.DriverResponse.builder()
                .id(driver.getId())
                .firstName(driver.getFirstName())
                .lastName(driver.getLastName())
                .email(driver.getEmail())
                .phoneNumber(driver.getPhoneNumber())
                .createdAt(driver.getCreatedAt())
                .lastLoginAt(driver.getLastLoginAt())
                .build();
    }
    
    public DriverDto.DriverResponse toResponseWithCompany(User driver, CompanyMember companyMember) {
        DriverDto.DriverResponse response = toResponse(driver);
        if (response != null && companyMember != null) {
            response.setCompany(DriverDto.CompanyInfo.builder()
                    .id(companyMember.getCompany().getId())
                    .name(companyMember.getCompany().getName())
                    .role(companyMember.getRole())
                    .status(companyMember.getStatus())
                    .build());
        }
        return response;
    }
    
    public DriverDto.DriverResponse toResponseWithVerification(User driver, List<DriverVerification> verifications) {
        DriverDto.DriverResponse response = toResponse(driver);
        if (response != null) {
            response.setVerificationStatus(buildVerificationStatus(verifications));
        }
        return response;
    }
    
    public DriverDto.DriverResponse toFullResponse(User driver, CompanyMember companyMember, List<DriverVerification> verifications) {
        DriverDto.DriverResponse response = toResponseWithCompany(driver, companyMember);
        if (response != null) {
            response.setVerificationStatus(buildVerificationStatus(verifications));
        }
        return response;
    }
    
    public DriverDto.DriverVerificationStatus buildVerificationStatus(List<DriverVerification> verifications) {
        if (verifications == null || verifications.isEmpty()) {
            return DriverDto.DriverVerificationStatus.builder()
                    .overallStatus(DriverDto.OverallVerificationStatus.UNVERIFIED)
                    .build();
        }
        
        DriverDto.DriverVerificationStatus.DriverVerificationStatusBuilder builder = 
                DriverDto.DriverVerificationStatus.builder();
        
        // Map each verification type
        for (DriverVerification verification : verifications) {
            DriverDto.VerificationTaskStatus taskStatus = DriverDto.VerificationTaskStatus.builder()
                    .status(verification.getStatus())
                    .notes(verification.getNotes())
                    .verifiedBy(verification.getVerifiedBy() != null ? verification.getVerifiedBy().getId() : null)
                    .verifiedAt(verification.getVerifiedAt())
                    .build();
            
            switch (verification.getVerificationType()) {
                case LICENSE -> builder.licenseVerification(taskStatus);
                case DEFENSIVE_TRAINING -> builder.defensiveTrainingVerification(taskStatus);
                case NATIONAL_ID -> builder.nationalIdVerification(taskStatus);
                case PASSPORT -> builder.passportVerification(taskStatus);
                case PHONE_NUMBER -> builder.phoneVerification(taskStatus);
            }
        }
        
        // Calculate overall status
        builder.overallStatus(calculateOverallStatus(verifications));
        
        return builder.build();
    }
    
    public DriverDto.OverallVerificationStatus calculateOverallStatus(List<DriverVerification> verifications) {
        if (verifications == null || verifications.isEmpty()) {
            return DriverDto.OverallVerificationStatus.UNVERIFIED;
        }
        
        long totalRequired = DriverVerificationDto.VerificationType.values().length;
        long verified = verifications.stream()
                .filter(v -> v.getStatus() == DriverDto.VerificationStatus.VERIFIED)
                .count();
        long expired = verifications.stream()
                .filter(v -> v.getStatus() == DriverDto.VerificationStatus.EXPIRED)
                .count();
        
        if (expired > 0) {
            return DriverDto.OverallVerificationStatus.VERIFICATION_EXPIRED;
        } else if (verified == totalRequired) {
            return DriverDto.OverallVerificationStatus.FULLY_VERIFIED;
        } else if (verified > 0) {
            return DriverDto.OverallVerificationStatus.PARTIALLY_VERIFIED;
        } else {
            return DriverDto.OverallVerificationStatus.UNVERIFIED;
        }
    }
    
    public DriverVerificationDto.VerificationTaskResponse toVerificationTaskResponse(DriverVerification verification) {
        if (verification == null) {
            return null;
        }
        
        return DriverVerificationDto.VerificationTaskResponse.builder()
                .id(verification.getId())
                .driverId(verification.getDriver().getId())
                .driverName(verification.getDriver().getFirstName() + " " + verification.getDriver().getLastName())
                .verificationType(verification.getVerificationType())
                .taskName(verification.getTaskName())
                .taskDescription(verification.getTaskDescription())
                .status(verification.getStatus())
                .notes(verification.getNotes())
                .verifiedBy(verification.getVerifiedBy() != null ? verification.getVerifiedBy().getId() : null)
                .verifiedByName(verification.getVerifiedBy() != null ? 
                        verification.getVerifiedBy().getFirstName() + " " + verification.getVerifiedBy().getLastName() : null)
                .verifiedAt(verification.getVerifiedAt())
                .createdAt(verification.getCreatedAt())
                .updatedAt(verification.getUpdatedAt())
                .build();
    }
    
    public DriverVerificationDto.VerificationSummaryResponse toVerificationSummaryResponse(
            User driver, List<DriverVerification> verifications) {
        
        if (driver == null) {
            return null;
        }
        
        List<DriverVerificationDto.VerificationTaskResponse> taskResponses = verifications.stream()
                .map(this::toVerificationTaskResponse)
                .collect(Collectors.toList());
        
        long totalTasks = taskResponses.size();
        long completedTasks = verifications.stream()
                .filter(DriverVerification::isCompleted)
                .count();
        long pendingTasks = verifications.stream()
                .filter(DriverVerification::isPending)
                .count();
        long rejectedTasks = verifications.stream()
                .filter(DriverVerification::isRejected)
                .count();
        
        double completionPercentage = totalTasks > 0 ? (double) completedTasks / totalTasks * 100 : 0.0;
        
        return DriverVerificationDto.VerificationSummaryResponse.builder()
                .driverId(driver.getId())
                .driverName(driver.getFirstName() + " " + driver.getLastName())
                .driverEmail(driver.getEmail())
                .overallStatus(calculateOverallStatus(verifications))
                .verificationTasks(taskResponses)
                .totalTasks((int) totalTasks)
                .completedTasks((int) completedTasks)
                .pendingTasks((int) pendingTasks)
                .rejectedTasks((int) rejectedTasks)
                .completionPercentage(completionPercentage)
                .lastUpdated(verifications.stream()
                        .map(DriverVerification::getUpdatedAt)
                        .max(java.time.LocalDateTime::compareTo)
                        .orElse(null))
                .build();
    }
    
    public DriverVerification toEntity(DriverVerificationDto.UpdateVerificationRequest request, User driver, User verifiedBy) {
        return DriverVerification.builder()
                .driver(driver)
                .verificationType(request.getVerificationType())
                .status(request.getStatus())
                .notes(request.getNotes())
                .verifiedBy(verifiedBy)
                .verifiedAt(request.getStatus() == DriverDto.VerificationStatus.VERIFIED ? 
                        java.time.LocalDateTime.now() : null)
                .build();
    }
}
