package zw.co.kanjan.logipool.dto.load;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import zw.co.kanjan.logipool.entity.Load;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Public load response DTO with sanitized information for guest users
 * Excludes sensitive information like exact locations, payment details, and client information
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PublicLoadResponse {
    
    private Long id;
    private String title;
    private String cargoType;
    private BigDecimal weight;
    private String weightUnit;
    private BigDecimal volume;
    private String volumeUnit;
    
    // Masked location information (city/region only)
    private String pickupRegion;
    private String deliveryRegion;
    
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime pickupDate;
    
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime deliveryDate;
    
    private BigDecimal estimatedDistance;
    private String distanceUnit;
    private Load.LoadType loadType;
    private Load.Priority priority;
    private Boolean isVerified;
    private Boolean requiresInsurance;
    private Boolean requiresSpecialHandling;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime biddingClosesAt;
    
    // Non-sensitive status information
    private Load.LoadStatus status;
    private Integer bidCount;
    
    // Generic description without sensitive details
    private String publicDescription;
    
    /**
     * Mask location to show only city/region for privacy
     */
    public static String maskLocation(String fullLocation) {
        if (fullLocation == null || fullLocation.trim().isEmpty()) {
            return "Not specified";
        }
        
        // Extract city name (assume format like "123 Street, City, Country" or "City, Country")
        String[] parts = fullLocation.split(",");
        if (parts.length >= 2) {
            // Return the second-to-last part (usually the city)
            return parts[parts.length - 2].trim();
        } else if (parts.length == 1) {
            // If no comma, try to extract first word that might be a city
            String[] words = fullLocation.trim().split("\\s+");
            return words[words.length - 1]; // Last word might be city
        }
        
        return "Location available";
    }
    
    /**
     * Create a generic public description from the original description
     */
    public static String createPublicDescription(String originalDescription, String cargoType) {
        if (originalDescription == null || originalDescription.trim().isEmpty()) {
            return "Transportation needed for " + (cargoType != null ? cargoType.toLowerCase() : "cargo");
        }
        
        // Limit description length and remove potentially sensitive information
        String cleaned = originalDescription.replaceAll("(?i)(phone|email|contact|call|\\d{3,})", "[Contact details available]");
        
        if (cleaned.length() > 150) {
            cleaned = cleaned.substring(0, 147) + "...";
        }
        
        return cleaned;
    }
}
