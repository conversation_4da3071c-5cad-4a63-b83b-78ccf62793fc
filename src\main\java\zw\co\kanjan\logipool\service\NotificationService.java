package zw.co.kanjan.logipool.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import zw.co.kanjan.logipool.entity.User;
import zw.co.kanjan.logipool.entity.Load;
import zw.co.kanjan.logipool.entity.Bid;
import zw.co.kanjan.logipool.entity.Company;
import zw.co.kanjan.logipool.entity.Invoice;
import zw.co.kanjan.logipool.entity.LocationPermission;
import zw.co.kanjan.logipool.entity.Payment;
import zw.co.kanjan.logipool.repository.CompanyRepository;
import zw.co.kanjan.logipool.repository.UserRepository;
import zw.co.kanjan.logipool.dto.NotificationDto;

import jakarta.mail.internet.MimeMessage;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class NotificationService {

    private final JavaMailSender mailSender;
    private final EmailTemplateService emailTemplateService;
    private final SmsService smsService;
    private final RealTimeNotificationService realTimeNotificationService;
    private final PersistentNotificationService persistentNotificationService;
    private final CompanyRepository companyRepository;
    private final UserRepository userRepository;

    @Value("${app.notification.email.enabled:true}")
    private boolean emailEnabled;

    @Value("${app.notification.sms.enabled:false}")
    private boolean smsEnabled;

    @Value("${app.notification.realtime.enabled:true}")
    private boolean realtimeEnabled;

    @Value("${app.notification.persistent.enabled:true}")
    private boolean persistentEnabled;

    @Value("${app.base-url:http://localhost:8080}")
    private String baseUrl;

    private final DecimalFormat currencyFormat = new DecimalFormat("#,##0.00");
    private final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("MMM dd, yyyy");
    private final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("MMM dd, yyyy HH:mm");

    /**
     * Generic notification method for simple notifications
     */
    public void sendNotification(User user, String title, String message, String type) {
        log.info("Sending notification to user {}: {}", user.getUsername(), title);

        // Send persistent notification
        if (persistentEnabled) {
            persistentNotificationService.createNotification(
                user.getId(),
                title,
                message,
                type,
                "MEDIUM",
                null,
                null,
                null,
                null
            );
        }

        // Send real-time notification
        if (realtimeEnabled) {
            realTimeNotificationService.sendNotificationToUser(
                user.getId(),
                NotificationDto.builder()
                    .title(title)
                    .message(message)
                    .type(type)
                    .timestamp(java.time.LocalDateTime.now())
                    .build()
            );
        }

        // Send email notification if enabled and user has email
        if (emailEnabled && user.getEmail() != null) {
            try {
                sendSimpleEmail(user.getEmail(), title, message);
            } catch (Exception e) {
                log.error("Failed to send email notification to: {}", user.getEmail(), e);
            }
        }
    }

    public void sendLoadPostedNotification(Load load) {
        log.info("Sending load posted notification for load: {}", load.getTitle());

        // Find transporters in the area (simplified - in real implementation, use location-based queries)
        List<Company> transporters = companyRepository.findAll().stream()
                .filter(company -> company.getUser().getRoles().stream()
                        .anyMatch(role -> role.getName().name().equals("TRANSPORTER")))
                .toList();

        for (Company transporter : transporters) {
            User transporterUser = transporter.getUser();

            // Send persistent notification
            if (persistentEnabled) {
                persistentNotificationService.createLoadPostedNotification(
                    transporterUser.getId(),
                    load.getTitle(),
                    load.getPickupLocation(),
                    load.getId()
                );
            }

            // Send real-time notification
            if (realtimeEnabled) {
                realTimeNotificationService.sendLoadPostedNotification(
                    transporterUser.getId(),
                    load.getTitle(),
                    load.getPickupLocation()
                );
            }

            // Send email notification
            if (emailEnabled && transporterUser.getEmail() != null) {
                sendLoadPostedEmail(transporterUser, load);
            }

            // Send SMS notification
            if (smsEnabled && transporterUser.getPhoneNumber() != null) {
                smsService.sendLoadPostedSms(
                    transporterUser.getPhoneNumber(),
                    load.getTitle(),
                    load.getPickupLocation()
                );
            }
        }
    }
    
    public void sendBidReceivedNotification(Bid bid) {
        log.info("Sending bid received notification for load: {}", bid.getLoad().getTitle());

        User client = bid.getLoad().getClient();
        String bidAmount = currencyFormat.format(bid.getAmount());

        // Send persistent notification
        if (persistentEnabled) {
            persistentNotificationService.createBidReceivedNotification(
                client.getId(),
                bid.getCompany().getName(),
                bidAmount,
                bid.getLoad().getTitle(),
                bid.getId(),
                bid.getLoad().getId()
            );
        }

        // Send real-time notification
        if (realtimeEnabled) {
            realTimeNotificationService.sendBidReceivedNotification(
                client.getId(),
                bid.getCompany().getName(),
                bidAmount,
                bid.getLoad().getTitle()
            );
        }

        // Send email notification
        if (emailEnabled && client.getEmail() != null) {
            sendBidReceivedEmail(client, bid);
        }

        // Send SMS notification
        if (smsEnabled && client.getPhoneNumber() != null) {
            smsService.sendBidReceivedSms(
                client.getPhoneNumber(),
                bid.getCompany().getName(),
                bidAmount
            );
        }
    }
    
    public void sendBidAcceptedNotification(Bid bid) {
        log.info("Sending bid accepted notification for bid: {}", bid.getId());

        User transporter = bid.getCompany().getUser();
        String bidAmount = currencyFormat.format(bid.getAmount());

        // Send persistent notification
        if (persistentEnabled) {
            persistentNotificationService.createBidAcceptedNotification(
                transporter.getId(),
                bid.getLoad().getTitle(),
                bidAmount,
                bid.getId()
            );
        }

        // Send real-time notification
        if (realtimeEnabled) {
            realTimeNotificationService.sendBidAcceptedNotification(
                transporter.getId(),
                bid.getLoad().getTitle(),
                bidAmount
            );
        }

        // Send email notification
        if (emailEnabled && transporter.getEmail() != null) {
            sendBidAcceptedEmail(transporter, bid);
        }

        // Send SMS notification
        if (smsEnabled && transporter.getPhoneNumber() != null) {
            smsService.sendBidAcceptedSms(
                transporter.getPhoneNumber(),
                bid.getLoad().getTitle()
            );
        }
    }
    
    public void sendBidRejectedNotification(Bid bid) {
        log.info("Sending bid rejected notification for bid: {}", bid.getId());

        User transporter = bid.getCompany().getUser();

        // Send real-time notification
        if (realtimeEnabled) {
            realTimeNotificationService.sendBidRejectedNotification(
                transporter.getId(),
                bid.getLoad().getTitle()
            );
        }

        // Send email notification
        if (emailEnabled && transporter.getEmail() != null) {
            sendBidRejectedEmail(transporter, bid);
        }

        // Send SMS notification
        if (smsEnabled && transporter.getPhoneNumber() != null) {
            smsService.sendBidRejectedSms(
                transporter.getPhoneNumber(),
                bid.getLoad().getTitle()
            );
        }
    }

    public void sendLoadStatusUpdateNotification(Load load, String status) {
        log.info("Sending load status update notification for load: {} - Status: {}", load.getTitle(), status);

        User client = load.getClient();

        // Send real-time notification
        if (realtimeEnabled) {
            realTimeNotificationService.sendLoadStatusUpdateNotification(
                client.getId(),
                load.getTitle(),
                status
            );
        }

        // Send email notification
        if (emailEnabled && client.getEmail() != null) {
            sendLoadStatusUpdateEmail(client, load, status);
        }

        // Send SMS notification
        if (smsEnabled && client.getPhoneNumber() != null) {
            smsService.sendLoadStatusUpdateSms(
                client.getPhoneNumber(),
                load.getTitle(),
                status
            );
        }
    }

    /**
     * Send fleet-related notifications (insurance expiry, permit renewal, etc.)
     */
    public void sendFleetNotification(User user, String title, String message, String type, String severity) {
        log.info("Sending fleet notification to user {}: {} - Severity: {}", user.getUsername(), title, severity);

        // Determine priority based on severity
        String priority = mapSeverityToPriority(severity);

        // Send persistent notification
        if (persistentEnabled) {
            persistentNotificationService.createNotification(
                user.getId(),
                title,
                message,
                type,
                priority,
                Map.of("severity", severity),
                null,
                "FLEET",
                getExpiryDateBasedOnSeverity(severity)
            );
        }

        // Send real-time notification
        if (realtimeEnabled) {
            realTimeNotificationService.sendNotificationToUser(
                user.getId(),
                NotificationDto.builder()
                    .title(title)
                    .message(message)
                    .type(type)
                    .timestamp(java.time.LocalDateTime.now())
                    .data(Map.of("severity", severity, "priority", priority))
                    .build()
            );
        }

        // Send email notification for critical and high severity alerts
        if (emailEnabled && user.getEmail() != null &&
            ("CRITICAL".equals(severity) || "HIGH".equals(severity) || "WARNING".equals(severity))) {
            try {
                sendFleetAlertEmail(user, title, message, severity);
            } catch (Exception e) {
                log.error("Failed to send fleet alert email to: {}", user.getEmail(), e);
            }
        }

        // Send SMS notification for critical alerts
        if (smsEnabled && user.getPhoneNumber() != null && "CRITICAL".equals(severity)) {
            try {
                smsService.sendFleetAlertSms(user.getPhoneNumber(), title, message);
            } catch (Exception e) {
                log.error("Failed to send fleet alert SMS to: {}", user.getPhoneNumber(), e);
            }
        }
    }
    
    private void sendLoadPostedEmail(User transporter, Load load) {
        try {
            Map<String, Object> variables = emailTemplateService.createLoadPostedVariables(
                transporter.getFirstName() + " " + transporter.getLastName(),
                load.getTitle(),
                load.getPickupLocation(),
                load.getDeliveryLocation(),
                load.getWeight() != null ? load.getWeight().toString() : "N/A",
                load.getPickupDate() != null ? load.getPickupDate().format(dateFormatter) : "TBD",
                load.getDeliveryDate() != null ? load.getDeliveryDate().format(dateFormatter) : "TBD",
                load.getLoadType() != null ? load.getLoadType().toString() : "General",
                load.getDescription() != null ? load.getDescription() : "",
                baseUrl + "/loads/" + load.getId() + "/bid",
                baseUrl + "/unsubscribe",
                baseUrl + "/support"
            );

            String htmlContent = emailTemplateService.processTemplate("load-posted", variables);
            sendHtmlEmail(transporter.getEmail(), "New Load Available - LogiPool", htmlContent);

        } catch (Exception e) {
            log.error("Failed to send load posted email to: {}", transporter.getEmail(), e);
        }
    }

    private void sendBidReceivedEmail(User client, Bid bid) {
        try {
            Map<String, Object> variables = emailTemplateService.createBidReceivedVariables(
                client.getFirstName() + " " + client.getLastName(),
                bid.getLoad().getTitle(),
                currencyFormat.format(bid.getAmount()),
                bid.getCompany().getName(),
                "4.5", // TODO: Get actual rating
                "23", // TODO: Get actual review count
                bid.getCompany().getVerificationStatus() != null ? "✅ Verified" : "⚠️ Unverified",
                bid.getEstimatedDeliveryTime() != null ? bid.getEstimatedDeliveryTime().format(dateFormatter) : "TBD",
                bid.getCreatedAt().format(dateTimeFormatter),
                "No additional message", // TODO: Add message field to Bid entity
                baseUrl + "/bids/" + bid.getId() + "/accept",
                baseUrl + "/loads/" + bid.getLoad().getId() + "/bids",
                baseUrl + "/unsubscribe",
                baseUrl + "/support"
            );

            String htmlContent = emailTemplateService.processTemplate("bid-received", variables);
            sendHtmlEmail(client.getEmail(), "New Bid Received - LogiPool", htmlContent);

        } catch (Exception e) {
            log.error("Failed to send bid received email to: {}", client.getEmail(), e);
        }
    }

    private void sendBidAcceptedEmail(User transporter, Bid bid) {
        try {
            Map<String, Object> variables = emailTemplateService.createBidAcceptedVariables(
                transporter.getFirstName() + " " + transporter.getLastName(),
                bid.getLoad().getTitle(),
                currencyFormat.format(bid.getAmount()),
                bid.getLoad().getClient().getFirstName() + " " + bid.getLoad().getClient().getLastName(),
                bid.getLoad().getPickupLocation(),
                bid.getLoad().getDeliveryLocation(),
                bid.getLoad().getPickupDate() != null ? bid.getLoad().getPickupDate().format(dateFormatter) : "TBD",
                bid.getLoad().getDeliveryDate() != null ? bid.getLoad().getDeliveryDate().format(dateFormatter) : "TBD",
                bid.getLoad().getWeight() != null ? bid.getLoad().getWeight().toString() : "N/A",
                bid.getLoad().getClient().getEmail(),
                bid.getLoad().getClient().getPhoneNumber() != null ? bid.getLoad().getClient().getPhoneNumber() : "N/A",
                baseUrl + "/jobs/" + bid.getLoad().getId(),
                baseUrl + "/contact/client/" + bid.getLoad().getClient().getId(),
                baseUrl + "/support"
            );

            String htmlContent = emailTemplateService.processTemplate("bid-accepted", variables);
            sendHtmlEmail(transporter.getEmail(), "Congratulations! Bid Accepted - LogiPool", htmlContent);

        } catch (Exception e) {
            log.error("Failed to send bid accepted email to: {}", transporter.getEmail(), e);
        }
    }

    private void sendBidRejectedEmail(User transporter, Bid bid) {
        try {
            String subject = "Bid Update - LogiPool";
            String message = String.format(
                "Thank you for your interest in the load '%s'. While your bid was not selected this time, " +
                "we encourage you to continue bidding on other opportunities. Keep up the great work!",
                bid.getLoad().getTitle()
            );

            sendSimpleEmail(transporter.getEmail(), subject, message);

        } catch (Exception e) {
            log.error("Failed to send bid rejected email to: {}", transporter.getEmail(), e);
        }
    }

    private void sendLoadStatusUpdateEmail(User client, Load load, String status) {
        try {
            String subject = "Load Status Update - LogiPool";
            String message = String.format(
                "Hello %s,\n\n" +
                "Your load '%s' status has been updated to: %s\n\n" +
                "You can view the full details and track progress at: %s\n\n" +
                "Best regards,\n" +
                "The LogiPool Team",
                client.getFirstName(),
                load.getTitle(),
                status,
                baseUrl + "/loads/" + load.getId()
            );

            sendSimpleEmail(client.getEmail(), subject, message);

        } catch (Exception e) {
            log.error("Failed to send load status update email to: {}", client.getEmail(), e);
        }
    }



    public void sendPaymentCompletedNotification(Payment payment) {
        log.info("Sending payment completed notification for payment: {}", payment.getTransactionId());

        User payee = payment.getPayee();
        User payer = payment.getPayer();

        // Send notification to payee
        sendNotification(payee, "Payment Received",
                String.format("You have received a payment of %s for load '%s'",
                        currencyFormat.format(payment.getNetAmount()), payment.getLoad().getTitle()),
                "PAYMENT_COMPLETED");

        // Send notification to payer
        sendNotification(payer, "Payment Sent",
                String.format("Your payment of %s for load '%s' has been completed",
                        currencyFormat.format(payment.getAmount()), payment.getLoad().getTitle()),
                "PAYMENT_COMPLETED");

        // Send email notifications
        if (emailEnabled && payee.getEmail() != null) {
            sendPaymentCompletedEmail(payee, payment, true);
        }

        if (emailEnabled && payer.getEmail() != null) {
            sendPaymentCompletedEmail(payer, payment, false);
        }
    }

    public void sendPaymentFailedNotification(Payment payment) {
        log.info("Sending payment failed notification for payment: {}", payment.getTransactionId());

        User payer = payment.getPayer();

        // Send notification
        sendNotification(payer, "Payment Failed",
                String.format("Your payment of %s for load '%s' could not be processed",
                        currencyFormat.format(payment.getAmount()), payment.getLoad().getTitle()),
                "PAYMENT_FAILED");

        // Send email notification
        if (emailEnabled && payer.getEmail() != null) {
            sendPaymentFailedEmail(payer, payment);
        }
    }

    public void sendPaymentCancelledNotification(Payment payment) {
        log.info("Sending payment cancelled notification for payment: {}", payment.getTransactionId());

        User payee = payment.getPayee();

        // Send notification
        sendNotification(payee, "Payment Cancelled",
                String.format("The payment of %s for load '%s' has been cancelled",
                        currencyFormat.format(payment.getAmount()), payment.getLoad().getTitle()),
                "PAYMENT_CANCELLED");

        // Send email notification
        if (emailEnabled && payee.getEmail() != null) {
            sendPaymentCancelledEmail(payee, payment);
        }
    }

    private void sendPaymentCreatedEmail(User payee, Payment payment) {
        try {
            String subject = "Payment Created - LogiPool";
            String message = String.format(
                "Hello %s,\n\n" +
                "A payment of %s has been created for your completed load '%s'.\n\n" +
                "Payment Details:\n" +
                "- Amount: %s\n" +
                "- Commission: %s\n" +
                "- Net Amount: %s\n" +
                "- Transaction ID: %s\n\n" +
                "The payment is currently pending and will be processed soon.\n\n" +
                "Best regards,\n" +
                "The LogiPool Team",
                payee.getFirstName(),
                currencyFormat.format(payment.getAmount()),
                payment.getLoad().getTitle(),
                currencyFormat.format(payment.getAmount()),
                currencyFormat.format(payment.getCommissionAmount()),
                currencyFormat.format(payment.getNetAmount()),
                payment.getTransactionId()
            );

            sendSimpleEmail(payee.getEmail(), subject, message);

        } catch (Exception e) {
            log.error("Failed to send payment created email to: {}", payee.getEmail(), e);
        }
    }

    private void sendPaymentCompletedEmail(User user, Payment payment, boolean isPayee) {
        try {
            String subject = "Payment Completed - LogiPool";
            String role = isPayee ? "received" : "sent";
            String message = String.format(
                "Hello %s,\n\n" +
                "Payment has been successfully completed for load '%s'.\n\n" +
                "Payment Details:\n" +
                "- Amount %s: %s\n" +
                "- Transaction ID: %s\n" +
                "- Completed At: %s\n\n" +
                "Thank you for using LogiPool!\n\n" +
                "Best regards,\n" +
                "The LogiPool Team",
                user.getFirstName(),
                payment.getLoad().getTitle(),
                role,
                currencyFormat.format(isPayee ? payment.getNetAmount() : payment.getAmount()),
                payment.getTransactionId(),
                payment.getPaidAt() != null ? payment.getPaidAt().format(dateTimeFormatter) : "N/A"
            );

            sendSimpleEmail(user.getEmail(), subject, message);

        } catch (Exception e) {
            log.error("Failed to send payment completed email to: {}", user.getEmail(), e);
        }
    }

    private void sendPaymentFailedEmail(User payer, Payment payment) {
        try {
            String subject = "Payment Failed - LogiPool";
            String message = String.format(
                "Hello %s,\n\n" +
                "Unfortunately, your payment for load '%s' could not be processed.\n\n" +
                "Payment Details:\n" +
                "- Amount: %s\n" +
                "- Transaction ID: %s\n\n" +
                "Please try again or contact our support team for assistance.\n\n" +
                "Best regards,\n" +
                "The LogiPool Team",
                payer.getFirstName(),
                payment.getLoad().getTitle(),
                currencyFormat.format(payment.getAmount()),
                payment.getTransactionId()
            );

            sendSimpleEmail(payer.getEmail(), subject, message);

        } catch (Exception e) {
            log.error("Failed to send payment failed email to: {}", payer.getEmail(), e);
        }
    }

    private void sendPaymentCancelledEmail(User payee, Payment payment) {
        try {
            String subject = "Payment Cancelled - LogiPool";
            String message = String.format(
                "Hello %s,\n\n" +
                "The payment for load '%s' has been cancelled.\n\n" +
                "Payment Details:\n" +
                "- Amount: %s\n" +
                "- Transaction ID: %s\n\n" +
                "If you have any questions, please contact our support team.\n\n" +
                "Best regards,\n" +
                "The LogiPool Team",
                payee.getFirstName(),
                payment.getLoad().getTitle(),
                currencyFormat.format(payment.getAmount()),
                payment.getTransactionId()
            );

            sendSimpleEmail(payee.getEmail(), subject, message);

        } catch (Exception e) {
            log.error("Failed to send payment cancelled email to: {}", payee.getEmail(), e);
        }
    }

    @Async("emailTaskExecutor")
    public void sendHtmlEmail(String to, String subject, String htmlContent) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(htmlContent, true);
            helper.setFrom("<EMAIL>");

            mailSender.send(message);
            log.info("HTML email sent successfully to: {}", to);

        } catch (Exception e) {
            log.error("Failed to send HTML email to: {}", to, e);
        }
    }

    @Async("emailTaskExecutor")
    private void sendSimpleEmail(String to, String subject, String text) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, false, "UTF-8");

            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(text, false);
            helper.setFrom("<EMAIL>");

            mailSender.send(message);
            log.info("Simple email sent successfully to: {}", to);

        } catch (Exception e) {
            log.error("Failed to send simple email to: {}", to, e);
        }
    }

    // Invoice notification methods

    public void sendInvoiceGeneratedNotification(Invoice invoice) {
        log.info("Sending invoice generated notification for invoice: {}", invoice.getInvoiceNumber());

        User client = invoice.getClient();

        // Send notification
        sendNotification(client, "Invoice Generated",
                String.format("Invoice %s has been generated for load '%s'",
                        invoice.getInvoiceNumber(), invoice.getLoad().getTitle()),
                "INVOICE_GENERATED");

        // Send email notification
        if (emailEnabled && client.getEmail() != null) {
            sendInvoiceGeneratedEmail(client, invoice);
        }
    }

    public void sendInvoiceSentNotification(Invoice invoice) {
        log.info("Sending invoice sent notification for invoice: {}", invoice.getInvoiceNumber());

        User client = invoice.getClient();

        // Send notification
        sendNotification(client, "Invoice Sent",
                String.format("Invoice %s for load '%s' has been sent. Amount: %s",
                        invoice.getInvoiceNumber(),
                        invoice.getLoad().getTitle(),
                        currencyFormat.format(invoice.getTotalAmount())),
                "INVOICE_SENT");

        // Send email notification
        if (emailEnabled && client.getEmail() != null) {
            sendInvoiceSentEmail(client, invoice);
        }
    }

    private void sendInvoiceGeneratedEmail(User client, Invoice invoice) {
        try {
            String subject = "Invoice Generated - LogiPool";
            String message = String.format(
                "Hello %s,\n\n" +
                "An invoice has been generated for your completed load.\n\n" +
                "Invoice Details:\n" +
                "- Invoice Number: %s\n" +
                "- Load: %s\n" +
                "- Amount: %s\n" +
                "- Due Date: %s\n\n" +
                "You can view the invoice details at: %s\n\n" +
                "Best regards,\n" +
                "The LogiPool Team",
                client.getFirstName(),
                invoice.getInvoiceNumber(),
                invoice.getLoad().getTitle(),
                currencyFormat.format(invoice.getTotalAmount()),
                invoice.getDueDate().format(dateFormatter),
                baseUrl + "/invoices/" + invoice.getId()
            );

            sendSimpleEmail(client.getEmail(), subject, message);

        } catch (Exception e) {
            log.error("Failed to send invoice generated email to: {}", client.getEmail(), e);
        }
    }

    private void sendInvoiceSentEmail(User client, Invoice invoice) {
        try {
            String subject = "Invoice Sent - LogiPool";
            String message = String.format(
                "Hello %s,\n\n" +
                "Your invoice is ready for payment.\n\n" +
                "Invoice Details:\n" +
                "- Invoice Number: %s\n" +
                "- Load: %s\n" +
                "- Amount: %s\n" +
                "- Due Date: %s\n\n" +
                "Please make payment by the due date to avoid any late fees.\n" +
                "You can view and pay the invoice at: %s\n\n" +
                "Best regards,\n" +
                "The LogiPool Team",
                client.getFirstName(),
                invoice.getInvoiceNumber(),
                invoice.getLoad().getTitle(),
                currencyFormat.format(invoice.getTotalAmount()),
                invoice.getDueDate().format(dateFormatter),
                baseUrl + "/invoices/" + invoice.getId() + "/pay"
            );

            sendSimpleEmail(client.getEmail(), subject, message);

        } catch (Exception e) {
            log.error("Failed to send invoice sent email to: {}", client.getEmail(), e);
        }
    }

    // Location permission notification methods

    public void sendLocationPermissionRequestNotification(LocationPermission permission) {
        log.info("Sending location permission request notification to driver: {}", permission.getDriver().getUsername());

        User driver = permission.getDriver();

        // Send notification
        sendNotification(driver, "Location Permission Request",
                String.format("Company '%s' has requested permission to track your location",
                        permission.getCompany().getName()),
                "LOCATION_PERMISSION_REQUEST");

        // Send email notification
        if (emailEnabled && driver.getEmail() != null) {
            sendLocationPermissionRequestEmail(driver, permission);
        }
    }

    public void sendLocationPermissionGrantedNotification(LocationPermission permission) {
        log.info("Sending location permission granted notification to company: {}", permission.getCompany().getName());

        User companyOwner = permission.getCompany().getUser();

        // Send notification
        sendNotification(companyOwner, "Location Permission Granted",
                String.format("Driver '%s' has granted location tracking permission",
                        permission.getDriver().getFirstName() + " " + permission.getDriver().getLastName()),
                "LOCATION_PERMISSION_GRANTED");

        // Send email notification
        if (emailEnabled && companyOwner.getEmail() != null) {
            sendLocationPermissionGrantedEmail(companyOwner, permission);
        }
    }

    public void sendLocationPermissionRevokedNotification(LocationPermission permission) {
        log.info("Sending location permission revoked notification");

        User driver = permission.getDriver();
        User companyOwner = permission.getCompany().getUser();

        // Send notification to driver
        sendNotification(driver, "Location Permission Revoked",
                String.format("Your location permission for company '%s' has been revoked",
                        permission.getCompany().getName()),
                "LOCATION_PERMISSION_REVOKED");

        // Send notification to company
        sendNotification(companyOwner, "Location Permission Revoked",
                String.format("Location permission for driver '%s' has been revoked",
                        driver.getFirstName() + " " + driver.getLastName()),
                "LOCATION_PERMISSION_REVOKED");

        // Send email notifications
        if (emailEnabled) {
            if (driver.getEmail() != null) {
                sendLocationPermissionRevokedEmail(driver, permission, true);
            }
            if (companyOwner.getEmail() != null) {
                sendLocationPermissionRevokedEmail(companyOwner, permission, false);
            }
        }
    }

    private void sendLocationPermissionRequestEmail(User driver, LocationPermission permission) {
        try {
            String subject = "Location Permission Request - LogiPool";
            String message = String.format(
                "Hello %s,\n\n" +
                "Company '%s' has requested permission to track your location.\n\n" +
                "Permission Details:\n" +
                "- Company: %s\n" +
                "- Permission Type: %s\n" +
                "- Real-time Tracking: %s\n" +
                "- Historical Data: %s\n" +
                "- Client Access: %s\n" +
                "- Valid From: %s\n" +
                "- Valid Until: %s\n\n" +
                "Please review and respond to this request at: %s\n\n" +
                "Best regards,\n" +
                "The LogiPool Team",
                driver.getFirstName(),
                permission.getCompany().getName(),
                permission.getCompany().getName(),
                permission.getPermissionType(),
                permission.getAllowRealTimeTracking() ? "Yes" : "No",
                permission.getAllowHistoricalData() ? "Yes" : "No",
                permission.getAllowClientAccess() ? "Yes" : "No",
                permission.getValidFrom() != null ? permission.getValidFrom().format(dateFormatter) : "Immediately",
                permission.getValidUntil() != null ? permission.getValidUntil().format(dateFormatter) : "No expiry",
                baseUrl + "/location-permissions/" + permission.getId()
            );

            sendSimpleEmail(driver.getEmail(), subject, message);

        } catch (Exception e) {
            log.error("Failed to send location permission request email to: {}", driver.getEmail(), e);
        }
    }

    private void sendLocationPermissionGrantedEmail(User companyOwner, LocationPermission permission) {
        try {
            String subject = "Location Permission Granted - LogiPool";
            String message = String.format(
                "Hello %s,\n\n" +
                "Great news! Driver '%s' has granted location tracking permission.\n\n" +
                "Permission Details:\n" +
                "- Driver: %s %s\n" +
                "- Permission Type: %s\n" +
                "- Real-time Tracking: %s\n" +
                "- Historical Data: %s\n" +
                "- Valid From: %s\n" +
                "- Valid Until: %s\n\n" +
                "You can now track this driver's location at: %s\n\n" +
                "Best regards,\n" +
                "The LogiPool Team",
                companyOwner.getFirstName(),
                permission.getDriver().getFirstName() + " " + permission.getDriver().getLastName(),
                permission.getDriver().getFirstName(),
                permission.getDriver().getLastName(),
                permission.getPermissionType(),
                permission.getAllowRealTimeTracking() ? "Yes" : "No",
                permission.getAllowHistoricalData() ? "Yes" : "No",
                permission.getValidFrom() != null ? permission.getValidFrom().format(dateFormatter) : "Immediately",
                permission.getValidUntil() != null ? permission.getValidUntil().format(dateFormatter) : "No expiry",
                baseUrl + "/tracking/drivers/" + permission.getDriver().getId()
            );

            sendSimpleEmail(companyOwner.getEmail(), subject, message);

        } catch (Exception e) {
            log.error("Failed to send location permission granted email to: {}", companyOwner.getEmail(), e);
        }
    }

    private void sendLocationPermissionRevokedEmail(User recipient, LocationPermission permission, boolean isDriver) {
        try {
            String subject = "Location Permission Revoked - LogiPool";
            String message;

            if (isDriver) {
                message = String.format(
                    "Hello %s,\n\n" +
                    "Your location permission for company '%s' has been revoked.\n\n" +
                    "Revocation Details:\n" +
                    "- Company: %s\n" +
                    "- Revoked By: %s\n" +
                    "- Reason: %s\n" +
                    "- Revoked At: %s\n\n" +
                    "Your location will no longer be shared with this company.\n\n" +
                    "Best regards,\n" +
                    "The LogiPool Team",
                    recipient.getFirstName(),
                    permission.getCompany().getName(),
                    permission.getCompany().getName(),
                    permission.getRevokedBy() != null ? permission.getRevokedBy().getUsername() : "System",
                    permission.getRevokeReason() != null ? permission.getRevokeReason() : "No reason provided",
                    permission.getRevokedAt() != null ? permission.getRevokedAt().format(dateTimeFormatter) : "Unknown"
                );
            } else {
                message = String.format(
                    "Hello %s,\n\n" +
                    "Location permission for driver '%s' has been revoked.\n\n" +
                    "Revocation Details:\n" +
                    "- Driver: %s %s\n" +
                    "- Revoked By: %s\n" +
                    "- Reason: %s\n" +
                    "- Revoked At: %s\n\n" +
                    "You will no longer have access to this driver's location data.\n\n" +
                    "Best regards,\n" +
                    "The LogiPool Team",
                    recipient.getFirstName(),
                    permission.getDriver().getFirstName() + " " + permission.getDriver().getLastName(),
                    permission.getDriver().getFirstName(),
                    permission.getDriver().getLastName(),
                    permission.getRevokedBy() != null ? permission.getRevokedBy().getUsername() : "System",
                    permission.getRevokeReason() != null ? permission.getRevokeReason() : "No reason provided",
                    permission.getRevokedAt() != null ? permission.getRevokedAt().format(dateTimeFormatter) : "Unknown"
                );
            }

            sendSimpleEmail(recipient.getEmail(), subject, message);

        } catch (Exception e) {
            log.error("Failed to send location permission revoked email to: {}", recipient.getEmail(), e);
        }
    }

    // Load lifecycle notification methods

    public void sendLoadDeliveredNotification(Load load) {
        log.info("Sending load delivered notification for load: {}", load.getId());

        User client = load.getClient();

        // Send notification to client
        sendNotification(client, "Load Delivered",
                String.format("Your load '%s' has been delivered successfully",
                        load.getTitle()),
                "LOAD_DELIVERED");

        // Send email notification
        if (emailEnabled && client.getEmail() != null) {
            sendLoadDeliveredEmail(client, load);
        }

        // Notify company if assigned
        if (load.getAssignedCompany() != null) {
            User companyOwner = load.getAssignedCompany().getUser();
            sendNotification(companyOwner, "Load Delivered",
                    String.format("Load '%s' has been delivered successfully",
                            load.getTitle()),
                    "LOAD_DELIVERED");
        }
    }

    public void sendDocumentsVerifiedNotification(Load load) {
        log.info("Sending documents verified notification for load: {}", load.getId());

        User client = load.getClient();

        // Send notification to client
        sendNotification(client, "Documents Verified",
                String.format("All documents for load '%s' have been verified",
                        load.getTitle()),
                "DOCUMENTS_VERIFIED");

        // Send email notification
        if (emailEnabled && client.getEmail() != null) {
            sendDocumentsVerifiedEmail(client, load);
        }
    }

    public void sendLoadCompletedNotification(Load load) {
        log.info("Sending load completed notification for load: {}", load.getId());

        User client = load.getClient();

        // Send notification to client
        sendNotification(client, "Load Completed",
                String.format("Load '%s' has been completed successfully",
                        load.getTitle()),
                "LOAD_COMPLETED");

        // Send email notification
        if (emailEnabled && client.getEmail() != null) {
            sendLoadCompletedEmail(client, load);
        }

        // Notify company if assigned
        if (load.getAssignedCompany() != null) {
            User companyOwner = load.getAssignedCompany().getUser();
            sendNotification(companyOwner, "Load Completed",
                    String.format("Load '%s' has been completed successfully",
                            load.getTitle()),
                    "LOAD_COMPLETED");
        }
    }

    public void sendPaymentCreatedNotification(Payment payment) {
        log.info("Sending payment created notification for payment: {}", payment.getId());

        User payer = payment.getPayer();
        User payee = payment.getPayee();

        // Send notification to payer
        sendNotification(payer, "Payment Due",
                String.format("Payment of %s is due for load '%s'",
                        currencyFormat.format(payment.getAmount()),
                        payment.getLoad().getTitle()),
                "PAYMENT_DUE");

        // Send notification to payee
        sendNotification(payee, "Payment Pending",
                String.format("Payment of %s is pending for load '%s'",
                        currencyFormat.format(payment.getNetAmount()),
                        payment.getLoad().getTitle()),
                "PAYMENT_PENDING");

        // Send email notifications
        if (emailEnabled) {
            if (payer.getEmail() != null) {
                sendPaymentDueEmail(payer, payment);
            }
            if (payee.getEmail() != null) {
                sendPaymentPendingEmail(payee, payment);
            }
        }
    }

    private void sendLoadDeliveredEmail(User client, Load load) {
        try {
            String subject = "Load Delivered - LogiPool";
            String message = String.format(
                "Hello %s,\n\n" +
                "Great news! Your load has been delivered successfully.\n\n" +
                "Load Details:\n" +
                "- Title: %s\n" +
                "- From: %s\n" +
                "- To: %s\n" +
                "- Delivered At: %s\n" +
                "- Assigned Company: %s\n\n" +
                "You can view the delivery details and documents at: %s\n\n" +
                "Best regards,\n" +
                "The LogiPool Team",
                client.getFirstName(),
                load.getTitle(),
                load.getPickupLocation(),
                load.getDeliveryLocation(),
                load.getDeliveredAt() != null ? load.getDeliveredAt().format(dateTimeFormatter) : "Just now",
                load.getAssignedCompany() != null ? load.getAssignedCompany().getName() : "N/A",
                baseUrl + "/loads/" + load.getId()
            );

            sendSimpleEmail(client.getEmail(), subject, message);

        } catch (Exception e) {
            log.error("Failed to send load delivered email to: {}", client.getEmail(), e);
        }
    }

    private void sendDocumentsVerifiedEmail(User client, Load load) {
        try {
            String subject = "Documents Verified - LogiPool";
            String message = String.format(
                "Hello %s,\n\n" +
                "All required documents for your load have been verified.\n\n" +
                "Load Details:\n" +
                "- Title: %s\n" +
                "- From: %s\n" +
                "- To: %s\n\n" +
                "Your load is now ready for final processing and payment.\n" +
                "You can view the verified documents at: %s\n\n" +
                "Best regards,\n" +
                "The LogiPool Team",
                client.getFirstName(),
                load.getTitle(),
                load.getPickupLocation(),
                load.getDeliveryLocation(),
                baseUrl + "/loads/" + load.getId() + "/documents"
            );

            sendSimpleEmail(client.getEmail(), subject, message);

        } catch (Exception e) {
            log.error("Failed to send documents verified email to: {}", client.getEmail(), e);
        }
    }

    private void sendLoadCompletedEmail(User client, Load load) {
        try {
            String subject = "Load Completed - LogiPool";
            String message = String.format(
                "Hello %s,\n\n" +
                "Your load has been completed successfully!\n\n" +
                "Load Details:\n" +
                "- Title: %s\n" +
                "- From: %s\n" +
                "- To: %s\n" +
                "- Completed At: %s\n\n" +
                "Thank you for using LogiPool for your transportation needs.\n" +
                "You can view the complete load details at: %s\n\n" +
                "We'd love to hear your feedback: %s\n\n" +
                "Best regards,\n" +
                "The LogiPool Team",
                client.getFirstName(),
                load.getTitle(),
                load.getPickupLocation(),
                load.getDeliveryLocation(),
                load.getCompletedAt() != null ? load.getCompletedAt().format(dateTimeFormatter) : "Just now",
                baseUrl + "/loads/" + load.getId(),
                baseUrl + "/feedback/" + load.getId()
            );

            sendSimpleEmail(client.getEmail(), subject, message);

        } catch (Exception e) {
            log.error("Failed to send load completed email to: {}", client.getEmail(), e);
        }
    }

    private void sendPaymentDueEmail(User payer, Payment payment) {
        try {
            String subject = "Payment Due - LogiPool";
            String message = String.format(
                "Hello %s,\n\n" +
                "A payment is due for your completed load.\n\n" +
                "Payment Details:\n" +
                "- Amount: %s\n" +
                "- Load: %s\n" +
                "- Due Date: %s\n" +
                "- Payment ID: %s\n\n" +
                "Please make payment by the due date to avoid any late fees.\n" +
                "You can make payment at: %s\n\n" +
                "Best regards,\n" +
                "The LogiPool Team",
                payer.getFirstName(),
                currencyFormat.format(payment.getAmount()),
                payment.getLoad().getTitle(),
                payment.getDueDate() != null ? payment.getDueDate().format(dateFormatter) : "Immediate",
                payment.getId(),
                baseUrl + "/payments/" + payment.getId() + "/pay"
            );

            sendSimpleEmail(payer.getEmail(), subject, message);

        } catch (Exception e) {
            log.error("Failed to send payment due email to: {}", payer.getEmail(), e);
        }
    }

    private void sendPaymentPendingEmail(User payee, Payment payment) {
        try {
            String subject = "Payment Pending - LogiPool";
            String message = String.format(
                "Hello %s,\n\n" +
                "A payment is pending for your completed load.\n\n" +
                "Payment Details:\n" +
                "- Net Amount: %s\n" +
                "- Commission: %s\n" +
                "- Load: %s\n" +
                "- Expected Release: %s\n" +
                "- Payment ID: %s\n\n" +
                "The payment will be released once the client makes payment.\n" +
                "You can track payment status at: %s\n\n" +
                "Best regards,\n" +
                "The LogiPool Team",
                payee.getFirstName(),
                currencyFormat.format(payment.getNetAmount()),
                currencyFormat.format(payment.getCommissionAmount()),
                payment.getLoad().getTitle(),
                payment.getDueDate() != null ? payment.getDueDate().format(dateFormatter) : "Upon client payment",
                payment.getId(),
                baseUrl + "/payments/" + payment.getId()
            );

            sendSimpleEmail(payee.getEmail(), subject, message);

        } catch (Exception e) {
            log.error("Failed to send payment pending email to: {}", payee.getEmail(), e);
        }
    }

    // Vehicle and Equipment approval notification methods

    public void notifyAdminsAboutPendingVehicleApproval(zw.co.kanjan.logipool.entity.Vehicle vehicle) {
        log.info("Notifying admins about pending vehicle approval: {}", vehicle.getId());

        String title = "Vehicle Approval Request";
        String message = String.format("Vehicle %s %s from company '%s' is pending approval for public visibility",
                vehicle.getMake(), vehicle.getModel(), vehicle.getCompany().getName());

        // Send notification to all admins
        if (realtimeEnabled) {
            realTimeNotificationService.sendRoleBasedNotification(
                "ADMIN",
                title,
                message,
                "VEHICLE_APPROVAL_REQUEST",
                Map.of("vehicleId", vehicle.getId(), "companyName", vehicle.getCompany().getName())
            );
        }
    }

    public void notifyAdminsAboutPendingEquipmentApproval(zw.co.kanjan.logipool.entity.Equipment equipment) {
        log.info("Notifying admins about pending equipment approval: {}", equipment.getId());

        String title = "Equipment Approval Request";
        String message = String.format("Equipment %s from company '%s' is pending approval for public visibility",
                equipment.getName(), equipment.getCompany().getName());

        // Send notification to all admins
        if (realtimeEnabled) {
            realTimeNotificationService.sendRoleBasedNotification(
                "ADMIN",
                title,
                message,
                "EQUIPMENT_APPROVAL_REQUEST",
                Map.of("equipmentId", equipment.getId(), "companyName", equipment.getCompany().getName())
            );
        }
    }

    // Admin notification methods

    public void sendAdminNewLoadNotification(Load load) {
        log.info("Sending admin notification for new load: {}", load.getTitle());

        // Find all admin users
        List<User> admins = userRepository.findAdminUsers();

        String title = "New Load Posted";
        String message = String.format("A new load '%s' has been posted by %s. Pickup: %s → Delivery: %s",
                load.getTitle(),
                load.getClient().getFirstName() + " " + load.getClient().getLastName(),
                load.getPickupLocation(),
                load.getDeliveryLocation());

        for (User admin : admins) {
            // Send persistent notification
            if (persistentEnabled) {
                persistentNotificationService.createNotification(
                    admin.getId(),
                    title,
                    message,
                    "ADMIN_NEW_LOAD",
                    "MEDIUM",
                    Map.of(
                        "loadId", load.getId(),
                        "loadTitle", load.getTitle(),
                        "clientName", load.getClient().getFirstName() + " " + load.getClient().getLastName(),
                        "trackingNumber", load.getTrackingNumber()
                    ),
                    load.getId(),
                    "LOAD",
                    null
                );
            }

            // Send real-time notification
            if (realtimeEnabled) {
                realTimeNotificationService.sendNotificationToUser(
                    admin.getId(),
                    NotificationDto.builder()
                        .title(title)
                        .message(message)
                        .type("ADMIN_NEW_LOAD")
                        .timestamp(LocalDateTime.now())
                        .data(Map.of(
                            "loadId", load.getId(),
                            "loadTitle", load.getTitle(),
                            "trackingNumber", load.getTrackingNumber()
                        ))
                        .build()
                );
            }

            // Send email notification
            if (emailEnabled && admin.getEmail() != null) {
                sendAdminNewLoadEmail(admin, load);
            }
        }
    }

    private void sendAdminNewLoadEmail(User admin, Load load) {
        try {
            String subject = "New Load Posted - " + load.getTitle();
            String body = buildAdminNewLoadEmailBody(admin, load);
            sendSimpleEmail(admin.getEmail(), subject, body);
            log.info("Admin new load email sent to: {}", admin.getEmail());
        } catch (Exception e) {
            log.error("Failed to send admin new load email to: {}", admin.getEmail(), e);
        }
    }

    private String buildAdminNewLoadEmailBody(User admin, Load load) {
        return String.format("""
            Dear %s,

            A new load has been posted on LogiPool:

            Load Details:
            - Title: %s
            - Tracking Number: %s
            - Client: %s %s
            - Pickup Location: %s
            - Delivery Location: %s
            - Pickup Date: %s
            - Delivery Date: %s
            - Cargo Type: %s
            - Weight: %s %s
            - Priority: %s

            You can view and manage this load in the admin dashboard.

            Best regards,
            LogiPool Team
            """,
            admin.getFirstName(),
            load.getTitle(),
            load.getTrackingNumber(),
            load.getClient().getFirstName(),
            load.getClient().getLastName(),
            load.getPickupLocation(),
            load.getDeliveryLocation(),
            load.getPickupDate(),
            load.getDeliveryDate(),
            load.getCargoType(),
            load.getWeight(),
            load.getWeightUnit(),
            load.getPriority()
        );
    }

    private void sendFleetAlertEmail(User user, String title, String message, String severity) {
        try {
            String subject = String.format("[%s] %s - LogiPool Fleet Alert", severity, title);
            String emailMessage = String.format(
                "Hello %s,\n\n" +
                "%s\n\n" +
                "Severity: %s\n\n" +
                "Please take immediate action to ensure compliance and avoid any operational disruptions.\n\n" +
                "You can manage your fleet documents and compliance at: %s\n\n" +
                "Best regards,\n" +
                "The LogiPool Fleet Management Team",
                user.getFirstName(),
                message,
                severity,
                baseUrl + "/fleet/dashboard"
            );

            sendSimpleEmail(user.getEmail(), subject, emailMessage);

        } catch (Exception e) {
            log.error("Failed to send fleet alert email to: {}", user.getEmail(), e);
        }
    }

    private String mapSeverityToPriority(String severity) {
        switch (severity) {
            case "CRITICAL": return "CRITICAL";
            case "HIGH": return "HIGH";
            case "WARNING": return "MEDIUM";
            case "MEDIUM": return "MEDIUM";
            case "LOW": return "LOW";
            default: return "MEDIUM";
        }
    }

    private java.time.LocalDateTime getExpiryDateBasedOnSeverity(String severity) {
        java.time.LocalDateTime now = java.time.LocalDateTime.now();
        switch (severity) {
            case "CRITICAL": return now.plusDays(1); // Critical alerts expire in 1 day
            case "HIGH": return now.plusDays(3); // High alerts expire in 3 days
            case "WARNING": return now.plusDays(7); // Warning alerts expire in 1 week
            case "MEDIUM": return now.plusDays(14); // Medium alerts expire in 2 weeks
            case "LOW": return now.plusDays(30); // Low alerts expire in 1 month
            default: return now.plusDays(7);
        }
    }
}
