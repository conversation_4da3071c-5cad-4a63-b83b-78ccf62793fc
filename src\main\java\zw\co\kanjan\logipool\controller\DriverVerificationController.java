package zw.co.kanjan.logipool.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import zw.co.kanjan.logipool.dto.driver.DriverVerificationDto;
import zw.co.kanjan.logipool.service.DriverVerificationService;

import java.security.Principal;

@RestController
@RequestMapping("/api/driver-verification")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Driver Verification", description = "Driver verification management APIs")
public class DriverVerificationController {
    
    private final DriverVerificationService driverVerificationService;
    
    @GetMapping("/pending")
    @Operation(summary = "Get pending verifications", description = "Get all pending driver verification tasks")
    @PreAuthorize("hasRole('ADMIN') or hasRole('COMPANY_OWNER') or hasRole('COMPANY_ADMIN')")
    public ResponseEntity<Page<DriverVerificationDto.VerificationTaskResponse>> getPendingVerifications(
            Pageable pageable,
            Principal principal) {
        
        log.info("Getting pending verifications for user: {}", principal.getName());
        Page<DriverVerificationDto.VerificationTaskResponse> response = 
                driverVerificationService.getPendingVerifications(pageable, principal.getName());
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/company/{companyId}")
    @Operation(summary = "Get company verification tasks", description = "Get verification tasks for a specific company")
    @PreAuthorize("hasRole('ADMIN') or hasRole('COMPANY_OWNER') or hasRole('COMPANY_ADMIN')")
    public ResponseEntity<Page<DriverVerificationDto.VerificationTaskResponse>> getCompanyVerifications(
            @Parameter(description = "Company ID") @PathVariable Long companyId,
            Pageable pageable,
            Principal principal) {
        
        log.info("Getting verification tasks for company {} by user: {}", companyId, principal.getName());
        Page<DriverVerificationDto.VerificationTaskResponse> response = 
                driverVerificationService.getCompanyVerifications(companyId, pageable, principal.getName());
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/driver/{driverId}")
    @Operation(summary = "Get driver verification tasks", description = "Get all verification tasks for a specific driver")
    @PreAuthorize("hasRole('ADMIN') or hasRole('COMPANY_OWNER') or hasRole('COMPANY_ADMIN') or (hasRole('DRIVER') and #driverId == authentication.principal.id)")
    public ResponseEntity<DriverVerificationDto.VerificationSummaryResponse> getDriverVerifications(
            @Parameter(description = "Driver ID") @PathVariable Long driverId,
            Principal principal) {
        
        log.info("Getting verification tasks for driver {} by user: {}", driverId, principal.getName());
        DriverVerificationDto.VerificationSummaryResponse response = 
                driverVerificationService.getDriverVerifications(driverId, principal.getName());
        return ResponseEntity.ok(response);
    }
    
    @PutMapping("/verify")
    @Operation(summary = "Update verification status", description = "Update the verification status of a driver verification task")
    @PreAuthorize("hasRole('ADMIN') or hasRole('COMPANY_OWNER') or hasRole('COMPANY_ADMIN')")
    public ResponseEntity<DriverVerificationDto.VerificationTaskResponse> updateVerificationStatus(
            @Valid @RequestBody DriverVerificationDto.UpdateVerificationRequest request,
            Principal principal) {
        
        log.info("Updating verification {} for driver {} by user: {}", 
                request.getVerificationType(), request.getDriverId(), principal.getName());
        DriverVerificationDto.VerificationTaskResponse response = 
                driverVerificationService.updateVerificationStatus(request, principal.getName());
        return ResponseEntity.ok(response);
    }
    
    @PutMapping("/bulk-verify")
    @Operation(summary = "Bulk update verification status", description = "Update multiple verification statuses at once")
    @PreAuthorize("hasRole('ADMIN') or hasRole('COMPANY_OWNER') or hasRole('COMPANY_ADMIN')")
    public ResponseEntity<String> bulkUpdateVerificationStatus(
            @Valid @RequestBody DriverVerificationDto.BulkVerificationUpdateRequest request,
            Principal principal) {
        
        log.info("Bulk updating {} verification tasks by user: {}", 
                request.getVerificationUpdates().size(), principal.getName());
        int updatedCount = driverVerificationService.bulkUpdateVerificationStatus(request, principal.getName());
        return ResponseEntity.ok("Successfully updated " + updatedCount + " verification tasks");
    }
    
    @GetMapping("/stats")
    @Operation(summary = "Get verification statistics", description = "Get overall verification statistics")
    @PreAuthorize("hasRole('ADMIN') or hasRole('COMPANY_OWNER') or hasRole('COMPANY_ADMIN')")
    public ResponseEntity<DriverVerificationDto.VerificationStatsResponse> getVerificationStats(
            Principal principal) {
        
        log.info("Getting verification statistics for user: {}", principal.getName());
        DriverVerificationDto.VerificationStatsResponse response = 
                driverVerificationService.getVerificationStats(principal.getName());
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/stats/company/{companyId}")
    @Operation(summary = "Get company verification statistics", description = "Get verification statistics for a specific company")
    @PreAuthorize("hasRole('ADMIN') or hasRole('COMPANY_OWNER') or hasRole('COMPANY_ADMIN')")
    public ResponseEntity<DriverVerificationDto.VerificationStatsResponse> getCompanyVerificationStats(
            @Parameter(description = "Company ID") @PathVariable Long companyId,
            Principal principal) {
        
        log.info("Getting verification statistics for company {} by user: {}", companyId, principal.getName());
        DriverVerificationDto.VerificationStatsResponse response = 
                driverVerificationService.getCompanyVerificationStats(companyId, principal.getName());
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/overdue")
    @Operation(summary = "Get overdue verifications", description = "Get verification tasks that are overdue (pending for more than specified days)")
    @PreAuthorize("hasRole('ADMIN') or hasRole('COMPANY_OWNER') or hasRole('COMPANY_ADMIN')")
    public ResponseEntity<Page<DriverVerificationDto.VerificationTaskResponse>> getOverdueVerifications(
            @Parameter(description = "Days overdue threshold") @RequestParam(defaultValue = "7") int daysOverdue,
            Pageable pageable,
            Principal principal) {
        
        log.info("Getting verifications overdue by {} days for user: {}", daysOverdue, principal.getName());
        Page<DriverVerificationDto.VerificationTaskResponse> response = 
                driverVerificationService.getOverdueVerifications(daysOverdue, pageable, principal.getName());
        return ResponseEntity.ok(response);
    }
    
    @PostMapping("/driver/{driverId}/reset")
    @Operation(summary = "Reset driver verifications", description = "Reset all verification tasks for a driver to pending status")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<DriverVerificationDto.VerificationSummaryResponse> resetDriverVerifications(
            @Parameter(description = "Driver ID") @PathVariable Long driverId,
            Principal principal) {
        
        log.info("Resetting verification tasks for driver {} by admin: {}", driverId, principal.getName());
        DriverVerificationDto.VerificationSummaryResponse response = 
                driverVerificationService.resetDriverVerifications(driverId, principal.getName());
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/export")
    @Operation(summary = "Export verification data", description = "Export verification data for reporting purposes")
    @PreAuthorize("hasRole('ADMIN') or hasRole('COMPANY_OWNER') or hasRole('COMPANY_ADMIN')")
    public ResponseEntity<String> exportVerificationData(
            @Parameter(description = "Company ID (optional)") @RequestParam(required = false) Long companyId,
            @Parameter(description = "Export format") @RequestParam(defaultValue = "CSV") String format,
            Principal principal) {
        
        log.info("Exporting verification data in {} format for user: {}", format, principal.getName());
        String exportData = driverVerificationService.exportVerificationData(companyId, format, principal.getName());
        return ResponseEntity.ok(exportData);
    }
}
