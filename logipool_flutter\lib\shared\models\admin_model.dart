import 'package:json_annotation/json_annotation.dart';

part 'admin_model.g.dart';

@JsonSerializable()
class DashboardOverview {
  final int? totalUsers;
  final int? totalCompanies;
  final int? totalLoads;
  final int? totalBids;
  final int? activeLoads;
  final int? completedLoads;
  final int? pendingVerifications;
  final double? totalRevenue;
  final double? monthlyRevenue;
  final List<RecentActivity>? recentActivities;
  final SystemHealth? systemHealth;
  final DateTime? generatedAt;

  const DashboardOverview({
    this.totalUsers,
    this.totalCompanies,
    this.totalLoads,
    this.totalBids,
    this.activeLoads,
    this.completedLoads,
    this.pendingVerifications,
    this.totalRevenue,
    this.monthlyRevenue,
    this.recentActivities,
    this.systemHealth,
    this.generatedAt,
  });

  factory DashboardOverview.fromJson(Map<String, dynamic> json) =>
      _$DashboardOverviewFromJson(json);

  Map<String, dynamic> toJson() => _$DashboardOverviewToJson(this);
}

@JsonSerializable()
class RecentActivity {
  final String? type;
  final String? description;
  final String? entityType;
  final int? entityId;
  final String? performedBy;
  final DateTime? timestamp;

  const RecentActivity({
    this.type,
    this.description,
    this.entityType,
    this.entityId,
    this.performedBy,
    this.timestamp,
  });

  factory RecentActivity.fromJson(Map<String, dynamic> json) =>
      _$RecentActivityFromJson(json);

  Map<String, dynamic> toJson() => _$RecentActivityToJson(this);
}

@JsonSerializable()
class SystemHealth {
  final String? status;
  final double? cpuUsage;
  final double? memoryUsage;
  final double? diskUsage;
  final int? activeConnections;
  final double? responseTime;
  final DateTime? lastChecked;

  const SystemHealth({
    this.status,
    this.cpuUsage,
    this.memoryUsage,
    this.diskUsage,
    this.activeConnections,
    this.responseTime,
    this.lastChecked,
  });

  factory SystemHealth.fromJson(Map<String, dynamic> json) =>
      _$SystemHealthFromJson(json);

  Map<String, dynamic> toJson() => _$SystemHealthToJson(this);
}

@JsonSerializable()
class UserManagementResponse {
  final int? id;
  final String? username;
  final String? email;
  final String? firstName;
  final String? lastName;
  final String? phoneNumber;
  final String? status;
  final List<String>? roles;
  final String? companyName;
  final int? companyId;
  final bool? isVerified;
  final DateTime? createdAt;
  final DateTime? lastLoginAt;

  const UserManagementResponse({
    this.id,
    this.username,
    this.email,
    this.firstName,
    this.lastName,
    this.phoneNumber,
    this.status,
    this.roles,
    this.companyName,
    this.companyId,
    this.isVerified,
    this.createdAt,
    this.lastLoginAt,
  });

  factory UserManagementResponse.fromJson(Map<String, dynamic> json) =>
      _$UserManagementResponseFromJson(json);

  Map<String, dynamic> toJson() => _$UserManagementResponseToJson(this);
}

@JsonSerializable()
class CompanyManagementResponse {
  final int? id;
  final String? name;
  final String? registrationNumber;
  final String? email;
  final String? phoneNumber;
  final String? address;
  final String? verificationStatus;
  final String? ownerName;
  final String? ownerUsername;
  final int? ownerId;
  final int? totalVehicles;
  final int? totalLoads;
  final double? totalRevenue;
  final DateTime? createdAt;
  final DateTime? verifiedAt;

  const CompanyManagementResponse({
    this.id,
    this.name,
    this.registrationNumber,
    this.email,
    this.phoneNumber,
    this.address,
    this.verificationStatus,
    this.ownerName,
    this.ownerUsername,
    this.ownerId,
    this.totalVehicles,
    this.totalLoads,
    this.totalRevenue,
    this.createdAt,
    this.verifiedAt,
  });

  factory CompanyManagementResponse.fromJson(Map<String, dynamic> json) =>
      _$CompanyManagementResponseFromJson(json);

  Map<String, dynamic> toJson() => _$CompanyManagementResponseToJson(this);

  // Helper methods for admin management
  String get formattedRevenue {
    if (totalRevenue == null) {
      return '\$0.00';
    }
    return '\$${totalRevenue!.toStringAsFixed(2)}';
  }

  String get formattedCreatedDate {
    if (createdAt == null) {
      return 'Unknown';
    }
    return '${createdAt!.day}/${createdAt!.month}/${createdAt!.year}';
  }

  String get formattedVerifiedDate {
    if (verifiedAt == null) {
      return 'Not verified';
    }
    return '${verifiedAt!.day}/${verifiedAt!.month}/${verifiedAt!.year}';
  }

  bool get isVerified => verificationStatus?.toUpperCase() == 'VERIFIED';
  bool get isPending => verificationStatus?.toUpperCase() == 'PENDING';
  bool get isRejected => verificationStatus?.toUpperCase() == 'REJECTED';
  bool get isSuspended => verificationStatus?.toUpperCase() == 'SUSPENDED';
}

@JsonSerializable()
class LoadManagementResponse {
  final int? id;
  final String? title;
  final String? description;
  final String? pickupLocation;
  final String? deliveryLocation;
  final double? weight;
  final double? price;
  final String? status;
  final String? clientName;
  final String? clientUsername;
  final int? clientId;
  final String? assignedCompanyName;
  final int? assignedCompanyId;
  final int? totalBids;
  final bool? isVerified;
  final DateTime? createdAt;
  final DateTime? deliveryDate;

  const LoadManagementResponse({
    this.id,
    this.title,
    this.description,
    this.pickupLocation,
    this.deliveryLocation,
    this.weight,
    this.price,
    this.status,
    this.clientName,
    this.clientUsername,
    this.clientId,
    this.assignedCompanyName,
    this.assignedCompanyId,
    this.totalBids,
    this.isVerified,
    this.createdAt,
    this.deliveryDate,
  });

  factory LoadManagementResponse.fromJson(Map<String, dynamic> json) =>
      _$LoadManagementResponseFromJson(json);

  Map<String, dynamic> toJson() => _$LoadManagementResponseToJson(this);
}

@JsonSerializable()
class SystemAnalytics {
  final UserAnalytics? userAnalytics;
  final LoadAnalytics? loadAnalytics;
  final RevenueAnalytics? revenueAnalytics;
  final PerformanceAnalytics? performanceAnalytics;
  final DateTime? generatedAt;

  const SystemAnalytics({
    this.userAnalytics,
    this.loadAnalytics,
    this.revenueAnalytics,
    this.performanceAnalytics,
    this.generatedAt,
  });

  factory SystemAnalytics.fromJson(Map<String, dynamic> json) =>
      _$SystemAnalyticsFromJson(json);

  Map<String, dynamic> toJson() => _$SystemAnalyticsToJson(this);
}

@JsonSerializable()
class UserAnalytics {
  final int? totalUsers;
  final int? activeUsers;
  final int? newUsersThisMonth;
  final int? clientUsers;
  final int? transporterUsers;
  final int? adminUsers;
  final Map<String, int>? usersByStatus;
  final Map<String, int>? registrationTrends;

  const UserAnalytics({
    this.totalUsers,
    this.activeUsers,
    this.newUsersThisMonth,
    this.clientUsers,
    this.transporterUsers,
    this.adminUsers,
    this.usersByStatus,
    this.registrationTrends,
  });

  factory UserAnalytics.fromJson(Map<String, dynamic> json) =>
      _$UserAnalyticsFromJson(json);

  Map<String, dynamic> toJson() => _$UserAnalyticsToJson(this);
}

@JsonSerializable()
class LoadAnalytics {
  final int? totalLoads;
  final int? activeLoads;
  final int? completedLoads;
  final int? newLoadsThisMonth;
  final Map<String, int>? loadsByStatus;
  final Map<String, int>? loadTrends;
  final double? averageLoadValue;
  final double? totalLoadValue;

  const LoadAnalytics({
    this.totalLoads,
    this.activeLoads,
    this.completedLoads,
    this.newLoadsThisMonth,
    this.loadsByStatus,
    this.loadTrends,
    this.averageLoadValue,
    this.totalLoadValue,
  });

  factory LoadAnalytics.fromJson(Map<String, dynamic> json) =>
      _$LoadAnalyticsFromJson(json);

  Map<String, dynamic> toJson() => _$LoadAnalyticsToJson(this);
}

@JsonSerializable()
class RevenueAnalytics {
  final double? totalRevenue;
  final double? monthlyRevenue;
  final double? averageCommission;
  final double? totalCommissions;
  final Map<String, double>? revenueTrends;
  final Map<String, double>? commissionTrends;

  const RevenueAnalytics({
    this.totalRevenue,
    this.monthlyRevenue,
    this.averageCommission,
    this.totalCommissions,
    this.revenueTrends,
    this.commissionTrends,
  });

  factory RevenueAnalytics.fromJson(Map<String, dynamic> json) =>
      _$RevenueAnalyticsFromJson(json);

  Map<String, dynamic> toJson() => _$RevenueAnalyticsToJson(this);
}

@JsonSerializable()
class PerformanceAnalytics {
  final double? averageResponseTime;
  final int? totalApiCalls;
  final int? errorCount;
  final double? errorRate;
  final Map<String, int>? endpointUsage;
  final Map<String, double>? performanceMetrics;

  const PerformanceAnalytics({
    this.averageResponseTime,
    this.totalApiCalls,
    this.errorCount,
    this.errorRate,
    this.endpointUsage,
    this.performanceMetrics,
  });

  factory PerformanceAnalytics.fromJson(Map<String, dynamic> json) =>
      _$PerformanceAnalyticsFromJson(json);

  Map<String, dynamic> toJson() => _$PerformanceAnalyticsToJson(this);
}

@JsonSerializable()
class SystemConfigurationResponse {
  final Map<String, dynamic>? applicationSettings;
  final Map<String, dynamic>? securitySettings;
  final Map<String, dynamic>? notificationSettings;
  final Map<String, dynamic>? paymentSettings;
  final Map<String, dynamic>? trackingSettings;
  final Map<String, dynamic>? fileSettings;

  const SystemConfigurationResponse({
    this.applicationSettings,
    this.securitySettings,
    this.notificationSettings,
    this.paymentSettings,
    this.trackingSettings,
    this.fileSettings,
  });

  factory SystemConfigurationResponse.fromJson(Map<String, dynamic> json) =>
      _$SystemConfigurationResponseFromJson(json);

  Map<String, dynamic> toJson() => _$SystemConfigurationResponseToJson(this);
}

@JsonSerializable()
class AuditLogResponse {
  final int? id;
  final String? action;
  final String? entityType;
  final int? entityId;
  final String? details;
  final String? performedBy;
  final String? ipAddress;
  final String? userAgent;
  final DateTime? timestamp;

  const AuditLogResponse({
    this.id,
    this.action,
    this.entityType,
    this.entityId,
    this.details,
    this.performedBy,
    this.ipAddress,
    this.userAgent,
    this.timestamp,
  });

  factory AuditLogResponse.fromJson(Map<String, dynamic> json) =>
      _$AuditLogResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AuditLogResponseToJson(this);
}

@JsonSerializable()
class SystemAlertResponse {
  final int? id;
  final String? type;
  final String? severity;
  final String? title;
  final String? message;
  final String? source;
  final bool? acknowledged;
  final String? acknowledgedBy;
  final DateTime? acknowledgedAt;
  final DateTime? createdAt;

  const SystemAlertResponse({
    this.id,
    this.type,
    this.severity,
    this.title,
    this.message,
    this.source,
    this.acknowledged,
    this.acknowledgedBy,
    this.acknowledgedAt,
    this.createdAt,
  });

  factory SystemAlertResponse.fromJson(Map<String, dynamic> json) =>
      _$SystemAlertResponseFromJson(json);

  Map<String, dynamic> toJson() => _$SystemAlertResponseToJson(this);
}

@JsonSerializable()
class SystemBackupResponse {
  final String? backupId;
  final String? type;
  final String? status;
  final int? sizeInBytes;
  final String? location;
  final String? initiatedBy;
  final DateTime? initiatedAt;
  final DateTime? completedAt;

  const SystemBackupResponse({
    this.backupId,
    this.type,
    this.status,
    this.sizeInBytes,
    this.location,
    this.initiatedBy,
    this.initiatedAt,
    this.completedAt,
  });

  factory SystemBackupResponse.fromJson(Map<String, dynamic> json) =>
      _$SystemBackupResponseFromJson(json);

  Map<String, dynamic> toJson() => _$SystemBackupResponseToJson(this);
}

// Request DTOs
@JsonSerializable()
class UserStatusUpdateRequest {
  final String status;

  const UserStatusUpdateRequest({required this.status});

  factory UserStatusUpdateRequest.fromJson(Map<String, dynamic> json) =>
      _$UserStatusUpdateRequestFromJson(json);

  Map<String, dynamic> toJson() => _$UserStatusUpdateRequestToJson(this);
}

@JsonSerializable()
class UserRoleUpdateRequest {
  final List<String> roles;

  const UserRoleUpdateRequest({required this.roles});

  factory UserRoleUpdateRequest.fromJson(Map<String, dynamic> json) =>
      _$UserRoleUpdateRequestFromJson(json);

  Map<String, dynamic> toJson() => _$UserRoleUpdateRequestToJson(this);
}

@JsonSerializable()
class CompanyVerificationRequest {
  final int companyId;
  final String status;
  final String? verificationNotes;
  final String? rejectionReason;

  const CompanyVerificationRequest({
    required this.companyId,
    required this.status,
    this.verificationNotes,
    this.rejectionReason,
  });

  factory CompanyVerificationRequest.fromJson(Map<String, dynamic> json) =>
      _$CompanyVerificationRequestFromJson(json);

  Map<String, dynamic> toJson() => _$CompanyVerificationRequestToJson(this);
}

@JsonSerializable()
class CompanySuspensionRequest {
  final String reason;
  final String? notes;
  final int? suspensionDurationDays;

  const CompanySuspensionRequest({
    required this.reason,
    this.notes,
    this.suspensionDurationDays,
  });

  factory CompanySuspensionRequest.fromJson(Map<String, dynamic> json) =>
      _$CompanySuspensionRequestFromJson(json);

  Map<String, dynamic> toJson() => _$CompanySuspensionRequestToJson(this);
}

@JsonSerializable()
class CompanyUnsuspensionRequest {
  final String? reason;
  final String? notes;

  const CompanyUnsuspensionRequest({
    this.reason,
    this.notes,
  });

  factory CompanyUnsuspensionRequest.fromJson(Map<String, dynamic> json) =>
      _$CompanyUnsuspensionRequestFromJson(json);

  Map<String, dynamic> toJson() => _$CompanyUnsuspensionRequestToJson(this);
}

@JsonSerializable()
class LoadAssignmentRequest {
  final int companyId;

  const LoadAssignmentRequest({required this.companyId});

  factory LoadAssignmentRequest.fromJson(Map<String, dynamic> json) =>
      _$LoadAssignmentRequestFromJson(json);

  Map<String, dynamic> toJson() => _$LoadAssignmentRequestToJson(this);
}

@JsonSerializable()
class SystemConfigurationUpdateRequest {
  final Map<String, dynamic> settings;

  const SystemConfigurationUpdateRequest({required this.settings});

  factory SystemConfigurationUpdateRequest.fromJson(Map<String, dynamic> json) =>
      _$SystemConfigurationUpdateRequestFromJson(json);

  Map<String, dynamic> toJson() => _$SystemConfigurationUpdateRequestToJson(this);
}
