package zw.co.kanjan.logipool.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import zw.co.kanjan.logipool.entity.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface TaxClearanceRepository extends JpaRepository<TaxClearance, Long> {
    
    List<TaxClearance> findByCompany(Company company);
    
    Page<TaxClearance> findByCompany(Company company, Pageable pageable);
    
    List<TaxClearance> findByCompanyAndStatus(Company company, TaxClearance.ClearanceStatus status);
    
    List<TaxClearance> findByCompanyAndTaxType(Company company, TaxClearance.TaxType taxType);
    
    Page<TaxClearance> findByCompanyAndTaxType(Company company, TaxClearance.TaxType taxType, Pageable pageable);
    
    Optional<TaxClearance> findByCertificateNumber(String certificateNumber);
    
    List<TaxClearance> findByVehicle(Vehicle vehicle);
    
    List<TaxClearance> findByTruckHead(TruckHead truckHead);
    
    @Query("SELECT tc FROM TaxClearance tc WHERE tc.company = :company AND " +
           "(:status IS NULL OR tc.status = :status) AND " +
           "(:taxType IS NULL OR tc.taxType = :taxType) AND " +
           "(:authority IS NULL OR LOWER(tc.issuingAuthority) LIKE LOWER(CONCAT('%', :authority, '%'))) AND " +
           "(:certificateNumber IS NULL OR LOWER(tc.certificateNumber) LIKE LOWER(CONCAT('%', :certificateNumber, '%')))")
    Page<TaxClearance> findByCompanyWithFilters(
            @Param("company") Company company,
            @Param("status") TaxClearance.ClearanceStatus status,
            @Param("taxType") TaxClearance.TaxType taxType,
            @Param("authority") String authority,
            @Param("certificateNumber") String certificateNumber,
            Pageable pageable);
    
    @Query("SELECT tc FROM TaxClearance tc WHERE tc.expiryDate BETWEEN :startDate AND :endDate")
    List<TaxClearance> findByExpiryDateBetween(
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT tc FROM TaxClearance tc WHERE tc.company = :company AND " +
           "tc.expiryDate BETWEEN :startDate AND :endDate")
    List<TaxClearance> findByCompanyAndExpiryDateBetween(
            @Param("company") Company company,
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT tc FROM TaxClearance tc WHERE tc.status = 'VALID' AND tc.expiryDate <= :date")
    List<TaxClearance> findExpiredTaxClearances(@Param("date") LocalDateTime date);
    
    @Query("SELECT tc FROM TaxClearance tc WHERE tc.company = :company AND tc.status = 'VALID' AND tc.expiryDate <= :date")
    List<TaxClearance> findExpiredTaxClearancesByCompany(@Param("company") Company company, @Param("date") LocalDateTime date);
    
    @Query("SELECT COUNT(tc) FROM TaxClearance tc WHERE tc.company = :company")
    long countByCompany(@Param("company") Company company);
    
    @Query("SELECT COUNT(tc) FROM TaxClearance tc WHERE tc.company = :company AND tc.status = :status")
    long countByCompanyAndStatus(@Param("company") Company company, @Param("status") TaxClearance.ClearanceStatus status);
    
    @Query("SELECT COUNT(tc) FROM TaxClearance tc WHERE tc.company = :company AND tc.taxType = :taxType")
    long countByCompanyAndTaxType(@Param("company") Company company, @Param("taxType") TaxClearance.TaxType taxType);
    
    boolean existsByCertificateNumber(String certificateNumber);
}
