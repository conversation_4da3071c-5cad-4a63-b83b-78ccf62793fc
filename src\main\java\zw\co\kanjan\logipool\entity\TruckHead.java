package zw.co.kanjan.logipool.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "truck_heads")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TruckHead {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank
    @Size(max = 20)
    @Column(unique = true)
    private String registrationNumber;
    
    @NotBlank
    @Size(max = 50)
    private String make;
    
    @NotBlank
    @Size(max = 50)
    private String model;
    
    @Column(name = "manufacture_year")
    private Integer year;
    
    @Size(max = 50)
    private String engineNumber;
    
    @Size(max = 50)
    private String chassisNumber;
    
    @Enumerated(EnumType.STRING)
    @Column(length = 20)
    @Builder.Default
    private TruckStatus status = TruckStatus.AVAILABLE;
    
    @Enumerated(EnumType.STRING)
    @Column(length = 30)
    private FuelType fuelType;
    
    private BigDecimal fuelCapacity;
    
    private BigDecimal maxTowingCapacity;
    
    @Size(max = 20)
    private String capacityUnit = "kg";
    
    @Column(columnDefinition = "TEXT")
    private String description;
    
    @Size(max = 500)
    private String imageUrl;
    
    // Current location
    @Size(max = 100)
    private String currentLocation;
    
    private BigDecimal currentLatitude;
    
    private BigDecimal currentLongitude;
    
    // Maintenance and compliance
    @Builder.Default
    private Boolean hasInsurance = false;
    
    @Builder.Default
    private Boolean hasFitnessCertificate = false;
    
    @Builder.Default
    private Boolean hasRoadPermit = false;
    
    @Builder.Default
    private Boolean hasTaxClearance = false;
    
    private LocalDateTime insuranceExpiryDate;
    
    private LocalDateTime fitnessExpiryDate;
    
    private LocalDateTime roadPermitExpiryDate;
    
    private LocalDateTime taxClearanceExpiryDate;
    
    private LocalDateTime lastMaintenanceDate;
    
    private LocalDateTime nextMaintenanceDate;
    
    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "company_id")
    private Company company;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "current_driver_id")
    private User currentDriver;
    
    @OneToMany(mappedBy = "truckHead", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @Builder.Default
    private List<Document> documents = new ArrayList<>();
    
    @OneToMany(mappedBy = "truckHead", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @Builder.Default
    private List<FleetAssignment> assignments = new ArrayList<>();
    
    @CreationTimestamp
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    private LocalDateTime updatedAt;
    
    public enum TruckStatus {
        AVAILABLE, ASSIGNED, IN_TRANSIT, MAINTENANCE, OUT_OF_SERVICE, RETIRED
    }
    
    public enum FuelType {
        DIESEL, PETROL, ELECTRIC, HYBRID, CNG, LPG
    }
}
