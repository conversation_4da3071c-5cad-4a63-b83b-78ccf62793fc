package zw.co.kanjan.logipool.mapper.fleet;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import zw.co.kanjan.logipool.dto.fleet.TruckHeadDto;
import zw.co.kanjan.logipool.entity.TruckHead;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface TruckHeadMapper {
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "company", ignore = true)
    @Mapping(target = "currentDriver", ignore = true)
    @Mapping(target = "documents", ignore = true)
    @Mapping(target = "assignments", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "hasInsurance", constant = "false")
    @Mapping(target = "hasFitnessCertificate", constant = "false")
    @Mapping(target = "hasRoadPermit", constant = "false")
    @Mapping(target = "hasTaxClearance", constant = "false")
    TruckHead toEntity(TruckHeadDto.TruckHeadCreateRequest request);
    
    @Mapping(source = "company.id", target = "companyId")
    @Mapping(source = "company.name", target = "companyName")
    @Mapping(source = "currentDriver.id", target = "currentDriverId")
    @Mapping(expression = "java(truckHead.getCurrentDriver() != null ? truckHead.getCurrentDriver().getFirstName() + \" \" + truckHead.getCurrentDriver().getLastName() : null)", target = "currentDriverName")
    TruckHeadDto.TruckHeadResponse toResponse(TruckHead truckHead);
}
