package zw.co.kanjan.logipool.dto.fleet;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import zw.co.kanjan.logipool.entity.FleetAssignment;

import java.time.LocalDateTime;

public class FleetAssignmentDto {
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FleetAssignmentCreateRequest {
        
        @NotNull(message = "Load ID is required")
        private Long loadId;
        
        private Long bidId;
        
        private Long truckHeadId;
        
        private Long trailer1Id;
        
        private Long trailer2Id;
        
        private Long primaryDriverId;
        
        private Long secondaryDriverId;
        
        @NotNull(message = "Scheduled start time is required")
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime scheduledStartTime;
        
        @NotNull(message = "Scheduled end time is required")
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime scheduledEndTime;
        
        @Size(max = 1000, message = "Notes must not exceed 1000 characters")
        private String notes;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FleetAssignmentUpdateRequest {
        
        private Long truckHeadId;
        
        private Long trailer1Id;
        
        private Long trailer2Id;
        
        private Long primaryDriverId;
        
        private Long secondaryDriverId;
        
        private FleetAssignment.AssignmentStatus status;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime scheduledStartTime;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime scheduledEndTime;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime actualStartTime;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime actualEndTime;
        
        @Size(max = 1000, message = "Notes must not exceed 1000 characters")
        private String notes;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FleetAssignmentResponse {
        
        private Long id;
        private Long loadId;
        private String loadTitle;
        private String loadTrackingNumber;
        private Long bidId;
        private Long truckHeadId;
        private String truckHeadRegistration;
        private String truckHeadMake;
        private String truckHeadModel;
        private Long trailer1Id;
        private String trailer1Registration;
        private String trailer1Type;
        private Long trailer2Id;
        private String trailer2Registration;
        private String trailer2Type;
        private Long primaryDriverId;
        private String primaryDriverName;
        private Long secondaryDriverId;
        private String secondaryDriverName;
        private Long companyId;
        private String companyName;
        private FleetAssignment.AssignmentStatus status;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime assignedAt;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime scheduledStartTime;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime actualStartTime;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime scheduledEndTime;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime actualEndTime;
        
        private String notes;
        private Long assignedById;
        private String assignedByName;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime createdAt;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime updatedAt;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FleetAvailabilityRequest {
        
        @NotNull(message = "Start time is required")
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime startTime;
        
        @NotNull(message = "End time is required")
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime endTime;
        
        private Long excludeAssignmentId; // For updates, exclude current assignment
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FleetAvailabilityResponse {
        
        private boolean isAvailable;
        private String reason;
        private LocalDateTime conflictStartTime;
        private LocalDateTime conflictEndTime;
        private Long conflictingAssignmentId;
        private String conflictingLoadTitle;
    }
}
