package zw.co.kanjan.logipool.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import zw.co.kanjan.logipool.controller.PublicController;
import zw.co.kanjan.logipool.dto.EquipmentDto;
import zw.co.kanjan.logipool.dto.VehicleDto;
import zw.co.kanjan.logipool.dto.load.LoadResponse;
import zw.co.kanjan.logipool.dto.load.PublicLoadResponse;
import zw.co.kanjan.logipool.entity.Equipment;
import zw.co.kanjan.logipool.entity.Load;
import zw.co.kanjan.logipool.entity.Vehicle;
import zw.co.kanjan.logipool.exception.ResourceNotFoundException;
import zw.co.kanjan.logipool.mapper.EquipmentMapper;
import zw.co.kanjan.logipool.mapper.LoadMapper;
import zw.co.kanjan.logipool.mapper.VehicleMapper;
import zw.co.kanjan.logipool.repository.CompanyRepository;
import zw.co.kanjan.logipool.repository.EquipmentRepository;
import zw.co.kanjan.logipool.repository.LoadRepository;
import zw.co.kanjan.logipool.repository.VehicleRepository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.time.LocalDateTime;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional(readOnly = true)
public class PublicBrowsingService {

    private final VehicleRepository vehicleRepository;
    private final EquipmentRepository equipmentRepository;
    private final LoadRepository loadRepository;
    private final CompanyRepository companyRepository;
    private final VehicleMapper vehicleMapper;
    private final EquipmentMapper equipmentMapper;
    private final LoadMapper loadMapper;
    private final NotificationService notificationService;

    public Page<VehicleDto.VehicleResponse> browseVehicles(
            Pageable pageable,
            Vehicle.VehicleType type,
            String location,
            String search,
            Boolean availableForRent) {
        
        log.info("Browsing vehicles with filters - type: {}, location: {}, search: {}, availableForRent: {}", 
                type, location, search, availableForRent);

        Page<Vehicle> vehicles;

        if (search != null && !search.trim().isEmpty()) {
            vehicles = vehicleRepository.searchPublicVehicles(search.trim(), pageable);
        } else if (type != null) {
            vehicles = vehicleRepository.findPubliclyVisibleVehiclesByType(type, pageable);
        } else {
            vehicles = vehicleRepository.findPubliclyVisibleVehicles(pageable);
        }

        // Apply additional filters
        if (location != null || availableForRent != null) {
            List<Vehicle> filteredVehicles = vehicles.getContent().stream()
                    .filter(vehicle -> {
                        boolean locationMatch = location == null || 
                                (vehicle.getCompany() != null && 
                                 vehicle.getCompany().getCity() != null && 
                                 vehicle.getCompany().getCity().equalsIgnoreCase(location));
                        
                        boolean rentMatch = availableForRent == null || 
                                vehicle.getIsAvailableForRent().equals(availableForRent);
                        
                        return locationMatch && rentMatch;
                    })
                    .collect(Collectors.toList());
            
            // Note: This is a simplified approach. For better performance with large datasets,
            // consider implementing these filters at the database level
        }

        return vehicles.map(vehicleMapper::toPublicResponse);
    }

    public VehicleDto.VehicleResponse getVehicleDetails(Long vehicleId) {
        log.info("Getting vehicle details for ID: {}", vehicleId);
        
        Vehicle vehicle = vehicleRepository.findById(vehicleId)
                .orElseThrow(() -> new ResourceNotFoundException("Vehicle not found with ID: " + vehicleId));
        
        // Check if vehicle is publicly visible
        if (!vehicle.getIsPubliclyVisible() ||
            !Vehicle.PublicApprovalStatus.APPROVED.equals(vehicle.getPublicApprovalStatus())) {
            throw new ResourceNotFoundException("Vehicle not available for public viewing");
        }
        
        return vehicleMapper.toPublicResponse(vehicle);
    }

    public List<VehicleDto.VehicleResponse> getFeaturedVehicles(int limit) {
        log.info("Getting featured vehicles with limit: {}", limit);

        // First try to get vehicles marked as featured
        Pageable pageable = PageRequest.of(0, limit, Sort.by(Sort.Direction.DESC, "createdAt"));
        Page<Vehicle> featuredVehicles = vehicleRepository.findFeaturedVehicles(pageable);

        List<Vehicle> vehicles = new ArrayList<>(featuredVehicles.getContent());

        // If we don't have enough featured vehicles, fill with recent available vehicles
        if (vehicles.size() < limit) {
            int remaining = limit - vehicles.size();
            Pageable remainingPageable = PageRequest.of(0, remaining, Sort.by(Sort.Direction.DESC, "createdAt"));
            Page<Vehicle> recentVehicles = vehicleRepository.findPubliclyVisibleVehiclesAvailableForRent(remainingPageable);

            // Add vehicles that are not already in the featured list
            for (Vehicle vehicle : recentVehicles.getContent()) {
                if (!vehicles.contains(vehicle) && vehicles.size() < limit) {
                    vehicles.add(vehicle);
                }
            }
        }

        return vehicles.stream()
                .map(vehicleMapper::toPublicResponse)
                .collect(Collectors.toList());
    }

    public Page<EquipmentDto.EquipmentResponse> browseEquipment(
            Pageable pageable,
            Equipment.EquipmentType type,
            String location,
            String search,
            Boolean availableForRent) {
        
        log.info("Browsing equipment with filters - type: {}, location: {}, search: {}, availableForRent: {}", 
                type, location, search, availableForRent);

        Page<Equipment> equipment;

        if (search != null && !search.trim().isEmpty()) {
            equipment = equipmentRepository.searchPublicEquipment(search.trim(), pageable);
        } else if (type != null) {
            equipment = equipmentRepository.findPubliclyVisibleEquipmentByType(type, pageable);
        } else {
            equipment = equipmentRepository.findPubliclyVisibleEquipment(pageable);
        }

        // Apply additional filters
        if (location != null || availableForRent != null) {
            List<Equipment> filteredEquipment = equipment.getContent().stream()
                    .filter(eq -> {
                        boolean locationMatch = location == null || 
                                (eq.getLocation() != null && eq.getLocation().equalsIgnoreCase(location));
                        
                        boolean rentMatch = availableForRent == null || 
                                eq.getIsAvailableForRent().equals(availableForRent);
                        
                        return locationMatch && rentMatch;
                    })
                    .collect(Collectors.toList());
        }

        return equipment.map(equipmentMapper::toPublicResponse);
    }

    public EquipmentDto.EquipmentResponse getEquipmentDetails(Long equipmentId) {
        log.info("Getting equipment details for ID: {}", equipmentId);
        
        Equipment equipment = equipmentRepository.findById(equipmentId)
                .orElseThrow(() -> new ResourceNotFoundException("Equipment not found with ID: " + equipmentId));
        
        // Check if equipment is publicly visible
        if (!equipment.getIsPubliclyVisible() || 
            !Equipment.PublicApprovalStatus.APPROVED.equals(equipment.getPublicApprovalStatus())) {
            throw new ResourceNotFoundException("Equipment not available for public viewing");
        }
        
        return equipmentMapper.toPublicResponse(equipment);
    }

    public String[] getAvailableLocations() {
        // This is a simplified implementation. In a real application, you might want to
        // query the database for actual locations where vehicles/equipment are available
        return new String[]{
            "Harare", "Bulawayo", "Mutare", "Gweru", "Kwekwe",
            "Masvingo", "Chinhoyi", "Kadoma", "Bindura", "Chegutu"
        };
    }

    @Deprecated
    public LoadResponse trackLoad(String trackingNumber) {
        log.warn("DEPRECATED: Using unsecured tracking method for tracking number: {}", trackingNumber);
        log.warn("This method is deprecated and should be replaced with secure tracking verification");

        Load load = loadRepository.findByTrackingNumber(trackingNumber)
                .orElseThrow(() -> new ResourceNotFoundException("Load not found with tracking number: " + trackingNumber));

        // Return limited information for deprecated public access
        LoadResponse response = loadMapper.toResponse(load);

        // Remove sensitive information for unsecured access
        return sanitizeLoadResponseForPublicAccess(response);
    }

    /**
     * Get secure tracking details with verification
     * This method should be used instead of the deprecated trackLoad method
     */
    public LoadResponse getSecureTrackingDetails(String trackingNumber) {
        log.info("Getting secure tracking details for tracking number: {}", trackingNumber);

        Load load = loadRepository.findByTrackingNumber(trackingNumber)
                .orElseThrow(() -> new ResourceNotFoundException("Load not found with tracking number: " + trackingNumber));

        // Return full tracking information for verified access
        return loadMapper.toResponse(load);
    }

    /**
     * Sanitize load response for public access by removing sensitive information
     */
    private LoadResponse sanitizeLoadResponseForPublicAccess(LoadResponse response) {
        // Create a copy with limited information
        LoadResponse sanitized = LoadResponse.builder()
                .id(response.getId())
                .trackingNumber(response.getTrackingNumber())
                .title("Shipment Information") // Generic title
                .status(response.getStatus())
                .pickupLocation(maskLocation(response.getPickupLocation()))
                .deliveryLocation(maskLocation(response.getDeliveryLocation()))
                .estimatedDistance(response.getEstimatedDistance())
                .distanceUnit(response.getDistanceUnit())
                .createdAt(response.getCreatedAt())
                .updatedAt(response.getUpdatedAt())
                // Remove sensitive fields
                .description(null)
                .cargoType(null)
                .weight(null)
                .weightUnit(null)
                .volume(null)
                .volumeUnit(null)
                .pickupLatitude(null)
                .pickupLongitude(null)
                .deliveryLatitude(null)
                .deliveryLongitude(null)
                .pickupDate(null)
                .deliveryDate(null)
                .paymentRate(null)
                .paymentUnit(null)
                .estimatedValue(null)
                .clientId(null)
                .clientName(null)
                .assignedCompanyId(null)
                .assignedCompanyName(null)
                .specialInstructions(null)
                .build();

        return sanitized;
    }

    /**
     * Mask location information for public access
     */
    private String maskLocation(String location) {
        if (location == null || location.length() <= 3) {
            return "***";
        }

        // Show only the first few characters and city/country if available
        String[] parts = location.split(",");
        if (parts.length > 1) {
            // If it's a formatted address, show only the city/country part
            return "*** " + parts[parts.length - 1].trim();
        } else {
            // If it's a simple location, mask most of it
            return location.substring(0, 3) + "***";
        }
    }

    /**
     * Browse publicly visible loads with filtering and search
     */
    public Page<PublicLoadResponse> browseLoads(
            Pageable pageable,
            Load.LoadType loadType,
            String cargoType,
            String search) {

        log.info("Browsing public loads with filters - loadType: {}, cargoType: {}, search: {}",
                loadType, cargoType, search);

        LocalDateTime now = LocalDateTime.now();
        Page<Load> loads;

        if (search != null && !search.trim().isEmpty()) {
            loads = loadRepository.searchPubliclyVisibleLoads(search.trim(), now, pageable);
        } else if (loadType != null) {
            loads = loadRepository.findPubliclyVisibleLoadsByLoadType(loadType, now, pageable);
        } else if (cargoType != null && !cargoType.trim().isEmpty()) {
            loads = loadRepository.findPubliclyVisibleLoadsByCargoType(cargoType.trim(), now, pageable);
        } else {
            loads = loadRepository.findPubliclyVisibleLoads(now, pageable);
        }

        return loads.map(this::toPublicLoadResponse);
    }

    /**
     * Get details of a specific publicly visible load
     */
    public PublicLoadResponse getPublicLoadDetails(Long loadId) {
        log.info("Getting public load details for ID: {}", loadId);

        Load load = loadRepository.findById(loadId)
                .orElseThrow(() -> new ResourceNotFoundException("Load not found with ID: " + loadId));

        // Check if load is publicly visible (posted and bidding still open)
        if (!Load.LoadStatus.POSTED.equals(load.getStatus()) ||
            load.getBiddingClosesAt() == null ||
            load.getBiddingClosesAt().isBefore(LocalDateTime.now())) {
            throw new ResourceNotFoundException("Load not available for public viewing");
        }

        return toPublicLoadResponse(load);
    }

    /**
     * Convert Load entity to PublicLoadResponse with sanitized information
     */
    private PublicLoadResponse toPublicLoadResponse(Load load) {
        return PublicLoadResponse.builder()
                .id(load.getId())
                .title(load.getTitle())
                .cargoType(load.getCargoType())
                .weight(load.getWeight())
                .weightUnit(load.getWeightUnit())
                .volume(load.getVolume())
                .volumeUnit(load.getVolumeUnit())
                .pickupRegion(PublicLoadResponse.maskLocation(load.getPickupLocation()))
                .deliveryRegion(PublicLoadResponse.maskLocation(load.getDeliveryLocation()))
                .pickupDate(load.getPickupDate())
                .deliveryDate(load.getDeliveryDate())
                .estimatedDistance(load.getEstimatedDistance())
                .distanceUnit(load.getDistanceUnit())
                .loadType(load.getLoadType())
                .priority(load.getPriority())
                .isVerified(load.getIsVerified())
                .requiresInsurance(load.getRequiresInsurance())
                .requiresSpecialHandling(load.getRequiresSpecialHandling())
                .createdAt(load.getCreatedAt())
                .biddingClosesAt(load.getBiddingClosesAt())
                .status(load.getStatus())
                .bidCount(load.getBids() != null ? load.getBids().size() : 0)
                .publicDescription(PublicLoadResponse.createPublicDescription(load.getDescription(), load.getCargoType()))
                .build();
    }

    public Map<String, Object> getPublicStats() {
        log.info("Getting public statistics");

        Map<String, Object> stats = new HashMap<>();

        // Get counts of publicly visible items
        long vehicleCount = vehicleRepository.countPubliclyVisibleVehicles();
        long equipmentCount = equipmentRepository.countPubliclyVisibleEquipment();
        long activeLoadsCount = loadRepository.countPubliclyVisibleLoads(LocalDateTime.now());
        long completedLoadsCount = loadRepository.countByStatus(Load.LoadStatus.DELIVERED);

        stats.put("totalVehicles", vehicleCount);
        stats.put("totalEquipment", equipmentCount);
        stats.put("activeLoads", activeLoadsCount);
        stats.put("completedLoads", completedLoadsCount);
        stats.put("totalCompanies", companyRepository.count());

        return stats;
    }

    @Transactional
    public void submitVehicleInquiry(Long vehicleId, PublicController.VehicleInquiryRequest request) {
        log.info("Submitting vehicle inquiry for vehicle ID: {} from: {}", vehicleId, request.email);
        
        Vehicle vehicle = vehicleRepository.findById(vehicleId)
                .orElseThrow(() -> new ResourceNotFoundException("Vehicle not found with ID: " + vehicleId));
        
        // Check if vehicle is publicly visible
        if (!vehicle.getIsPubliclyVisible() || 
            !Vehicle.PublicApprovalStatus.APPROVED.equals(vehicle.getPublicApprovalStatus())) {
            throw new ResourceNotFoundException("Vehicle not available for inquiry");
        }
        
        // Send notification to vehicle owner/company
        String subject = "New Vehicle Inquiry - " + vehicle.getMake() + " " + vehicle.getModel();
        String message = buildVehicleInquiryMessage(vehicle, request);
        
        if (vehicle.getCompany() != null && vehicle.getCompany().getUser() != null) {
            notificationService.sendNotification(
                vehicle.getCompany().getUser(),
                subject,
                message,
                "VEHICLE_INQUIRY"
            );
        }
        
        log.info("Vehicle inquiry submitted successfully for vehicle ID: {}", vehicleId);
    }

    @Transactional
    public void submitEquipmentInquiry(Long equipmentId, PublicController.EquipmentInquiryRequest request) {
        log.info("Submitting equipment inquiry for equipment ID: {} from: {}", equipmentId, request.email);
        
        Equipment equipment = equipmentRepository.findById(equipmentId)
                .orElseThrow(() -> new ResourceNotFoundException("Equipment not found with ID: " + equipmentId));
        
        // Check if equipment is publicly visible
        if (!equipment.getIsPubliclyVisible() || 
            !Equipment.PublicApprovalStatus.APPROVED.equals(equipment.getPublicApprovalStatus())) {
            throw new ResourceNotFoundException("Equipment not available for inquiry");
        }
        
        // Send notification to equipment owner/company
        String subject = "New Equipment Inquiry - " + equipment.getName();
        String message = buildEquipmentInquiryMessage(equipment, request);
        
        if (equipment.getCompany() != null && equipment.getCompany().getUser() != null) {
            notificationService.sendNotification(
                equipment.getCompany().getUser(),
                subject,
                message,
                "EQUIPMENT_INQUIRY"
            );
        }
        
        log.info("Equipment inquiry submitted successfully for equipment ID: {}", equipmentId);
    }

    private String buildVehicleInquiryMessage(Vehicle vehicle, PublicController.VehicleInquiryRequest request) {
        return String.format(
            "New inquiry for your vehicle:\n\n" +
            "Vehicle: %s %s (%s)\n" +
            "From: %s\n" +
            "Email: %s\n" +
            "Phone: %s\n" +
            "Rental Period: %s to %s\n\n" +
            "Message:\n%s\n\n" +
            "Please respond to this inquiry through the LogiPool platform.",
            vehicle.getMake(), vehicle.getModel(), vehicle.getRegistrationNumber(),
            request.name, request.email, request.phone,
            request.startDate, request.endDate, request.message
        );
    }

    private String buildEquipmentInquiryMessage(Equipment equipment, PublicController.EquipmentInquiryRequest request) {
        return String.format(
            "New inquiry for your equipment:\n\n" +
            "Equipment: %s (%s %s)\n" +
            "From: %s\n" +
            "Email: %s\n" +
            "Phone: %s\n" +
            "Rental Period: %s to %s\n" +
            "Needs Operator: %s\n\n" +
            "Message:\n%s\n\n" +
            "Please respond to this inquiry through the LogiPool platform.",
            equipment.getName(), equipment.getMake(), equipment.getModel(),
            request.name, request.email, request.phone,
            request.startDate, request.endDate, 
            request.needsOperator != null && request.needsOperator ? "Yes" : "No",
            request.message
        );
    }
}
