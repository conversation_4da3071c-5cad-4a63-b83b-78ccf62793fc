package zw.co.kanjan.logipool.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "tax_clearances")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaxClearance {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank
    @Size(max = 50)
    private String certificateNumber;
    
    @NotBlank
    @Size(max = 100)
    private String issuingAuthority;
    
    @Enumerated(EnumType.STRING)
    @Column(length = 30)
    @NotNull
    private TaxType taxType;
    
    @Enumerated(EnumType.STRING)
    @Column(length = 20)
    @Builder.Default
    private ClearanceStatus status = ClearanceStatus.VALID;
    
    @NotNull
    private LocalDateTime issueDate;
    
    @NotNull
    private LocalDateTime expiryDate;
    
    private BigDecimal taxAmount;
    
    private BigDecimal paidAmount;
    
    private BigDecimal outstandingAmount;
    
    @Size(max = 20)
    private String currency = "USD";
    
    @Column(columnDefinition = "TEXT")
    private String description;
    
    @Column(columnDefinition = "TEXT")
    private String conditions;
    
    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "company_id")
    private Company company;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "vehicle_id")
    private Vehicle vehicle;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "truck_head_id")
    private TruckHead truckHead;
    
    @OneToMany(mappedBy = "taxClearance", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @Builder.Default
    private List<Document> documents = new ArrayList<>();
    
    @CreationTimestamp
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    private LocalDateTime updatedAt;
    
    public enum TaxType {
        INCOME_TAX,         // Company income tax
        VAT,                // Value Added Tax
        WITHHOLDING_TAX,    // Withholding tax
        VEHICLE_LICENSE,    // Vehicle licensing fees
        ROAD_TAX,           // Road usage tax
        CUSTOMS_DUTY,       // Import/export duties
        MUNICIPAL_TAX,      // Municipal taxes
        OTHER               // Other tax types
    }
    
    public enum ClearanceStatus {
        VALID,      // Currently valid
        EXPIRED,    // Has expired
        PENDING,    // Pending approval
        REJECTED,   // Application rejected
        SUSPENDED   // Temporarily suspended
    }
}
