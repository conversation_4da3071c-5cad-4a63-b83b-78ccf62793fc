package zw.co.kanjan.logipool.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import jakarta.validation.Valid;
import java.util.List;
import zw.co.kanjan.logipool.dto.VehicleDto;
import zw.co.kanjan.logipool.dto.EquipmentDto;
import zw.co.kanjan.logipool.dto.load.LoadResponse;
import zw.co.kanjan.logipool.dto.load.PublicLoadResponse;
import zw.co.kanjan.logipool.dto.tracking.TrackingVerificationDto;
import zw.co.kanjan.logipool.entity.TrackingVerification;
import zw.co.kanjan.logipool.entity.Vehicle;
import zw.co.kanjan.logipool.entity.Equipment;
import zw.co.kanjan.logipool.entity.Load;
import zw.co.kanjan.logipool.service.PublicBrowsingService;
import zw.co.kanjan.logipool.service.GuestLoadInquiryService;
import zw.co.kanjan.logipool.service.TrackingVerificationService;
import zw.co.kanjan.logipool.dto.guest.GuestLoadInquiryRequest;
import zw.co.kanjan.logipool.entity.GuestLoadInquiry;

import java.util.Map;
import jakarta.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/api/public")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Public API", description = "Public endpoints for browsing vehicles and equipment")
@CrossOrigin(origins = "*")
public class PublicController {

    private final PublicBrowsingService publicBrowsingService;
    private final GuestLoadInquiryService guestLoadInquiryService;
    private final TrackingVerificationService trackingVerificationService;

    @GetMapping("/vehicles")
    @Operation(summary = "Browse publicly available vehicles", 
               description = "Get a paginated list of publicly visible and approved vehicles")
    public ResponseEntity<Page<VehicleDto.VehicleResponse>> browseVehicles(
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "Sort by field") @RequestParam(defaultValue = "createdAt") String sortBy,
            @Parameter(description = "Sort direction") @RequestParam(defaultValue = "desc") String sortDir,
            @Parameter(description = "Filter by vehicle type") @RequestParam(required = false) Vehicle.VehicleType type,
            @Parameter(description = "Filter by location") @RequestParam(required = false) String location,
            @Parameter(description = "Search term") @RequestParam(required = false) String search,
            @Parameter(description = "Filter by availability for rent") @RequestParam(required = false) Boolean availableForRent) {
        
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<VehicleDto.VehicleResponse> vehicles = publicBrowsingService.browseVehicles(
                pageable, type, location, search, availableForRent);
        
        return ResponseEntity.ok(vehicles);
    }

    @GetMapping("/vehicles/{id}")
    @Operation(summary = "Get vehicle details",
               description = "Get detailed information about a specific vehicle")
    public ResponseEntity<VehicleDto.VehicleResponse> getVehicleDetails(
            @Parameter(description = "Vehicle ID") @PathVariable Long id) {

        VehicleDto.VehicleResponse vehicle = publicBrowsingService.getVehicleDetails(id);
        return ResponseEntity.ok(vehicle);
    }

    @GetMapping("/vehicles/featured")
    @Operation(summary = "Get featured vehicles",
               description = "Get a list of featured vehicles for the home page")
    public ResponseEntity<List<VehicleDto.VehicleResponse>> getFeaturedVehicles(
            @Parameter(description = "Maximum number of vehicles to return") @RequestParam(defaultValue = "6") int limit) {

        List<VehicleDto.VehicleResponse> vehicles = publicBrowsingService.getFeaturedVehicles(limit);
        return ResponseEntity.ok(vehicles);
    }

    @GetMapping("/equipment")
    @Operation(summary = "Browse publicly available equipment", 
               description = "Get a paginated list of publicly visible and approved equipment")
    public ResponseEntity<Page<EquipmentDto.EquipmentResponse>> browseEquipment(
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "Sort by field") @RequestParam(defaultValue = "createdAt") String sortBy,
            @Parameter(description = "Sort direction") @RequestParam(defaultValue = "desc") String sortDir,
            @Parameter(description = "Filter by equipment type") @RequestParam(required = false) Equipment.EquipmentType type,
            @Parameter(description = "Filter by location") @RequestParam(required = false) String location,
            @Parameter(description = "Search term") @RequestParam(required = false) String search,
            @Parameter(description = "Filter by availability for rent") @RequestParam(required = false) Boolean availableForRent) {
        
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<EquipmentDto.EquipmentResponse> equipment = publicBrowsingService.browseEquipment(
                pageable, type, location, search, availableForRent);
        
        return ResponseEntity.ok(equipment);
    }

    @GetMapping("/equipment/{id}")
    @Operation(summary = "Get equipment details", 
               description = "Get detailed information about a specific equipment")
    public ResponseEntity<EquipmentDto.EquipmentResponse> getEquipmentDetails(
            @Parameter(description = "Equipment ID") @PathVariable Long id) {
        
        EquipmentDto.EquipmentResponse equipment = publicBrowsingService.getEquipmentDetails(id);
        return ResponseEntity.ok(equipment);
    }

    @GetMapping("/vehicles/types")
    @Operation(summary = "Get vehicle types", 
               description = "Get list of available vehicle types")
    public ResponseEntity<Vehicle.VehicleType[]> getVehicleTypes() {
        return ResponseEntity.ok(Vehicle.VehicleType.values());
    }

    @GetMapping("/equipment/types")
    @Operation(summary = "Get equipment types",
               description = "Get list of available equipment types")
    public ResponseEntity<Equipment.EquipmentType[]> getEquipmentTypes() {
        return ResponseEntity.ok(Equipment.EquipmentType.values());
    }

    @GetMapping("/loads")
    @Operation(summary = "Browse publicly available loads",
               description = "Get a paginated list of posted loads available for bidding")
    public ResponseEntity<Page<PublicLoadResponse>> browseLoads(
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "Sort by field") @RequestParam(defaultValue = "createdAt") String sortBy,
            @Parameter(description = "Sort direction") @RequestParam(defaultValue = "desc") String sortDir,
            @Parameter(description = "Filter by load type") @RequestParam(required = false) Load.LoadType loadType,
            @Parameter(description = "Filter by cargo type") @RequestParam(required = false) String cargoType,
            @Parameter(description = "Search term") @RequestParam(required = false) String search) {

        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);

        Page<PublicLoadResponse> loads = publicBrowsingService.browseLoads(
                pageable, loadType, cargoType, search);

        return ResponseEntity.ok(loads);
    }

    @GetMapping("/loads/{id}")
    @Operation(summary = "Get public load details",
               description = "Get details of a specific publicly available load")
    public ResponseEntity<PublicLoadResponse> getLoadDetails(
            @Parameter(description = "Load ID") @PathVariable Long id) {

        PublicLoadResponse load = publicBrowsingService.getPublicLoadDetails(id);
        return ResponseEntity.ok(load);
    }

    @GetMapping("/loads/types")
    @Operation(summary = "Get load types",
               description = "Get list of available load types")
    public ResponseEntity<Load.LoadType[]> getLoadTypes() {
        return ResponseEntity.ok(Load.LoadType.values());
    }

    @GetMapping("/locations")
    @Operation(summary = "Get available locations",
               description = "Get list of locations where vehicles/equipment are available")
    public ResponseEntity<String[]> getAvailableLocations() {
        String[] locations = publicBrowsingService.getAvailableLocations();
        return ResponseEntity.ok(locations);
    }

    @GetMapping("/track/{trackingNumber}")
    @Operation(summary = "Track load by tracking number (DEPRECATED)",
               description = "Track a load using its tracking number (public access) - Use secure tracking endpoints instead")
    @Deprecated
    public ResponseEntity<LoadResponse> trackLoad(
            @Parameter(description = "Load tracking number") @PathVariable String trackingNumber) {

        LoadResponse load = publicBrowsingService.trackLoad(trackingNumber);
        return ResponseEntity.ok(load);
    }

    @PostMapping("/tracking/request-verification")
    @Operation(summary = "Request tracking verification",
               description = "Request secure access to tracking information via email or SMS")
    public ResponseEntity<TrackingVerificationDto.VerificationResponse> requestTrackingVerification(
            @Valid @RequestBody TrackingVerificationDto.VerificationRequest request,
            HttpServletRequest httpRequest) {

        String ipAddress = getClientIpAddress(httpRequest);
        String userAgent = httpRequest.getHeader("User-Agent");

        String token = trackingVerificationService.requestTrackingVerification(
                request.getTrackingNumber(),
                request.getContact(),
                request.getVerificationMethodEnum(),
                ipAddress,
                userAgent
        );

        boolean requiresCode = request.getVerificationMethodEnum() == TrackingVerification.VerificationMethod.SMS;
        int expiryMinutes = requiresCode ? 15 : 1440; // 15 minutes for SMS, 24 hours for email

        TrackingVerificationDto.VerificationResponse response =
                TrackingVerificationDto.VerificationResponse.success(token, requiresCode, expiryMinutes, request.getContact());

        return ResponseEntity.ok(response);
    }

    @PostMapping("/tracking/verify-code")
    @Operation(summary = "Verify tracking code",
               description = "Verify SMS code to gain access to tracking information")
    public ResponseEntity<TrackingVerificationDto.CodeVerificationResponse> verifyTrackingCode(
            @Valid @RequestBody TrackingVerificationDto.CodeVerificationRequest request,
            HttpServletRequest httpRequest) {

        String ipAddress = getClientIpAddress(httpRequest);

        String token = trackingVerificationService.verifyTrackingCode(
                request.getTrackingNumber(),
                request.getVerificationCode(),
                ipAddress
        );

        TrackingVerificationDto.CodeVerificationResponse response =
                TrackingVerificationDto.CodeVerificationResponse.success(token, 24);

        return ResponseEntity.ok(response);
    }

    @GetMapping("/tracking/details")
    @Operation(summary = "Get secure tracking details",
               description = "Get tracking details using verified access token")
    public ResponseEntity<LoadResponse> getSecureTrackingDetails(
            @Parameter(description = "Verified access token") @RequestParam(required = true) String token,
            HttpServletRequest httpRequest) {

        String ipAddress = getClientIpAddress(httpRequest);

        LoadResponse response = trackingVerificationService.getTrackingDetails(token, ipAddress);

        return ResponseEntity.ok(response);
    }

    @GetMapping("/stats")
    @Operation(summary = "Get public statistics",
               description = "Get public statistics for the platform")
    public ResponseEntity<Map<String, Object>> getPublicStats() {
        Map<String, Object> stats = publicBrowsingService.getPublicStats();
        return ResponseEntity.ok(stats);
    }

    @PostMapping("/vehicles/{id}/inquiry")
    @Operation(summary = "Submit vehicle inquiry", 
               description = "Submit an inquiry for a specific vehicle")
    public ResponseEntity<String> submitVehicleInquiry(
            @Parameter(description = "Vehicle ID") @PathVariable Long id,
            @RequestBody VehicleInquiryRequest request) {
        
        publicBrowsingService.submitVehicleInquiry(id, request);
        return ResponseEntity.ok("Inquiry submitted successfully");
    }

    @PostMapping("/equipment/{id}/inquiry")
    @Operation(summary = "Submit equipment inquiry", 
               description = "Submit an inquiry for a specific equipment")
    public ResponseEntity<String> submitEquipmentInquiry(
            @Parameter(description = "Equipment ID") @PathVariable Long id,
            @RequestBody EquipmentInquiryRequest request) {
        
        publicBrowsingService.submitEquipmentInquiry(id, request);
        return ResponseEntity.ok("Inquiry submitted successfully");
    }

    @PostMapping("/guest-load-inquiry")
    @Operation(summary = "Submit guest load inquiry",
               description = "Submit a load inquiry without creating an account")
    public ResponseEntity<Map<String, Object>> submitGuestLoadInquiry(
            @Valid @RequestBody GuestLoadInquiryRequest request,
            HttpServletRequest httpRequest) {

        // Add IP address and user agent for tracking
        request.setIpAddress(getClientIpAddress(httpRequest));
        request.setUserAgent(httpRequest.getHeader("User-Agent"));

        GuestLoadInquiry inquiry = guestLoadInquiryService.submitInquiry(request);

        Map<String, Object> response = Map.of(
            "success", true,
            "message", "Load inquiry submitted successfully",
            "inquiryId", inquiry.getId(),
            "referenceNumber", "#" + inquiry.getId()
        );

        return ResponseEntity.ok(response);
    }

    // Inner classes for inquiry requests
    public static class VehicleInquiryRequest {
        public String name;
        public String email;
        public String phone;
        public String message;
        public String startDate;
        public String endDate;
    }

    public static class EquipmentInquiryRequest {
        public String name;
        public String email;
        public String phone;
        public String message;
        public String startDate;
        public String endDate;
        public Boolean needsOperator;
    }

    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }
}
