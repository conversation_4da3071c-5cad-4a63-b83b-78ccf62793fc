import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../features/auth/screens/login_screen.dart';
import '../../features/auth/screens/register_screen.dart';
import '../../features/auth/screens/forgot_password_screen.dart';
import '../../features/auth/screens/reset_password_screen.dart';
import '../../shared/widgets/main_layout.dart';
import '../../shared/widgets/public_layout.dart';
import '../../features/public/screens/public_home_screen.dart';
import '../../features/public/screens/about_us_screen.dart';
import '../../features/public/screens/services_screen.dart';
import '../../features/public/screens/contact_us_screen.dart';
import '../../features/public/screens/vehicles_browse_screen.dart';
import '../../features/public/screens/equipment_browse_screen.dart';
import '../../features/public/screens/loads_browse_screen.dart';
import '../../features/public/screens/order_tracking_screen.dart';
import '../../features/public/screens/terms_conditions_screen.dart';
import '../../features/public/screens/faq_screen.dart';
import '../../features/public/screens/privacy_policy_screen.dart';
import '../../features/public/screens/guest_load_posting_screen.dart';
import '../../features/loads/screens/load_list_screen.dart';
import '../../features/loads/screens/load_detail_screen.dart';
import '../../features/loads/screens/create_load_screen.dart';
import '../../features/loads/screens/load_management_screen.dart';
import '../../features/loads/screens/load_lifecycle_screen.dart';
import '../../features/bids/screens/bid_details_screen.dart';
import '../../features/bids/screens/my_bids_screen.dart';
import '../../features/bids/bloc/bid_bloc.dart';
import '../../shared/services/bid_service.dart';
import '../../core/di/service_locator.dart';
import '../../shared/models/bid_model.dart';
import '../../features/payments/bloc/payment_bloc.dart';
import '../../shared/services/payment_service.dart';
import '../../features/loads/bloc/load_bloc.dart';
import '../../shared/services/load_service.dart';
import '../../features/companies/screens/companies_screen.dart';
import '../../features/companies/screens/company_profile_screen.dart';
import '../../features/companies/screens/company_registration_screen.dart';
import '../../features/companies/screens/company_directory_screen.dart';
import '../../features/companies/screens/company_edit_screen.dart';
import '../../features/companies/screens/company_details_screen.dart';
import '../../features/companies/screens/companies_router_screen.dart';
import '../../features/companies/bloc/company_bloc.dart';
import '../../features/tracking/screens/tracking_screen.dart';
import '../../features/documents/screens/documents_screen.dart';
import '../../features/documents/screens/document_upload_screen.dart';
import '../../features/documents/screens/document_view_screen.dart';
import '../../features/payments/screens/payments_screen.dart';
import '../../features/payments/screens/financial_management_screen.dart';
import '../../features/payments/screens/payment_create_screen.dart';
import '../../features/payments/screens/payment_details_screen.dart';
import '../../features/payments/screens/payment_process_screen.dart';
import '../../features/payments/screens/invoice_details_screen.dart';
import '../../features/invoices/bloc/invoice_bloc.dart';
import '../../shared/services/invoice_service.dart';
import '../../features/notifications/screens/notifications_screen.dart';
import '../../features/notifications/screens/notification_details_screen.dart';
import '../../features/notifications/bloc/notification_bloc.dart';
import '../../shared/services/notification_api_service.dart';
import '../../features/admin/screens/admin_dashboard_screen.dart';
import '../../features/admin/screens/admin_companies_screen.dart';
import '../../features/admin/screens/admin_company_details_screen.dart';
import '../../features/admin/bloc/admin_bloc.dart';
import '../../shared/services/auth_service.dart';

class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: '/',
    redirect: (context, state) {
      final authService = AuthService.instance;
      final isLoggedIn = authService.isLoggedIn;
      final isLoggingIn = state.matchedLocation == '/login' ||
          state.matchedLocation == '/register' ||
          state.matchedLocation == '/forgot-password' ||
          state.matchedLocation.startsWith('/reset-password');

      // Public routes that don't require authentication
      final publicRoutes = [
        '/',
        '/vehicles',
        '/equipment',
        '/loads',
        '/services',
        '/about',
        '/contact',
        '/track',
        '/faq',
        '/terms',
        '/privacy',
        '/guest-load-posting',
      ];

      final isPublicRoute = publicRoutes.contains(state.matchedLocation);

      // If logged in and on auth pages, redirect to authenticated home
      if (isLoggedIn && isLoggingIn) {
        return '/loads';
      }

      // Allow access to public routes and auth pages without authentication
      if (isPublicRoute || isLoggingIn) {
        return null;
      }

      // If not logged in and trying to access protected route, redirect to login
      if (!isLoggedIn) {
        return '/login';
      }

      return null;
    },
    routes: [
      // Public Routes
      ShellRoute(
        builder: (context, state, child) => PublicLayout(child: child),
        routes: [
          GoRoute(
            path: '/',
            name: 'home',
            builder: (context, state) => const PublicHomeScreen(),
          ),
          GoRoute(
            path: '/vehicles',
            name: 'vehicles',
            builder: (context, state) => const VehiclesBrowseScreen(),
          ),
          GoRoute(
            path: '/equipment',
            name: 'equipment',
            builder: (context, state) => const EquipmentBrowseScreen(),
          ),
          GoRoute(
            path: '/loads',
            name: 'public-loads',
            builder: (context, state) => const LoadsBrowseScreen(),
          ),
          GoRoute(
            path: '/services',
            name: 'services',
            builder: (context, state) => const ServicesScreen(),
          ),
          GoRoute(
            path: '/about',
            name: 'about',
            builder: (context, state) => const AboutUsScreen(),
          ),
          GoRoute(
            path: '/contact',
            name: 'contact',
            builder: (context, state) => const ContactUsScreen(),
          ),
          GoRoute(
            path: '/track',
            name: 'track',
            builder: (context, state) => const OrderTrackingScreen(),
          ),
          GoRoute(
            path: '/faq',
            name: 'faq',
            builder: (context, state) => const FaqScreen(),
          ),
          GoRoute(
            path: '/terms',
            name: 'terms',
            builder: (context, state) => const TermsConditionsScreen(),
          ),
          GoRoute(
            path: '/privacy',
            name: 'privacy',
            builder: (context, state) => const PrivacyPolicyScreen(),
          ),
          GoRoute(
            path: '/guest-load-posting',
            name: 'guest-load-posting',
            builder: (context, state) => const GuestLoadPostingScreen(),
          ),
        ],
      ),

      // Auth Routes
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: '/register',
        name: 'register',
        builder: (context, state) => const RegisterScreen(),
      ),
      GoRoute(
        path: '/forgot-password',
        name: 'forgot-password',
        builder: (context, state) => const ForgotPasswordScreen(),
      ),
      GoRoute(
        path: '/reset-password',
        name: 'reset-password',
        builder: (context, state) {
          final token = state.uri.queryParameters['token'] ?? '';
          return ResetPasswordScreen(token: token);
        },
      ),

      // Main App Routes with Bottom Navigation
      ShellRoute(
        builder: (context, state, child) => MainLayout(child: child),
        routes: [
          // Loads
          GoRoute(
            path: '/loads',
            name: 'loads',
            builder: (context, state) => const LoadListScreen(),
            routes: [
              GoRoute(
                path: '/create',
                name: 'create-load',
                builder: (context, state) => const CreateLoadScreen(),
              ),
              GoRoute(
                path: '/:id',
                name: 'load-details',
                builder: (context, state) {
                  final id = state.pathParameters['id']!;
                  return BlocProvider(
                    create: (context) => LoadBloc(getIt<LoadService>()),
                    child: LoadDetailScreen(loadId: id),
                  );
                },
              ),
              GoRoute(
                path: '/:id/manage',
                name: 'load-management',
                builder: (context, state) {
                  final id = int.parse(state.pathParameters['id']!);
                  return BlocProvider(
                    create: (context) => LoadBloc(getIt<LoadService>()),
                    child: LoadManagementScreen(loadId: id),
                  );
                },
              ),
              GoRoute(
                path: '/:id/lifecycle',
                name: 'load-lifecycle',
                builder: (context, state) {
                  final id = int.parse(state.pathParameters['id']!);
                  return BlocProvider(
                    create: (context) => LoadBloc(getIt<LoadService>()),
                    child: LoadLifecycleScreen(loadId: id),
                  );
                },
              ),
            ],
          ),

          // Bids
          GoRoute(
            path: '/bids',
            name: 'bids',
            builder: (context, state) => BlocProvider(
              create: (context) => BidBloc(getIt<BidService>()),
              child: const MyBidsScreen(),
            ),
            routes: [
              GoRoute(
                path: '/:id',
                name: 'bid-details',
                builder: (context, state) {
                  final id = int.parse(state.pathParameters['id']!);
                  return BlocProvider(
                    create: (context) => BidBloc(getIt<BidService>()),
                    child: BidDetailsScreen(bidId: id),
                  );
                },
              ),
            ],
          ),

          // Companies
          GoRoute(
            path: '/companies',
            name: 'companies',
            builder: (context, state) => BlocProvider(
              create: (context) => CompanyBloc(companyService: getIt()),
              child: const CompaniesRouterScreen(),
            ),
            routes: [
              GoRoute(
                path: '/profile',
                name: 'company-profile',
                builder: (context, state) => BlocProvider(
                  create: (context) => CompanyBloc(companyService: getIt()),
                  child: const CompanyProfileScreen(),
                ),
              ),
              GoRoute(
                path: '/register',
                name: 'company-register',
                builder: (context, state) => BlocProvider(
                  create: (context) => CompanyBloc(companyService: getIt()),
                  child: const CompanyRegistrationScreen(),
                ),
              ),
              GoRoute(
                path: '/directory',
                name: 'company-directory',
                builder: (context, state) => BlocProvider(
                  create: (context) => CompanyBloc(companyService: getIt()),
                  child: const CompanyDirectoryScreen(),
                ),
              ),
              GoRoute(
                path: '/detail/:id',
                name: 'company-detail',
                builder: (context, state) {
                  final id = int.parse(state.pathParameters['id']!);
                  return BlocProvider(
                    create: (context) => CompanyBloc(companyService: getIt()),
                    child: CompanyDetailsScreen(companyId: id),
                  );
                },
              ),
              GoRoute(
                path: '/edit/:id',
                name: 'company-edit',
                builder: (context, state) {
                  final id = state.pathParameters['id']!;
                  return BlocProvider(
                    create: (context) => CompanyBloc(companyService: getIt()),
                    child: CompanyEditScreen(companyId: id),
                  );
                },
              ),
              GoRoute(
                path: '/invoices/:id',
                name: 'invoice-details',
                builder: (context, state) {
                  final id = state.pathParameters['id']!;
                  return BlocProvider(
                    create: (context) =>
                        InvoiceBloc(invoiceService: getIt<InvoiceService>()),
                    child: InvoiceDetailsScreen(invoiceId: id),
                  );
                },
              ),
            ],
          ),

          // Tracking
          GoRoute(
            path: '/tracking',
            name: 'tracking',
            builder: (context, state) => const TrackingScreen(),
          ),

          // Documents
          GoRoute(
            path: '/documents',
            name: 'documents',
            builder: (context, state) {
              final queryParams = state.uri.queryParameters;
              return DocumentsScreen(
                companyId: queryParams['companyId'] != null
                    ? int.tryParse(queryParams['companyId']!)
                    : null,
                vehicleId: queryParams['vehicleId'] != null
                    ? int.tryParse(queryParams['vehicleId']!)
                    : null,
                loadId: queryParams['loadId'] != null
                    ? int.tryParse(queryParams['loadId']!)
                    : null,
                mode: queryParams['mode'] ?? 'all',
              );
            },
            routes: [
              GoRoute(
                path: '/upload',
                name: 'document-upload',
                builder: (context, state) {
                  final extra = state.extra as Map<String, dynamic>?;
                  return DocumentUploadScreen(
                    source: extra?['source'] as String?,
                    companyId: extra?['companyId'] as int?,
                    vehicleId: extra?['vehicleId'] as int?,
                    loadId: extra?['loadId'] as int?,
                  );
                },
              ),
              GoRoute(
                path: '/:id',
                name: 'document-view',
                builder: (context, state) {
                  final id = int.parse(state.pathParameters['id']!);
                  return DocumentViewScreen(documentId: id);
                },
              ),
            ],
          ),

          // Payments
          GoRoute(
            path: '/payments',
            name: 'payments',
            builder: (context, state) => BlocProvider(
              create: (context) =>
                  PaymentBloc(paymentService: getIt<PaymentService>()),
              child: const FinancialManagementScreen(),
            ),
            routes: [
              GoRoute(
                path: '/create',
                name: 'payment-create',
                builder: (context, state) => BlocProvider(
                  create: (context) =>
                      PaymentBloc(paymentService: getIt<PaymentService>()),
                  child: const PaymentCreateScreen(),
                ),
              ),
              GoRoute(
                path: '/:id',
                name: 'payment-details',
                builder: (context, state) {
                  final id = state.pathParameters['id']!;
                  return BlocProvider(
                    create: (context) =>
                        PaymentBloc(paymentService: getIt<PaymentService>()),
                    child: PaymentDetailsScreen(paymentId: int.parse(id)),
                  );
                },
                routes: [
                  GoRoute(
                    path: '/process',
                    name: 'payment-process',
                    builder: (context, state) {
                      final id = state.pathParameters['id']!;
                      return BlocProvider(
                        create: (context) => PaymentBloc(
                            paymentService: getIt<PaymentService>()),
                        child: PaymentProcessScreen(paymentId: int.parse(id)),
                      );
                    },
                  ),
                ],
              ),
              // Invoice routes under payments
              GoRoute(
                path: '/invoices/:id',
                name: 'payment-invoice-details',
                builder: (context, state) {
                  final id = state.pathParameters['id']!;
                  return BlocProvider(
                    create: (context) =>
                        InvoiceBloc(invoiceService: getIt<InvoiceService>()),
                    child: InvoiceDetailsScreen(invoiceId: id),
                  );
                },
              ),
            ],
          ),

          // Notifications
          GoRoute(
            path: '/notifications',
            name: 'notifications',
            builder: (context, state) => BlocProvider(
              create: (context) =>
                  NotificationBloc(getIt<NotificationApiService>()),
              child: const NotificationsScreen(),
            ),
            routes: [
              GoRoute(
                path: '/:id',
                name: 'notification-details',
                builder: (context, state) {
                  final id = int.parse(state.pathParameters['id']!);
                  return BlocProvider(
                    create: (context) =>
                        NotificationBloc(getIt<NotificationApiService>()),
                    child: NotificationDetailsScreen(notificationId: id),
                  );
                },
              ),
            ],
          ),

          // Admin (only for admin users)
          GoRoute(
            path: '/admin',
            name: 'admin',
            builder: (context, state) => const AdminDashboardScreen(),
            routes: [
              GoRoute(
                path: '/companies',
                name: 'admin-companies',
                builder: (context, state) => BlocProvider(
                  create: (context) => AdminBloc(getIt()),
                  child: const AdminCompaniesScreen(),
                ),
                routes: [
                  GoRoute(
                    path: '/:id',
                    name: 'admin-company-details',
                    builder: (context, state) {
                      final id = int.parse(state.pathParameters['id']!);
                      return BlocProvider(
                        create: (context) => AdminBloc(getIt()),
                        child: AdminCompanyDetailsScreen(companyId: id),
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      appBar: AppBar(title: const Text('Error')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Page not found',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              state.error?.toString() ?? 'Unknown error',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go('/loads'),
              child: const Text('Go Home'),
            ),
          ],
        ),
      ),
    ),
  );
}
