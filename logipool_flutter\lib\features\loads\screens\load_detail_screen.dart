import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

import '../../../shared/models/load_model.dart';
import '../../../shared/models/user_model.dart';
import '../../../shared/services/auth_service.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/di/service_locator.dart';
import '../../../shared/services/bid_service.dart';
import '../bloc/load_bloc.dart';
import '../../bids/bloc/bid_bloc.dart';
import '../../bids/screens/create_bid_screen.dart';
import '../../bids/screens/bid_list_screen.dart';

class LoadDetailScreen extends StatefulWidget {
  final String loadId;

  const LoadDetailScreen({super.key, required this.loadId});

  @override
  State<LoadDetailScreen> createState() => _LoadDetailScreenState();
}

class _LoadDetailScreenState extends State<LoadDetailScreen> {
  @override
  void initState() {
    super.initState();
    context.read<LoadBloc>().add(
          LoadFetchByIdRequested(id: int.parse(widget.loadId)),
        );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocConsumer<LoadBloc, LoadState>(
        listener: (context, state) {
          if (state is LoadDeleteSuccess) {
            // Show success message and navigate back
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Load deleted successfully'),
                backgroundColor: Colors.green,
              ),
            );
            Navigator.pop(context);
          } else if (state is LoadUpdateSuccess) {
            // Refresh load details to show updated information
            context.read<LoadBloc>().add(
                  LoadFetchByIdRequested(id: int.parse(widget.loadId)),
                );
          } else if (state is LoadError) {
            // Show error message
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is LoadLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state is LoadError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading load details',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    state.message,
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      context.read<LoadBloc>().add(
                            LoadFetchByIdRequested(
                                id: int.parse(widget.loadId)),
                          );
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          if (state is LoadDetailLoaded) {
            return _buildLoadDetail(context, state.load);
          }

          if (state is LoadUpdateSuccess) {
            // Show the updated load immediately
            return _buildLoadDetail(context, state.load);
          }

          if (state is LoadDeleteSuccess) {
            // This state is handled in the listener, but we need to show something
            // while the navigation is happening
            return const Center(child: CircularProgressIndicator());
          }

          // Handle other states (like LoadListLoaded, LoadInitial, etc.)
          // Show loading indicator while the fetch request is being processed
          return const Center(child: CircularProgressIndicator());
        },
      ),
      floatingActionButton: BlocBuilder<LoadBloc, LoadState>(
        builder: (context, state) {
          if (state is LoadDetailLoaded) {
            return _buildFloatingActionButton(context, state.load);
          }
          return const SizedBox.shrink();
        },
      ),
    );
  }

  Widget _buildLoadDetail(BuildContext context, LoadModel load) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return CustomScrollView(
      slivers: [
        // App Bar
        SliverAppBar(
          expandedHeight: 200,
          pinned: true,
          flexibleSpace: FlexibleSpaceBar(
            title: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  load.title,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    shadows: [
                      Shadow(
                        offset: Offset(0, 1),
                        blurRadius: 3,
                        color: Colors.black54,
                      ),
                    ],
                  ),
                ),
                Text(
                  'ID: ${load.id ?? 'N/A'}${load.trackingNumber != null ? ' • ${load.trackingNumber}' : ''}',
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    shadows: [
                      Shadow(
                        offset: Offset(0, 1),
                        blurRadius: 3,
                        color: Colors.black54,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            background: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    colorScheme.primary,
                    colorScheme.primary.withOpacity(0.8),
                  ],
                ),
              ),
              child: Stack(
                children: [
                  // Status chip positioned on the right
                  Positioned(
                    right: 16,
                    top: 100,
                    child: _buildStatusChip(context, load),
                  ),
                  // Manage button positioned above status chip for assigned companies
                  FutureBuilder<UserModel?>(
                    future: context.read<AuthService>().getCurrentUser(),
                    builder: (context, snapshot) {
                      final currentUser = snapshot.data;
                      final canManage = currentUser != null &&
                          load.assignedCompanyId != null &&
                          currentUser.company != null &&
                          int.tryParse(currentUser.company!.id) ==
                              load.assignedCompanyId;

                      if (!canManage) return const SizedBox.shrink();

                      return Positioned(
                        right: 16,
                        top: 60, // Position above the status chip
                        child: ElevatedButton.icon(
                          onPressed: () =>
                              context.push('/loads/${load.id}/manage'),
                          icon: const Icon(Icons.settings, size: 16),
                          label: const Text('Manage'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.white,
                            foregroundColor: colorScheme.primary,
                            elevation: 4,
                            shadowColor: Colors.black26,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            minimumSize: const Size(0, 32), // Compact size
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
          actions: [
            FutureBuilder<UserModel?>(
              future: context.read<AuthService>().getCurrentUser(),
              builder: (context, snapshot) {
                final currentUser = snapshot.data;
                final isOwner = currentUser != null &&
                    load.clientId != null &&
                    int.parse(currentUser.id) == load.clientId;

                return PopupMenuButton<String>(
                  onSelected: (value) {
                    switch (value) {
                      case 'edit':
                        // TODO: Navigate to edit screen
                        break;
                      case 'delete':
                        _showDeleteConfirmation(context, load);
                        break;
                      case 'manage':
                        context.push('/loads/${load.id}/manage');
                        break;
                      case 'lifecycle':
                        context.push('/loads/${load.id}/lifecycle');
                        break;
                      case 'share':
                        // TODO: Implement share functionality
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    if (isOwner) ...[
                      const PopupMenuItem(
                        value: 'edit',
                        child: ListTile(
                          leading: Icon(Icons.edit),
                          title: Text('Edit Load'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                      if (load.status == LoadStatus.posted)
                        const PopupMenuItem(
                          value: 'delete',
                          child: ListTile(
                            leading: Icon(Icons.delete, color: Colors.red),
                            title: Text(
                              'Delete Load',
                              style: TextStyle(color: Colors.red),
                            ),
                            contentPadding: EdgeInsets.zero,
                          ),
                        ),
                    ],
                    // Show manage option for assigned companies
                    if (load.assignedCompanyId != null &&
                        currentUser?.company != null &&
                        int.tryParse(currentUser!.company!.id) ==
                            load.assignedCompanyId)
                      const PopupMenuItem(
                        value: 'manage',
                        child: ListTile(
                          leading: Icon(Icons.settings),
                          title: Text('Manage Load'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                    // Show lifecycle option for load owners and assigned companies
                    if (isOwner ||
                        (load.assignedCompanyId != null &&
                            currentUser?.company != null &&
                            int.tryParse(currentUser!.company!.id) ==
                                load.assignedCompanyId))
                      const PopupMenuItem(
                        value: 'lifecycle',
                        child: ListTile(
                          leading: Icon(Icons.timeline),
                          title: Text('Load Lifecycle'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                    const PopupMenuItem(
                      value: 'share',
                      child: ListTile(
                        leading: Icon(Icons.share),
                        title: Text('Share Load'),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                  ],
                );
              },
            ),
          ],
        ),

        // Content
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Route Information
                _buildRouteCard(context, load),

                const SizedBox(height: 16),

                // Load Details
                _buildLoadDetailsCard(context, load),

                const SizedBox(height: 16),

                // Schedule
                _buildScheduleCard(context, load),

                const SizedBox(height: 16),

                // Payment Information
                if (load.paymentType != null || load.paymentRate != null)
                  _buildPaymentCard(context, load),

                const SizedBox(height: 16),

                // Special Requirements
                if (load.requiresInsurance ||
                    load.requiresSpecialHandling ||
                    load.specialInstructions != null)
                  _buildSpecialRequirementsCard(context, load),

                const SizedBox(height: 16),

                // Description
                if (load.description != null)
                  _buildDescriptionCard(context, load),

                const SizedBox(height: 16),

                // Bidding Information
                _buildBiddingCard(context, load),

                const SizedBox(height: 100), // Space for FAB
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStatusChip(BuildContext context, LoadModel load) {
    final theme = Theme.of(context);
    Color backgroundColor;
    Color textColor;

    switch (load.status) {
      case LoadStatus.posted:
        backgroundColor = Colors.green[100]!;
        textColor = Colors.green[700]!;
        break;
      case LoadStatus.biddingClosed:
        backgroundColor = Colors.orange[100]!;
        textColor = Colors.orange[700]!;
        break;
      case LoadStatus.assigned:
        backgroundColor = Colors.blue[100]!;
        textColor = Colors.blue[700]!;
        break;
      case LoadStatus.inTransit:
        backgroundColor = Colors.purple[100]!;
        textColor = Colors.purple[700]!;
        break;
      case LoadStatus.delivered:
        backgroundColor = Colors.teal[100]!;
        textColor = Colors.teal[700]!;
        break;
      case LoadStatus.completed:
        backgroundColor = Colors.green[100]!;
        textColor = Colors.green[700]!;
        break;
      case LoadStatus.cancelled:
        backgroundColor = Colors.red[100]!;
        textColor = Colors.red[700]!;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Text(
        load.status.displayName,
        style: theme.textTheme.labelMedium?.copyWith(
          color: textColor,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildRouteCard(BuildContext context, LoadModel load) {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.route, color: theme.colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Route Information',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Route visualization
            Row(
              children: [
                Column(
                  children: [
                    Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        color: Colors.green,
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                    Container(width: 2, height: 40, color: Colors.grey[300]),
                    Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                  ],
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'From',
                        style: theme.textTheme.labelSmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        load.pickupLocation,
                        style: theme.textTheme.bodyLarge?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 24),
                      Text(
                        'To',
                        style: theme.textTheme.labelSmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        load.deliveryLocation,
                        style: theme.textTheme.bodyLarge?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            if (load.estimatedDistance != null) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surfaceVariant,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.straighten,
                      size: 16,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Estimated Distance: ${load.estimatedDistance!.toStringAsFixed(1)} ${load.distanceUnit}',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildLoadDetailsCard(BuildContext context, LoadModel load) {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.inventory_2, color: theme.colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Load Details',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Load ID
            _buildDetailRow(
              context,
              'Load ID',
              '${load.id ?? 'N/A'}',
              Icons.tag,
            ),

            if (load.trackingNumber != null) ...[
              const SizedBox(height: 12),
              _buildDetailRow(
                context,
                'Tracking Number',
                load.trackingNumber!,
                Icons.qr_code,
              ),
            ],

            const SizedBox(height: 12),

            // Cargo Type
            _buildDetailRow(
              context,
              'Cargo Type',
              load.cargoType,
              Icons.category,
            ),

            const SizedBox(height: 12),

            // Weight
            _buildDetailRow(
              context,
              'Weight',
              '${load.weight.toStringAsFixed(1)} ${load.weightUnit}',
              Icons.scale,
            ),

            if (load.volume != null) ...[
              const SizedBox(height: 12),
              _buildDetailRow(
                context,
                'Volume',
                '${load.volume!.toStringAsFixed(1)} ${load.volumeUnit}',
                Icons.view_in_ar,
              ),
            ],

            if (load.loadType != null) ...[
              const SizedBox(height: 12),
              _buildDetailRow(
                context,
                'Load Type',
                load.loadType!.displayName,
                Icons.local_shipping,
              ),
            ],

            const SizedBox(height: 12),
            _buildDetailRow(
              context,
              'Priority',
              load.priority.displayName,
              _getPriorityIcon(load.priority),
            ),

            if (load.estimatedValue != null) ...[
              const SizedBox(height: 12),
              _buildDetailRow(
                context,
                'Estimated Value',
                '\$${load.estimatedValue!.toStringAsFixed(2)}',
                Icons.attach_money,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildScheduleCard(BuildContext context, LoadModel load) {
    final theme = Theme.of(context);
    final dateFormat = DateFormat('MMM dd, yyyy');
    final timeFormat = DateFormat('HH:mm');

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.schedule, color: theme.colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Schedule',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Pickup Date
            _buildDateTimeRow(
              context,
              'Pickup Date',
              load.pickupDate,
              Icons.upload,
              Colors.green,
            ),

            const SizedBox(height: 12),

            // Delivery Date
            _buildDateTimeRow(
              context,
              'Delivery Date',
              load.deliveryDate,
              Icons.download,
              Colors.blue,
            ),

            if (load.biddingClosesAt != null) ...[
              const SizedBox(height: 12),
              _buildDateTimeRow(
                context,
                'Bidding Closes',
                load.biddingClosesAt!,
                Icons.timer,
                Colors.orange,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentCard(BuildContext context, LoadModel load) {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.payment, color: theme.colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Payment Information',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (load.paymentType != null)
              _buildDetailRow(
                context,
                'Payment Type',
                load.paymentType!.displayName,
                Icons.account_balance_wallet,
              ),
            if (load.paymentRate != null) ...[
              if (load.paymentType != null) const SizedBox(height: 12),
              _buildDetailRow(
                context,
                'Payment Rate',
                '\$${load.paymentRate!.toStringAsFixed(2)}${load.paymentUnit != null ? '/${load.paymentUnit}' : ''}',
                Icons.attach_money,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSpecialRequirementsCard(BuildContext context, LoadModel load) {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.warning_amber, color: theme.colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Special Requirements',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (load.requiresInsurance)
              _buildRequirementChip(
                context,
                'Insurance Required',
                Icons.security,
                Colors.blue,
              ),
            if (load.requiresSpecialHandling) ...[
              if (load.requiresInsurance) const SizedBox(height: 8),
              _buildRequirementChip(
                context,
                'Special Handling Required',
                Icons.warning,
                Colors.orange,
              ),
            ],
            if (load.specialInstructions != null) ...[
              const SizedBox(height: 16),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surfaceVariant,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Special Instructions',
                      style: theme.textTheme.labelMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      load.specialInstructions!,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDescriptionCard(BuildContext context, LoadModel load) {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.description, color: theme.colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Description',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(load.description!, style: theme.textTheme.bodyMedium),
          ],
        ),
      ),
    );
  }

  Widget _buildBiddingCard(BuildContext context, LoadModel load) {
    final theme = Theme.of(context);

    return FutureBuilder<UserModel?>(
      future: context.read<AuthService>().getCurrentUser(),
      builder: (context, snapshot) {
        final currentUser = snapshot.data;
        final canViewBids = currentUser != null &&
            int.parse(currentUser.id) == load.clientId &&
            load.bidCount > 0;

        return Card(
          child: InkWell(
            onTap: canViewBids ? () => _navigateToBidList(context, load) : null,
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.local_offer, color: theme.colorScheme.primary),
                      const SizedBox(width: 8),
                      Text(
                        'Bidding Information',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.primaryContainer,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Column(
                            children: [
                              Text(
                                '${load.bidCount}',
                                style: theme.textTheme.headlineMedium?.copyWith(
                                  color: theme.colorScheme.onPrimaryContainer,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                'Total Bids',
                                style: theme.textTheme.labelMedium?.copyWith(
                                  color: theme.colorScheme.onPrimaryContainer,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: load.isVerified
                                ? Colors.green[100]
                                : Colors.grey[100],
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Column(
                            children: [
                              Icon(
                                load.isVerified
                                    ? Icons.verified
                                    : Icons.help_outline,
                                color: load.isVerified
                                    ? Colors.green[700]
                                    : Colors.grey[600],
                                size: 32,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                load.isVerified ? 'Verified' : 'Unverified',
                                style: theme.textTheme.labelMedium?.copyWith(
                                  color: load.isVerified
                                      ? Colors.green[700]
                                      : Colors.grey[600],
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildDetailRow(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    final theme = Theme.of(context);

    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.grey[600]),
        const SizedBox(width: 8),
        Text(
          '$label: ',
          style: theme.textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
        ),
        Expanded(
          child: Text(
            value,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDateTimeRow(
    BuildContext context,
    String label,
    DateTime dateTime,
    IconData icon,
    Color color,
  ) {
    final theme = Theme.of(context);
    final dateFormat = DateFormat('MMM dd, yyyy');
    final timeFormat = DateFormat('HH:mm');

    return Row(
      children: [
        Icon(icon, size: 16, color: color),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: theme.textTheme.labelMedium?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              Text(
                '${dateFormat.format(dateTime)} at ${timeFormat.format(dateTime)}',
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildRequirementChip(
    BuildContext context,
    String label,
    IconData icon,
    Color color,
  ) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withOpacity(0.3), width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 8),
          Text(
            label,
            style: theme.textTheme.labelMedium?.copyWith(
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getPriorityIcon(Priority priority) {
    switch (priority) {
      case Priority.low:
        return Icons.keyboard_arrow_down;
      case Priority.normal:
        return Icons.remove;
      case Priority.high:
        return Icons.keyboard_arrow_up;
      case Priority.urgent:
        return Icons.priority_high;
    }
  }

  Future<void> _showDeleteConfirmation(
    BuildContext context,
    LoadModel load,
  ) async {
    // Add debug logging
    final currentUser = await context.read<AuthService>().getCurrentUser();
    print('DEBUG Delete Confirmation:');
    print('  Current user: ${currentUser?.username} (ID: ${currentUser?.id})');
    print('  Load client ID: ${load.clientId}');
    print('  Load client name: ${load.clientName}');
    print('  Load status: ${load.status}');

    if (!context.mounted) return;

    await showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Load'),
        content: Text(
          'Are you sure you want to delete "${load.title}"? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context); // Close dialog
              context.read<LoadBloc>().add(LoadDeleteRequested(id: load.id!));
              // Don't navigate here - let the BlocConsumer handle it
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButton(BuildContext context, LoadModel load) {
    return FutureBuilder<UserModel?>(
      future: context.read<AuthService>().getCurrentUser(),
      builder: (context, snapshot) {
        final currentUser = snapshot.data;

        if (currentUser == null) {
          return const SizedBox.shrink();
        }

        // Debug logging
        print('DEBUG: Current user role: ${currentUser.role}');
        print('DEBUG: Expected role: ${AppConstants.roleTransporter}');
        print('DEBUG: Load status: ${load.status}');
        print('DEBUG: Bidding closes at: ${load.biddingClosesAt}');
        print('DEBUG: Current time: ${DateTime.now()}');

        // Show different buttons based on user role and load status
        if (currentUser.role == AppConstants.roleTransporter &&
            load.status == LoadStatus.posted &&
            load.biddingClosesAt != null &&
            load.biddingClosesAt!.isAfter(DateTime.now())) {
          return FloatingActionButton.extended(
            onPressed: () => _navigateToCreateBid(context, load),
            icon: const Icon(Icons.local_offer),
            label: const Text('Place Bid'),
            backgroundColor: Theme.of(context).colorScheme.primary,
          );
        }

        if (int.parse(currentUser.id) == load.clientId && load.bidCount > 0) {
          return FloatingActionButton.extended(
            onPressed: () => _navigateToBidList(context, load),
            icon: const Icon(Icons.list),
            label: Text('View Bids (${load.bidCount})'),
            backgroundColor: Theme.of(context).colorScheme.secondary,
          );
        }

        // Show informative message for transporters when bidding is not available
        if (currentUser.role == AppConstants.roleTransporter) {
          return _buildBiddingStatusInfo(context, load);
        }

        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildBiddingStatusInfo(BuildContext context, LoadModel load) {
    String message;
    IconData icon;
    Color? backgroundColor;

    if (load.status != LoadStatus.posted) {
      message = 'Load ${load.status.name.toLowerCase()}';
      icon = Icons.info_outline;
      backgroundColor = Colors.grey;
    } else if (load.biddingClosesAt == null) {
      message = 'No bidding period set';
      icon = Icons.schedule_outlined;
      backgroundColor = Colors.orange;
    } else if (load.biddingClosesAt!.isBefore(DateTime.now())) {
      message = 'Bidding closed';
      icon = Icons.timer_off_outlined;
      backgroundColor = Colors.red;
    } else {
      // This shouldn't happen as the bid button should be shown
      message = 'Cannot bid';
      icon = Icons.block;
      backgroundColor = Colors.grey;
    }

    return FloatingActionButton.extended(
      onPressed: () => _showBiddingInfoDialog(context, load),
      icon: Icon(icon),
      label: Text(message),
      backgroundColor: backgroundColor,
    );
  }

  void _showBiddingInfoDialog(BuildContext context, LoadModel load) {
    String title;
    String content;
    List<String> requirements = [];

    if (load.status != LoadStatus.posted) {
      title = 'Load Not Available for Bidding';
      content =
          'This load has status: ${load.status.name.toLowerCase()}. Only loads with "posted" status accept bids.';
    } else if (load.biddingClosesAt == null) {
      title = 'No Bidding Period Set';
      content = 'The client has not set a bidding deadline for this load.';
    } else if (load.biddingClosesAt!.isBefore(DateTime.now())) {
      title = 'Bidding Period Closed';
      content =
          'The bidding period for this load ended on ${DateFormat('MMM dd, yyyy \'at\' HH:mm').format(load.biddingClosesAt!)}.';
    } else {
      title = 'Bidding Requirements';
      content = 'To place bids on loads, you need:';
      requirements = [
        'Transporter role (✓ You have this)',
        'Verified company profile',
        'Load must be in "posted" status',
        'Bidding period must be open',
      ];
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(content),
            if (requirements.isNotEmpty) ...[
              const SizedBox(height: 16),
              ...requirements.map(
                (req) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: 2),
                  child: Text('• $req'),
                ),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _navigateToCreateBid(BuildContext context, LoadModel load) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BlocProvider(
          create: (context) => BidBloc(getIt<BidService>()),
          child: CreateBidScreen(load: load),
        ),
      ),
    ).then((result) {
      if (result == true) {
        // Refresh load details to update bid count
        context.read<LoadBloc>().add(
              LoadFetchByIdRequested(id: int.parse(widget.loadId)),
            );
      }
    });
  }

  void _navigateToBidList(BuildContext context, LoadModel load) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BlocProvider(
          create: (context) => BidBloc(getIt<BidService>()),
          child: BidListScreen(load: load),
        ),
      ),
    ).then((result) {
      if (result == true) {
        // Refresh load details to update status if bid was accepted
        context.read<LoadBloc>().add(
              LoadFetchByIdRequested(id: int.parse(widget.loadId)),
            );
      }
    });
  }
}
