// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'admin_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DashboardOverview _$DashboardOverviewFromJson(Map<String, dynamic> json) =>
    DashboardOverview(
      totalUsers: (json['totalUsers'] as num?)?.toInt(),
      totalCompanies: (json['totalCompanies'] as num?)?.toInt(),
      totalLoads: (json['totalLoads'] as num?)?.toInt(),
      totalBids: (json['totalBids'] as num?)?.toInt(),
      activeLoads: (json['activeLoads'] as num?)?.toInt(),
      completedLoads: (json['completedLoads'] as num?)?.toInt(),
      pendingVerifications: (json['pendingVerifications'] as num?)?.toInt(),
      totalRevenue: (json['totalRevenue'] as num?)?.toDouble(),
      monthlyRevenue: (json['monthlyRevenue'] as num?)?.toDouble(),
      recentActivities: (json['recentActivities'] as List<dynamic>?)
          ?.map((e) => RecentActivity.fromJson(e as Map<String, dynamic>))
          .toList(),
      systemHealth: json['systemHealth'] == null
          ? null
          : SystemHealth.fromJson(json['systemHealth'] as Map<String, dynamic>),
      generatedAt: json['generatedAt'] == null
          ? null
          : DateTime.parse(json['generatedAt'] as String),
    );

Map<String, dynamic> _$DashboardOverviewToJson(DashboardOverview instance) =>
    <String, dynamic>{
      'totalUsers': instance.totalUsers,
      'totalCompanies': instance.totalCompanies,
      'totalLoads': instance.totalLoads,
      'totalBids': instance.totalBids,
      'activeLoads': instance.activeLoads,
      'completedLoads': instance.completedLoads,
      'pendingVerifications': instance.pendingVerifications,
      'totalRevenue': instance.totalRevenue,
      'monthlyRevenue': instance.monthlyRevenue,
      'recentActivities': instance.recentActivities,
      'systemHealth': instance.systemHealth,
      'generatedAt': instance.generatedAt?.toIso8601String(),
    };

RecentActivity _$RecentActivityFromJson(Map<String, dynamic> json) =>
    RecentActivity(
      type: json['type'] as String?,
      description: json['description'] as String?,
      entityType: json['entityType'] as String?,
      entityId: (json['entityId'] as num?)?.toInt(),
      performedBy: json['performedBy'] as String?,
      timestamp: json['timestamp'] == null
          ? null
          : DateTime.parse(json['timestamp'] as String),
    );

Map<String, dynamic> _$RecentActivityToJson(RecentActivity instance) =>
    <String, dynamic>{
      'type': instance.type,
      'description': instance.description,
      'entityType': instance.entityType,
      'entityId': instance.entityId,
      'performedBy': instance.performedBy,
      'timestamp': instance.timestamp?.toIso8601String(),
    };

SystemHealth _$SystemHealthFromJson(Map<String, dynamic> json) => SystemHealth(
      status: json['status'] as String?,
      cpuUsage: (json['cpuUsage'] as num?)?.toDouble(),
      memoryUsage: (json['memoryUsage'] as num?)?.toDouble(),
      diskUsage: (json['diskUsage'] as num?)?.toDouble(),
      activeConnections: (json['activeConnections'] as num?)?.toInt(),
      responseTime: (json['responseTime'] as num?)?.toDouble(),
      lastChecked: json['lastChecked'] == null
          ? null
          : DateTime.parse(json['lastChecked'] as String),
    );

Map<String, dynamic> _$SystemHealthToJson(SystemHealth instance) =>
    <String, dynamic>{
      'status': instance.status,
      'cpuUsage': instance.cpuUsage,
      'memoryUsage': instance.memoryUsage,
      'diskUsage': instance.diskUsage,
      'activeConnections': instance.activeConnections,
      'responseTime': instance.responseTime,
      'lastChecked': instance.lastChecked?.toIso8601String(),
    };

UserManagementResponse _$UserManagementResponseFromJson(
        Map<String, dynamic> json) =>
    UserManagementResponse(
      id: (json['id'] as num?)?.toInt(),
      username: json['username'] as String?,
      email: json['email'] as String?,
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
      phoneNumber: json['phoneNumber'] as String?,
      status: json['status'] as String?,
      roles:
          (json['roles'] as List<dynamic>?)?.map((e) => e as String).toList(),
      companyName: json['companyName'] as String?,
      companyId: (json['companyId'] as num?)?.toInt(),
      isVerified: json['isVerified'] as bool?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      lastLoginAt: json['lastLoginAt'] == null
          ? null
          : DateTime.parse(json['lastLoginAt'] as String),
    );

Map<String, dynamic> _$UserManagementResponseToJson(
        UserManagementResponse instance) =>
    <String, dynamic>{
      'id': instance.id,
      'username': instance.username,
      'email': instance.email,
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'phoneNumber': instance.phoneNumber,
      'status': instance.status,
      'roles': instance.roles,
      'companyName': instance.companyName,
      'companyId': instance.companyId,
      'isVerified': instance.isVerified,
      'createdAt': instance.createdAt?.toIso8601String(),
      'lastLoginAt': instance.lastLoginAt?.toIso8601String(),
    };

CompanyManagementResponse _$CompanyManagementResponseFromJson(
        Map<String, dynamic> json) =>
    CompanyManagementResponse(
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] as String?,
      registrationNumber: json['registrationNumber'] as String?,
      email: json['email'] as String?,
      phoneNumber: json['phoneNumber'] as String?,
      address: json['address'] as String?,
      verificationStatus: json['verificationStatus'] as String?,
      ownerName: json['ownerName'] as String?,
      ownerUsername: json['ownerUsername'] as String?,
      ownerId: (json['ownerId'] as num?)?.toInt(),
      totalVehicles: (json['totalVehicles'] as num?)?.toInt(),
      totalLoads: (json['totalLoads'] as num?)?.toInt(),
      totalRevenue: (json['totalRevenue'] as num?)?.toDouble(),
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      verifiedAt: json['verifiedAt'] == null
          ? null
          : DateTime.parse(json['verifiedAt'] as String),
    );

Map<String, dynamic> _$CompanyManagementResponseToJson(
        CompanyManagementResponse instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'registrationNumber': instance.registrationNumber,
      'email': instance.email,
      'phoneNumber': instance.phoneNumber,
      'address': instance.address,
      'verificationStatus': instance.verificationStatus,
      'ownerName': instance.ownerName,
      'ownerUsername': instance.ownerUsername,
      'ownerId': instance.ownerId,
      'totalVehicles': instance.totalVehicles,
      'totalLoads': instance.totalLoads,
      'totalRevenue': instance.totalRevenue,
      'createdAt': instance.createdAt?.toIso8601String(),
      'verifiedAt': instance.verifiedAt?.toIso8601String(),
    };

LoadManagementResponse _$LoadManagementResponseFromJson(
        Map<String, dynamic> json) =>
    LoadManagementResponse(
      id: (json['id'] as num?)?.toInt(),
      title: json['title'] as String?,
      description: json['description'] as String?,
      pickupLocation: json['pickupLocation'] as String?,
      deliveryLocation: json['deliveryLocation'] as String?,
      weight: (json['weight'] as num?)?.toDouble(),
      price: (json['price'] as num?)?.toDouble(),
      status: json['status'] as String?,
      clientName: json['clientName'] as String?,
      clientUsername: json['clientUsername'] as String?,
      clientId: (json['clientId'] as num?)?.toInt(),
      assignedCompanyName: json['assignedCompanyName'] as String?,
      assignedCompanyId: (json['assignedCompanyId'] as num?)?.toInt(),
      totalBids: (json['totalBids'] as num?)?.toInt(),
      isVerified: json['isVerified'] as bool?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      deliveryDate: json['deliveryDate'] == null
          ? null
          : DateTime.parse(json['deliveryDate'] as String),
    );

Map<String, dynamic> _$LoadManagementResponseToJson(
        LoadManagementResponse instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'pickupLocation': instance.pickupLocation,
      'deliveryLocation': instance.deliveryLocation,
      'weight': instance.weight,
      'price': instance.price,
      'status': instance.status,
      'clientName': instance.clientName,
      'clientUsername': instance.clientUsername,
      'clientId': instance.clientId,
      'assignedCompanyName': instance.assignedCompanyName,
      'assignedCompanyId': instance.assignedCompanyId,
      'totalBids': instance.totalBids,
      'isVerified': instance.isVerified,
      'createdAt': instance.createdAt?.toIso8601String(),
      'deliveryDate': instance.deliveryDate?.toIso8601String(),
    };

SystemAnalytics _$SystemAnalyticsFromJson(Map<String, dynamic> json) =>
    SystemAnalytics(
      userAnalytics: json['userAnalytics'] == null
          ? null
          : UserAnalytics.fromJson(
              json['userAnalytics'] as Map<String, dynamic>),
      loadAnalytics: json['loadAnalytics'] == null
          ? null
          : LoadAnalytics.fromJson(
              json['loadAnalytics'] as Map<String, dynamic>),
      revenueAnalytics: json['revenueAnalytics'] == null
          ? null
          : RevenueAnalytics.fromJson(
              json['revenueAnalytics'] as Map<String, dynamic>),
      performanceAnalytics: json['performanceAnalytics'] == null
          ? null
          : PerformanceAnalytics.fromJson(
              json['performanceAnalytics'] as Map<String, dynamic>),
      generatedAt: json['generatedAt'] == null
          ? null
          : DateTime.parse(json['generatedAt'] as String),
    );

Map<String, dynamic> _$SystemAnalyticsToJson(SystemAnalytics instance) =>
    <String, dynamic>{
      'userAnalytics': instance.userAnalytics,
      'loadAnalytics': instance.loadAnalytics,
      'revenueAnalytics': instance.revenueAnalytics,
      'performanceAnalytics': instance.performanceAnalytics,
      'generatedAt': instance.generatedAt?.toIso8601String(),
    };

UserAnalytics _$UserAnalyticsFromJson(Map<String, dynamic> json) =>
    UserAnalytics(
      totalUsers: (json['totalUsers'] as num?)?.toInt(),
      activeUsers: (json['activeUsers'] as num?)?.toInt(),
      newUsersThisMonth: (json['newUsersThisMonth'] as num?)?.toInt(),
      clientUsers: (json['clientUsers'] as num?)?.toInt(),
      transporterUsers: (json['transporterUsers'] as num?)?.toInt(),
      adminUsers: (json['adminUsers'] as num?)?.toInt(),
      usersByStatus: (json['usersByStatus'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, (e as num).toInt()),
      ),
      registrationTrends:
          (json['registrationTrends'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, (e as num).toInt()),
      ),
    );

Map<String, dynamic> _$UserAnalyticsToJson(UserAnalytics instance) =>
    <String, dynamic>{
      'totalUsers': instance.totalUsers,
      'activeUsers': instance.activeUsers,
      'newUsersThisMonth': instance.newUsersThisMonth,
      'clientUsers': instance.clientUsers,
      'transporterUsers': instance.transporterUsers,
      'adminUsers': instance.adminUsers,
      'usersByStatus': instance.usersByStatus,
      'registrationTrends': instance.registrationTrends,
    };

LoadAnalytics _$LoadAnalyticsFromJson(Map<String, dynamic> json) =>
    LoadAnalytics(
      totalLoads: (json['totalLoads'] as num?)?.toInt(),
      activeLoads: (json['activeLoads'] as num?)?.toInt(),
      completedLoads: (json['completedLoads'] as num?)?.toInt(),
      newLoadsThisMonth: (json['newLoadsThisMonth'] as num?)?.toInt(),
      loadsByStatus: (json['loadsByStatus'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, (e as num).toInt()),
      ),
      loadTrends: (json['loadTrends'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, (e as num).toInt()),
      ),
      averageLoadValue: (json['averageLoadValue'] as num?)?.toDouble(),
      totalLoadValue: (json['totalLoadValue'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$LoadAnalyticsToJson(LoadAnalytics instance) =>
    <String, dynamic>{
      'totalLoads': instance.totalLoads,
      'activeLoads': instance.activeLoads,
      'completedLoads': instance.completedLoads,
      'newLoadsThisMonth': instance.newLoadsThisMonth,
      'loadsByStatus': instance.loadsByStatus,
      'loadTrends': instance.loadTrends,
      'averageLoadValue': instance.averageLoadValue,
      'totalLoadValue': instance.totalLoadValue,
    };

RevenueAnalytics _$RevenueAnalyticsFromJson(Map<String, dynamic> json) =>
    RevenueAnalytics(
      totalRevenue: (json['totalRevenue'] as num?)?.toDouble(),
      monthlyRevenue: (json['monthlyRevenue'] as num?)?.toDouble(),
      averageCommission: (json['averageCommission'] as num?)?.toDouble(),
      totalCommissions: (json['totalCommissions'] as num?)?.toDouble(),
      revenueTrends: (json['revenueTrends'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
      commissionTrends:
          (json['commissionTrends'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
    );

Map<String, dynamic> _$RevenueAnalyticsToJson(RevenueAnalytics instance) =>
    <String, dynamic>{
      'totalRevenue': instance.totalRevenue,
      'monthlyRevenue': instance.monthlyRevenue,
      'averageCommission': instance.averageCommission,
      'totalCommissions': instance.totalCommissions,
      'revenueTrends': instance.revenueTrends,
      'commissionTrends': instance.commissionTrends,
    };

PerformanceAnalytics _$PerformanceAnalyticsFromJson(
        Map<String, dynamic> json) =>
    PerformanceAnalytics(
      averageResponseTime: (json['averageResponseTime'] as num?)?.toDouble(),
      totalApiCalls: (json['totalApiCalls'] as num?)?.toInt(),
      errorCount: (json['errorCount'] as num?)?.toInt(),
      errorRate: (json['errorRate'] as num?)?.toDouble(),
      endpointUsage: (json['endpointUsage'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, (e as num).toInt()),
      ),
      performanceMetrics:
          (json['performanceMetrics'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
    );

Map<String, dynamic> _$PerformanceAnalyticsToJson(
        PerformanceAnalytics instance) =>
    <String, dynamic>{
      'averageResponseTime': instance.averageResponseTime,
      'totalApiCalls': instance.totalApiCalls,
      'errorCount': instance.errorCount,
      'errorRate': instance.errorRate,
      'endpointUsage': instance.endpointUsage,
      'performanceMetrics': instance.performanceMetrics,
    };

SystemConfigurationResponse _$SystemConfigurationResponseFromJson(
        Map<String, dynamic> json) =>
    SystemConfigurationResponse(
      applicationSettings: json['applicationSettings'] as Map<String, dynamic>?,
      securitySettings: json['securitySettings'] as Map<String, dynamic>?,
      notificationSettings:
          json['notificationSettings'] as Map<String, dynamic>?,
      paymentSettings: json['paymentSettings'] as Map<String, dynamic>?,
      trackingSettings: json['trackingSettings'] as Map<String, dynamic>?,
      fileSettings: json['fileSettings'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$SystemConfigurationResponseToJson(
        SystemConfigurationResponse instance) =>
    <String, dynamic>{
      'applicationSettings': instance.applicationSettings,
      'securitySettings': instance.securitySettings,
      'notificationSettings': instance.notificationSettings,
      'paymentSettings': instance.paymentSettings,
      'trackingSettings': instance.trackingSettings,
      'fileSettings': instance.fileSettings,
    };

AuditLogResponse _$AuditLogResponseFromJson(Map<String, dynamic> json) =>
    AuditLogResponse(
      id: (json['id'] as num?)?.toInt(),
      action: json['action'] as String?,
      entityType: json['entityType'] as String?,
      entityId: (json['entityId'] as num?)?.toInt(),
      details: json['details'] as String?,
      performedBy: json['performedBy'] as String?,
      ipAddress: json['ipAddress'] as String?,
      userAgent: json['userAgent'] as String?,
      timestamp: json['timestamp'] == null
          ? null
          : DateTime.parse(json['timestamp'] as String),
    );

Map<String, dynamic> _$AuditLogResponseToJson(AuditLogResponse instance) =>
    <String, dynamic>{
      'id': instance.id,
      'action': instance.action,
      'entityType': instance.entityType,
      'entityId': instance.entityId,
      'details': instance.details,
      'performedBy': instance.performedBy,
      'ipAddress': instance.ipAddress,
      'userAgent': instance.userAgent,
      'timestamp': instance.timestamp?.toIso8601String(),
    };

SystemAlertResponse _$SystemAlertResponseFromJson(Map<String, dynamic> json) =>
    SystemAlertResponse(
      id: (json['id'] as num?)?.toInt(),
      type: json['type'] as String?,
      severity: json['severity'] as String?,
      title: json['title'] as String?,
      message: json['message'] as String?,
      source: json['source'] as String?,
      acknowledged: json['acknowledged'] as bool?,
      acknowledgedBy: json['acknowledgedBy'] as String?,
      acknowledgedAt: json['acknowledgedAt'] == null
          ? null
          : DateTime.parse(json['acknowledgedAt'] as String),
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$SystemAlertResponseToJson(
        SystemAlertResponse instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': instance.type,
      'severity': instance.severity,
      'title': instance.title,
      'message': instance.message,
      'source': instance.source,
      'acknowledged': instance.acknowledged,
      'acknowledgedBy': instance.acknowledgedBy,
      'acknowledgedAt': instance.acknowledgedAt?.toIso8601String(),
      'createdAt': instance.createdAt?.toIso8601String(),
    };

SystemBackupResponse _$SystemBackupResponseFromJson(
        Map<String, dynamic> json) =>
    SystemBackupResponse(
      backupId: json['backupId'] as String?,
      type: json['type'] as String?,
      status: json['status'] as String?,
      sizeInBytes: (json['sizeInBytes'] as num?)?.toInt(),
      location: json['location'] as String?,
      initiatedBy: json['initiatedBy'] as String?,
      initiatedAt: json['initiatedAt'] == null
          ? null
          : DateTime.parse(json['initiatedAt'] as String),
      completedAt: json['completedAt'] == null
          ? null
          : DateTime.parse(json['completedAt'] as String),
    );

Map<String, dynamic> _$SystemBackupResponseToJson(
        SystemBackupResponse instance) =>
    <String, dynamic>{
      'backupId': instance.backupId,
      'type': instance.type,
      'status': instance.status,
      'sizeInBytes': instance.sizeInBytes,
      'location': instance.location,
      'initiatedBy': instance.initiatedBy,
      'initiatedAt': instance.initiatedAt?.toIso8601String(),
      'completedAt': instance.completedAt?.toIso8601String(),
    };

UserStatusUpdateRequest _$UserStatusUpdateRequestFromJson(
        Map<String, dynamic> json) =>
    UserStatusUpdateRequest(
      status: json['status'] as String,
    );

Map<String, dynamic> _$UserStatusUpdateRequestToJson(
        UserStatusUpdateRequest instance) =>
    <String, dynamic>{
      'status': instance.status,
    };

UserRoleUpdateRequest _$UserRoleUpdateRequestFromJson(
        Map<String, dynamic> json) =>
    UserRoleUpdateRequest(
      roles: (json['roles'] as List<dynamic>).map((e) => e as String).toList(),
    );

Map<String, dynamic> _$UserRoleUpdateRequestToJson(
        UserRoleUpdateRequest instance) =>
    <String, dynamic>{
      'roles': instance.roles,
    };

CompanyVerificationRequest _$CompanyVerificationRequestFromJson(
        Map<String, dynamic> json) =>
    CompanyVerificationRequest(
      companyId: (json['companyId'] as num).toInt(),
      status: json['status'] as String,
      verificationNotes: json['verificationNotes'] as String?,
      rejectionReason: json['rejectionReason'] as String?,
    );

Map<String, dynamic> _$CompanyVerificationRequestToJson(
        CompanyVerificationRequest instance) =>
    <String, dynamic>{
      'companyId': instance.companyId,
      'status': instance.status,
      'verificationNotes': instance.verificationNotes,
      'rejectionReason': instance.rejectionReason,
    };

CompanySuspensionRequest _$CompanySuspensionRequestFromJson(
        Map<String, dynamic> json) =>
    CompanySuspensionRequest(
      reason: json['reason'] as String,
      notes: json['notes'] as String?,
      suspensionDurationDays: (json['suspensionDurationDays'] as num?)?.toInt(),
    );

Map<String, dynamic> _$CompanySuspensionRequestToJson(
        CompanySuspensionRequest instance) =>
    <String, dynamic>{
      'reason': instance.reason,
      'notes': instance.notes,
      'suspensionDurationDays': instance.suspensionDurationDays,
    };

CompanyUnsuspensionRequest _$CompanyUnsuspensionRequestFromJson(
        Map<String, dynamic> json) =>
    CompanyUnsuspensionRequest(
      reason: json['reason'] as String?,
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$CompanyUnsuspensionRequestToJson(
        CompanyUnsuspensionRequest instance) =>
    <String, dynamic>{
      'reason': instance.reason,
      'notes': instance.notes,
    };

LoadAssignmentRequest _$LoadAssignmentRequestFromJson(
        Map<String, dynamic> json) =>
    LoadAssignmentRequest(
      companyId: (json['companyId'] as num).toInt(),
    );

Map<String, dynamic> _$LoadAssignmentRequestToJson(
        LoadAssignmentRequest instance) =>
    <String, dynamic>{
      'companyId': instance.companyId,
    };

SystemConfigurationUpdateRequest _$SystemConfigurationUpdateRequestFromJson(
        Map<String, dynamic> json) =>
    SystemConfigurationUpdateRequest(
      settings: json['settings'] as Map<String, dynamic>,
    );

Map<String, dynamic> _$SystemConfigurationUpdateRequestToJson(
        SystemConfigurationUpdateRequest instance) =>
    <String, dynamic>{
      'settings': instance.settings,
    };
