package zw.co.kanjan.logipool.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "permits")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Permit {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank
    @Size(max = 50)
    private String permitNumber;
    
    @NotBlank
    @Size(max = 100)
    private String issuingAuthority;
    
    @Enumerated(EnumType.STRING)
    @Column(length = 30)
    @NotNull
    private PermitType permitType;
    
    @Enumerated(EnumType.STRING)
    @Column(length = 20)
    @Builder.Default
    private PermitStatus status = PermitStatus.ACTIVE;
    
    @NotNull
    private LocalDateTime issueDate;
    
    @NotNull
    private LocalDateTime expiryDate;
    
    private BigDecimal permitFee;
    
    @Size(max = 20)
    private String currency = "USD";
    
    @Column(columnDefinition = "TEXT")
    private String description;
    
    @Column(columnDefinition = "TEXT")
    private String conditions;
    
    @Column(columnDefinition = "TEXT")
    private String restrictions;
    
    // Route/area specific fields
    @Size(max = 200)
    private String authorizedRoutes;
    
    @Size(max = 200)
    private String authorizedAreas;
    
    private BigDecimal maxWeight;
    
    private BigDecimal maxLength;
    
    private BigDecimal maxWidth;
    
    private BigDecimal maxHeight;
    
    @Size(max = 20)
    private String dimensionUnit = "m";
    
    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "company_id")
    private Company company;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "vehicle_id")
    private Vehicle vehicle;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "truck_head_id")
    private TruckHead truckHead;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "trailer_id")
    private Trailer trailer;
    
    @OneToMany(mappedBy = "permit", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @Builder.Default
    private List<Document> documents = new ArrayList<>();
    
    @CreationTimestamp
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    private LocalDateTime updatedAt;
    
    public enum PermitType {
        ROAD_TRANSPORT,     // General road transport permit
        CROSS_BORDER,       // Cross border transport
        OVERSIZE_LOAD,      // Oversize/overweight loads
        DANGEROUS_GOODS,    // Hazardous materials
        LIVESTOCK,          // Livestock transport
        PASSENGER,          // Passenger transport
        MUNICIPAL,          // Municipal area permit
        SPECIAL_CARGO,      // Special cargo permit
        TEMPORARY,          // Temporary permit
        ANNUAL              // Annual permit
    }
    
    public enum PermitStatus {
        ACTIVE,     // Currently active
        EXPIRED,    // Has expired
        SUSPENDED,  // Temporarily suspended
        REVOKED,    // Permanently revoked
        PENDING,    // Application pending
        REJECTED    // Application rejected
    }
}
