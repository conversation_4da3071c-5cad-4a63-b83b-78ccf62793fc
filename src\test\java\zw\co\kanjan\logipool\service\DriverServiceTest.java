package zw.co.kanjan.logipool.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;
import zw.co.kanjan.logipool.dto.driver.DriverDto;
import zw.co.kanjan.logipool.dto.driver.DriverVerificationDto;
import zw.co.kanjan.logipool.entity.*;
import zw.co.kanjan.logipool.exception.BusinessException;
import zw.co.kanjan.logipool.exception.ResourceNotFoundException;
import zw.co.kanjan.logipool.exception.ValidationException;
import zw.co.kanjan.logipool.mapper.DriverMapper;
import zw.co.kanjan.logipool.repository.*;

import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DriverServiceTest {

    @Mock
    private UserRepository userRepository;

    @Mock
    private RoleRepository roleRepository;

    @Mock
    private CompanyRepository companyRepository;

    @Mock
    private CompanyMemberRepository companyMemberRepository;

    @Mock
    private DriverVerificationRepository driverVerificationRepository;

    @Mock
    private DriverMapper driverMapper;

    @Mock
    private PasswordEncoder passwordEncoder;

    @InjectMocks
    private DriverService driverService;

    private User adminUser;
    private User driverUser;
    private Company company;
    private Role driverRole;
    private DriverDto.DriverRegistrationRequest registrationRequest;

    @BeforeEach
    void setUp() {
        // Setup admin user
        adminUser = User.builder()
                .id(1L)
                .username("<EMAIL>")
                .email("<EMAIL>")
                .firstName("Admin")
                .lastName("User")
                .roles(Set.of(Role.builder().name(Role.RoleName.ADMIN).build()))
                .build();

        // Setup driver user
        driverUser = User.builder()
                .id(2L)
                .username("<EMAIL>")
                .email("<EMAIL>")
                .firstName("John")
                .lastName("Driver")
                .roles(Set.of(Role.builder().name(Role.RoleName.DRIVER).build()))
                .build();

        // Setup company
        company = Company.builder()
                .id(1L)
                .name("Test Transport")
                .user(adminUser)
                .build();

        // Setup driver role
        driverRole = Role.builder()
                .id(4L)
                .name(Role.RoleName.DRIVER)
                .description("Driver who operates vehicles for transportation services")
                .build();

        // Setup registration request
        registrationRequest = DriverDto.DriverRegistrationRequest.builder()
                .firstName("Jane")
                .lastName("Driver")
                .email("<EMAIL>")
                .password("password123")
                .phoneNumber("+263771234567")
                .licenseNumber("DL123456")
                .licenseClass("C")
                .nationalId("63-123456-A-12")
                .companyId(1L)
                .build();
    }

    @Test
    void testRegisterDriver_Success() {
        // Given
        when(userRepository.findByUsername("<EMAIL>")).thenReturn(Optional.of(adminUser));
        when(userRepository.existsByEmail("<EMAIL>")).thenReturn(false);
        when(roleRepository.findByName(Role.RoleName.DRIVER)).thenReturn(Optional.of(driverRole));
        when(passwordEncoder.encode("password123")).thenReturn("encoded_password");
        when(userRepository.save(any(User.class))).thenReturn(driverUser);
        when(companyRepository.findById(1L)).thenReturn(Optional.of(company));
        when(companyMemberRepository.save(any(CompanyMember.class))).thenReturn(new CompanyMember());
        when(driverVerificationRepository.findByDriverOrderByCreatedAtDesc(any(User.class)))
                .thenReturn(new ArrayList<>());
        when(driverMapper.toFullResponse(any(User.class), any(), any()))
                .thenReturn(DriverDto.DriverResponse.builder()
                        .id(2L)
                        .firstName("Jane")
                        .lastName("Driver")
                        .email("<EMAIL>")
                        .build());

        // When
        DriverDto.DriverResponse result = driverService.registerDriver(registrationRequest, "<EMAIL>");

        // Then
        assertNotNull(result);
        assertEquals("Jane", result.getFirstName());
        assertEquals("Driver", result.getLastName());
        assertEquals("<EMAIL>", result.getEmail());

        verify(userRepository).save(any(User.class));
        verify(driverVerificationRepository).saveAll(any(List.class));
        verify(companyMemberRepository).save(any(CompanyMember.class));
    }

    @Test
    void testRegisterDriver_EmailAlreadyExists() {
        // Given
        when(userRepository.findByUsername("<EMAIL>")).thenReturn(Optional.of(adminUser));
        when(userRepository.existsByEmail("<EMAIL>")).thenReturn(true);

        // When & Then
        ValidationException exception = assertThrows(ValidationException.class, () ->
                driverService.registerDriver(registrationRequest, "<EMAIL>"));

        assertEquals("Email already exists: <EMAIL>", exception.getMessage());
        verify(userRepository, never()).save(any(User.class));
    }

    @Test
    void testSelfRegisterDriver_Success() {
        // Given
        DriverDto.DriverRegistrationRequest selfRegRequest = DriverDto.DriverRegistrationRequest.builder()
                .firstName("Self")
                .lastName("Driver")
                .email("<EMAIL>")
                .password("password123")
                .phoneNumber("+263771234567")
                .build(); // No company ID for self-registration

        when(userRepository.existsByEmail("<EMAIL>")).thenReturn(false);
        when(roleRepository.findByName(Role.RoleName.DRIVER)).thenReturn(Optional.of(driverRole));
        when(passwordEncoder.encode("password123")).thenReturn("encoded_password");
        when(userRepository.save(any(User.class))).thenReturn(driverUser);
        when(driverVerificationRepository.findByDriverOrderByCreatedAtDesc(any(User.class)))
                .thenReturn(new ArrayList<>());
        when(driverMapper.toFullResponse(any(User.class), any(), any()))
                .thenReturn(DriverDto.DriverResponse.builder()
                        .id(2L)
                        .firstName("Self")
                        .lastName("Driver")
                        .email("<EMAIL>")
                        .build());

        // When
        DriverDto.DriverResponse result = driverService.selfRegisterDriver(selfRegRequest);

        // Then
        assertNotNull(result);
        assertEquals("Self", result.getFirstName());
        assertEquals("Driver", result.getLastName());

        verify(userRepository).save(any(User.class));
        verify(driverVerificationRepository).saveAll(any(List.class));
        verify(companyMemberRepository, never()).save(any(CompanyMember.class)); // No company assignment
    }

    @Test
    void testSelfRegisterDriver_WithCompanyId_ShouldFail() {
        // Given
        when(userRepository.existsByEmail("<EMAIL>")).thenReturn(false);

        // When & Then
        ValidationException exception = assertThrows(ValidationException.class, () ->
                driverService.selfRegisterDriver(registrationRequest)); // Has companyId

        assertEquals("Cannot assign to company during self-registration", exception.getMessage());
    }

    @Test
    void testGetAllDrivers_AsAdmin() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        List<User> drivers = Arrays.asList(driverUser);
        Page<User> driverPage = new PageImpl<>(drivers, pageable, 1);

        when(userRepository.findByUsername("<EMAIL>")).thenReturn(Optional.of(adminUser));
        when(userRepository.findByRoles_Name(Role.RoleName.DRIVER, pageable)).thenReturn(driverPage);
        when(driverVerificationRepository.findByDriverOrderByCreatedAtDesc(any(User.class)))
                .thenReturn(new ArrayList<>());
        when(driverMapper.toFullResponse(any(User.class), any(), any()))
                .thenReturn(DriverDto.DriverResponse.builder()
                        .id(2L)
                        .firstName("John")
                        .lastName("Driver")
                        .build());

        // When
        Page<DriverDto.DriverResponse> result = driverService.getAllDrivers(pageable, "<EMAIL>");

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals("John", result.getContent().get(0).getFirstName());

        verify(userRepository).findByRoles_Name(Role.RoleName.DRIVER, pageable);
    }

    @Test
    void testGetDriverById_Success() {
        // Given
        when(userRepository.findByUsername("<EMAIL>")).thenReturn(Optional.of(adminUser));
        when(userRepository.findById(2L)).thenReturn(Optional.of(driverUser));
        when(driverVerificationRepository.findByDriverOrderByCreatedAtDesc(driverUser))
                .thenReturn(new ArrayList<>());
        when(driverMapper.toFullResponse(any(User.class), any(), any()))
                .thenReturn(DriverDto.DriverResponse.builder()
                        .id(2L)
                        .firstName("John")
                        .lastName("Driver")
                        .build());

        // When
        DriverDto.DriverResponse result = driverService.getDriverById(2L, "<EMAIL>");

        // Then
        assertNotNull(result);
        assertEquals(2L, result.getId());
        assertEquals("John", result.getFirstName());
    }

    @Test
    void testGetDriverById_NotFound() {
        // Given
        when(userRepository.findByUsername("<EMAIL>")).thenReturn(Optional.of(adminUser));
        when(userRepository.findById(999L)).thenReturn(Optional.empty());

        // When & Then
        ResourceNotFoundException exception = assertThrows(ResourceNotFoundException.class, () ->
                driverService.getDriverById(999L, "<EMAIL>"));

        assertEquals("Driver not found with id: 999", exception.getMessage());
    }

    @Test
    void testAssignDriverToCompany_Success() {
        // Given
        DriverDto.AssignToCompanyRequest request = DriverDto.AssignToCompanyRequest.builder()
                .driverId(2L)
                .companyId(1L)
                .role(CompanyMember.CompanyRole.DRIVER)
                .canUpdateLoadStatus(true)
                .canTrackLocation(true)
                .build();

        when(userRepository.findByUsername("<EMAIL>")).thenReturn(Optional.of(adminUser));
        when(userRepository.findById(2L)).thenReturn(Optional.of(driverUser));
        when(companyRepository.findById(1L)).thenReturn(Optional.of(company));
        when(companyMemberRepository.findByUserAndCompanyAndStatus(
                driverUser, company, CompanyMember.MemberStatus.ACTIVE))
                .thenReturn(Optional.empty());
        when(companyMemberRepository.save(any(CompanyMember.class)))
                .thenReturn(new CompanyMember());
        when(driverVerificationRepository.findByDriverOrderByCreatedAtDesc(driverUser))
                .thenReturn(new ArrayList<>());
        when(driverMapper.toFullResponse(any(User.class), any(), any()))
                .thenReturn(DriverDto.DriverResponse.builder()
                        .id(2L)
                        .firstName("John")
                        .lastName("Driver")
                        .build());

        // When
        DriverDto.DriverResponse result = driverService.assignDriverToCompany(request, "<EMAIL>");

        // Then
        assertNotNull(result);
        verify(companyMemberRepository).save(any(CompanyMember.class));
    }

    @Test
    void testAssignDriverToCompany_AlreadyAssigned() {
        // Given
        DriverDto.AssignToCompanyRequest request = DriverDto.AssignToCompanyRequest.builder()
                .driverId(2L)
                .companyId(1L)
                .build();

        CompanyMember existingMember = CompanyMember.builder()
                .user(driverUser)
                .company(company)
                .status(CompanyMember.MemberStatus.ACTIVE)
                .build();

        when(userRepository.findByUsername("<EMAIL>")).thenReturn(Optional.of(adminUser));
        when(userRepository.findById(2L)).thenReturn(Optional.of(driverUser));
        when(companyRepository.findById(1L)).thenReturn(Optional.of(company));
        when(companyMemberRepository.findByUserAndCompanyAndStatus(
                driverUser, company, CompanyMember.MemberStatus.ACTIVE))
                .thenReturn(Optional.of(existingMember));

        // When & Then
        ValidationException exception = assertThrows(ValidationException.class, () ->
                driverService.assignDriverToCompany(request, "<EMAIL>"));

        assertEquals("Driver is already assigned to this company", exception.getMessage());
        verify(companyMemberRepository, never()).save(any(CompanyMember.class));
    }
}
