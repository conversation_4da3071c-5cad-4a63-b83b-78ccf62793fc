import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../core/constants/app_constants.dart';
import 'contact_integration_widget.dart';

class PublicLayout extends StatefulWidget {
  final Widget child;

  const PublicLayout({
    super.key,
    required this.child,
  });

  @override
  State<PublicLayout> createState() => _PublicLayoutState();
}

class _PublicLayoutState extends State<PublicLayout> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    return SelectionArea(
      child: Scaffold(
        key: _scaffoldKey,
        appBar: _buildAppBar(context),
        drawer: _buildDrawer(context),
        body: widget.child,
        floatingActionButton: _buildContactFAB(context),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    final isDesktop = MediaQuery.of(context).size.width > 768;

    return AppBar(
      title: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Image.asset(
              'assets/images/logo.png',
              height: 28,
              errorBuilder: (context, error, stackTrace) => const Icon(
                Icons.local_shipping,
                size: 28,
                color: Colors.white,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'LogiPool',
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  height: 1.0,
                ),
              ),
              if (isDesktop)
                const Text(
                  'Logistics Marketplace',
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.white70,
                    height: 1.0,
                  ),
                ),
            ],
          ),
        ],
      ),
      backgroundColor: Theme.of(context).primaryColor,
      foregroundColor: Colors.white,
      elevation: 2,
      actions: [
        // Desktop navigation items
        if (isDesktop) ...[
          _buildNavItem(context, 'Home', '/'),
          _buildNavItem(context, 'Vehicles', '/vehicles'),
          _buildNavItem(context, 'Equipment', '/equipment'),
          _buildNavItem(context, 'Loads', '/loads'),
          _buildNavItem(context, 'Services', '/services'),
          _buildNavItem(context, 'About', '/about'),
          _buildNavItem(context, 'Contact', '/contact'),
          _buildNavItem(context, 'Track Order', '/track'),
          const SizedBox(width: 8),
          // Search button for desktop
          IconButton(
            onPressed: () => _showSearchDialog(context),
            icon: const Icon(Icons.search),
            tooltip: 'Search',
          ),
          const SizedBox(width: 8),
          // Quick contact button for desktop
          IconButton(
            onPressed: () => _showQuickContactDialog(context),
            icon: const Icon(Icons.support_agent),
            tooltip: 'Contact Us',
          ),
          const SizedBox(width: 8),
        ],
        // Mobile search button
        if (!isDesktop) ...[
          IconButton(
            onPressed: () => _showSearchDialog(context),
            icon: const Icon(Icons.search),
            tooltip: 'Search',
          ),
        ],
        // Sign In button
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0),
          child: ElevatedButton(
            onPressed: () => context.go('/login'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              foregroundColor: Theme.of(context).primaryColor,
              padding: EdgeInsets.symmetric(
                horizontal: isDesktop ? 20 : 12,
                vertical: 8,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(Icons.login, size: 16),
                const SizedBox(width: 4),
                Text(
                  isDesktop ? 'Sign In' : 'Login',
                  style: const TextStyle(fontWeight: FontWeight.w600),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(width: 8),
      ],
    );
  }

  Widget _buildNavItem(BuildContext context, String title, String route) {
    final isActive = GoRouterState.of(context).matchedLocation == route;

    return TextButton(
      onPressed: () => context.go(route),
      style: TextButton.styleFrom(
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      ),
      child: Text(
        title,
        style: TextStyle(
          fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
          decoration: isActive ? TextDecoration.underline : null,
        ),
      ),
    );
  }

  void _showSearchDialog(BuildContext context) {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search LogiPool'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              decoration: const InputDecoration(
                hintText: 'Search vehicles, equipment, or services...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onSubmitted: (value) {
                Navigator.pop(context);
                if (value.trim().isNotEmpty) {
                  // Navigate to search results
                  context.go(
                      '/vehicles?search=${Uri.encodeComponent(value.trim())}');
                }
              },
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                      context.go('/vehicles');
                    },
                    icon: const Icon(Icons.local_shipping),
                    label: const Text('Vehicles'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                      context.go('/equipment');
                    },
                    icon: const Icon(Icons.construction),
                    label: const Text('Equipment'),
                  ),
                ),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  Widget _buildDrawer(BuildContext context) {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
            ),
            child: const Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  Icons.local_shipping,
                  size: 48,
                  color: Colors.white,
                ),
                SizedBox(height: 8),
                Text(
                  'LogiPool',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Logistics Marketplace',
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          _buildDrawerItem(context, Icons.home, 'Home', '/'),
          _buildDrawerItem(
              context, Icons.local_shipping, 'Vehicles', '/vehicles'),
          _buildDrawerItem(
              context, Icons.construction, 'Equipment', '/equipment'),
          _buildDrawerItem(
              context, Icons.inventory_2, 'Loads', '/loads'),
          _buildDrawerItem(
              context, Icons.business_center, 'Services', '/services'),
          _buildDrawerItem(context, Icons.info, 'About Us', '/about'),
          _buildDrawerItem(context, Icons.contact_mail, 'Contact', '/contact'),
          _buildDrawerItem(
              context, Icons.track_changes, 'Track Order', '/track'),
          const Divider(),
          _buildDrawerItem(context, Icons.help, 'FAQ', '/faq'),
          _buildDrawerItem(
              context, Icons.description, 'Terms & Conditions', '/terms'),
          _buildDrawerItem(
              context, Icons.privacy_tip, 'Privacy Policy', '/privacy'),
          const Divider(),
          ListTile(
            leading: const Icon(Icons.login),
            title: const Text('Sign In'),
            onTap: () {
              Navigator.pop(context);
              context.go('/login');
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDrawerItem(
      BuildContext context, IconData icon, String title, String route) {
    final isActive = GoRouterState.of(context).matchedLocation == route;

    return ListTile(
      leading: Icon(
        icon,
        color: isActive ? Theme.of(context).primaryColor : null,
      ),
      title: Text(
        title,
        style: TextStyle(
          fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
          color: isActive ? Theme.of(context).primaryColor : null,
        ),
      ),
      onTap: () {
        Navigator.pop(context);
        context.go(route);
      },
    );
  }

  Widget _buildWhatsAppButton(BuildContext context) {
    return FloatingActionButton(
      onPressed: () => _launchWhatsApp(),
      backgroundColor: const Color(0xFF25D366), // WhatsApp green
      child: const Icon(
        Icons.chat,
        color: Colors.white,
      ),
    );
  }



  Future<void> _launchWhatsApp() async {
    final phoneNumber = AppConstants.whatsAppNumberForDialing;
    final currentRoute = GoRouterState.of(context).matchedLocation;

    String message =
        'Hello! I\'m interested in LogiPool services. Can you help me?';

    // Customize message based on current page
    switch (currentRoute) {
      case '/vehicles':
        message =
            'Hi! I\'m looking for vehicle rental services. Can you provide more information?';
        break;
      case '/equipment':
        message = 'Hello! I need equipment rental services. Can you assist me?';
        break;
      case '/services':
        message = 'Hi! I\'d like to know more about your logistics services.';
        break;
      case '/track':
        message = 'Hello! I need help with tracking my shipment.';
        break;
      case '/contact':
        message = 'Hi! I have some questions about LogiPool services.';
        break;
      default:
        message =
            'Hello! I\'m interested in LogiPool services. Can you help me?';
    }

    final url =
        'https://wa.me/$phoneNumber?text=${Uri.encodeComponent(message)}';

    try {
      if (await canLaunchUrl(Uri.parse(url))) {
        await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
      } else {
        // Fallback: show contact information
        if (context.mounted) {
          _showContactDialog();
        }
      }
    } catch (e) {
      if (context.mounted) {
        _showContactDialog();
      }
    }
  }

  void _showContactDialog() {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Contact Us'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('WhatsApp: ${AppConstants.formattedWhatsAppNumber}'),
            const SizedBox(height: 8),
            Text('Phone: ${AppConstants.formattedPhoneNumber}'),
            const SizedBox(height: 8),
            Text('Email: ${AppConstants.companyEmail}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildContactFAB(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Post Load FAB
        FloatingActionButton.extended(
          onPressed: () => context.go('/guest-load-posting'),
          backgroundColor: Colors.orange,
          foregroundColor: Colors.white,
          icon: const Icon(Icons.add_box),
          label: const Text('Post Load'),
          heroTag: 'post_load',
        ),
        // const SizedBox(height: 12),
        // // WhatsApp FAB
        // FloatingActionButton(
        //   onPressed: () => _launchWhatsApp(),
        //   backgroundColor: Colors.green,
        //   foregroundColor: Colors.white,
        //   child: const Icon(Icons.chat),
        //   heroTag: 'whatsapp',
        //   mini: true,
        // ),
      ],
    );
  }

  void _showQuickContactDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: ConstrainedBox(
          constraints: const BoxConstraints(
            maxWidth: 400, // Set maximum width to prevent it from being too wide
            minWidth: 300, // Set minimum width for mobile devices
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.support_agent,
                      color: Theme.of(context).primaryColor,
                      size: 28,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Contact LogiPool',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                const ContactIntegrationWidget(
                  showLabels: true,
                  isHorizontal: false,
                  padding: EdgeInsets.zero,
                ),
                const SizedBox(height: 16),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    children: [
                      Text(
                        'Business Hours',
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                      ),
                      const SizedBox(height: 4),
                      const Text(
                        'Mon - Fri: 8:00 AM - 6:00 PM\nSat: 9:00 AM - 2:00 PM',
                        textAlign: TextAlign.center,
                        style: TextStyle(fontSize: 12),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _launchPhone() async {
    final phoneNumber = 'tel:${AppConstants.phoneNumberForDialing}';
    try {
      if (await canLaunchUrl(Uri.parse(phoneNumber))) {
        await launchUrl(Uri.parse(phoneNumber));
      }
    } catch (e) {
      // Handle error silently
    }
  }

  Future<void> _launchEmail() async {
    final email = 'mailto:${AppConstants.companyEmail}?subject=LogiPool Inquiry';
    try {
      if (await canLaunchUrl(Uri.parse(email))) {
        await launchUrl(Uri.parse(email));
      }
    } catch (e) {
      // Handle error silently
    }
  }
}
