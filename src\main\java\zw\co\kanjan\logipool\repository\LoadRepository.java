package zw.co.kanjan.logipool.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import zw.co.kanjan.logipool.entity.Load;
import zw.co.kanjan.logipool.entity.User;
import zw.co.kanjan.logipool.entity.Company;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface LoadRepository extends JpaRepository<Load, Long> {
    
    Page<Load> findByStatus(Load.LoadStatus status, Pageable pageable);
    
    Page<Load> findByClient(User client, Pageable pageable);
    
    Page<Load> findByLoadType(Load.LoadType loadType, Pageable pageable);
    
    @Query("SELECT l FROM Load l WHERE l.status = :status AND l.isVerified = :verified")
    Page<Load> findByStatusAndVerified(@Param("status") Load.LoadStatus status, 
                                      @Param("verified") Boolean verified, 
                                      Pageable pageable);
    
    @Query("SELECT l FROM Load l WHERE l.pickupLocation LIKE %:location% OR l.deliveryLocation LIKE %:location%")
    Page<Load> findByLocation(@Param("location") String location, Pageable pageable);
    
    @Query("SELECT l FROM Load l WHERE l.pickupDate BETWEEN :startDate AND :endDate")
    Page<Load> findByPickupDateBetween(@Param("startDate") LocalDateTime startDate, 
                                      @Param("endDate") LocalDateTime endDate, 
                                      Pageable pageable);
    
    @Query("SELECT l FROM Load l WHERE l.biddingClosesAt < :now AND l.status = 'POSTED'")
    List<Load> findExpiredBidding(@Param("now") LocalDateTime now);
    
    @Query("SELECT l FROM Load l WHERE l.status IN :statuses")
    Page<Load> findByStatusIn(@Param("statuses") List<Load.LoadStatus> statuses, Pageable pageable);

    @Query("SELECT COUNT(l) FROM Load l WHERE l.status = :status")
    Long countByStatus(@Param("status") Load.LoadStatus status);

    @Query("SELECT COUNT(l) FROM Load l WHERE l.assignedCompany = :company")
    Integer countByAssignedCompany(@Param("company") Company company);

    @Query("SELECT l FROM Load l ORDER BY l.createdAt DESC")
    List<Load> findTop5ByOrderByCreatedAtDesc();

    // Find load by tracking number for public access
    Optional<Load> findByTrackingNumber(String trackingNumber);

    // Find loads visible to a specific user (posted loads + user's own loads + company assigned loads)
    @Query("SELECT DISTINCT l FROM Load l " +
           "LEFT JOIN CompanyMember cm ON cm.company = l.assignedCompany " +
           "WHERE l.status = 'POSTED' OR " +
           "l.client.id = :userId OR " +
           "(cm.user.id = :userId AND cm.status = 'ACTIVE') " +
           "ORDER BY l.createdAt DESC")
    Page<Load> findLoadsVisibleToUser(@Param("userId") Long userId, Pageable pageable);

    // Find loads by status visible to a specific user
    @Query("SELECT DISTINCT l FROM Load l " +
           "LEFT JOIN CompanyMember cm ON cm.company = l.assignedCompany " +
           "WHERE l.status = :status AND (" +
           "l.status = 'POSTED' OR " +
           "l.client.id = :userId OR " +
           "(cm.user.id = :userId AND cm.status = 'ACTIVE')) " +
           "ORDER BY l.createdAt DESC")
    Page<Load> findLoadsByStatusVisibleToUser(@Param("status") Load.LoadStatus status,
                                             @Param("userId") Long userId,
                                             Pageable pageable);

    // Find publicly visible loads (posted status only, for guest browsing)
    @Query("SELECT l FROM Load l WHERE l.status = 'POSTED' AND l.biddingClosesAt > :now ORDER BY l.createdAt DESC")
    Page<Load> findPubliclyVisibleLoads(@Param("now") LocalDateTime now, Pageable pageable);

    // Find publicly visible loads by cargo type
    @Query("SELECT l FROM Load l WHERE l.status = 'POSTED' AND l.biddingClosesAt > :now AND " +
           "LOWER(l.cargoType) LIKE LOWER(CONCAT('%', :cargoType, '%')) ORDER BY l.createdAt DESC")
    Page<Load> findPubliclyVisibleLoadsByCargoType(@Param("cargoType") String cargoType,
                                                   @Param("now") LocalDateTime now,
                                                   Pageable pageable);

    // Find publicly visible loads by load type
    @Query("SELECT l FROM Load l WHERE l.status = 'POSTED' AND l.biddingClosesAt > :now AND " +
           "l.loadType = :loadType ORDER BY l.createdAt DESC")
    Page<Load> findPubliclyVisibleLoadsByLoadType(@Param("loadType") Load.LoadType loadType,
                                                  @Param("now") LocalDateTime now,
                                                  Pageable pageable);

    // Search publicly visible loads
    @Query("SELECT l FROM Load l WHERE l.status = 'POSTED' AND l.biddingClosesAt > :now AND " +
           "(LOWER(l.title) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
           "LOWER(l.cargoType) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
           "LOWER(l.pickupLocation) LIKE LOWER(CONCAT('%', :search, '%')) OR " +
           "LOWER(l.deliveryLocation) LIKE LOWER(CONCAT('%', :search, '%'))) " +
           "ORDER BY l.createdAt DESC")
    Page<Load> searchPubliclyVisibleLoads(@Param("search") String search,
                                         @Param("now") LocalDateTime now,
                                         Pageable pageable);

    // Count publicly visible loads
    @Query("SELECT COUNT(l) FROM Load l WHERE l.status = 'POSTED' AND l.biddingClosesAt > :now")
    Long countPubliclyVisibleLoads(@Param("now") LocalDateTime now);

    // Find loads with pickup dates in the next N days for daily reports
    @Query("SELECT l FROM Load l WHERE l.pickupDate BETWEEN :startDate AND :endDate " +
           "AND l.status IN ('POSTED', 'ASSIGNED', 'IN_TRANSIT') " +
           "ORDER BY l.pickupDate ASC")
    List<Load> findLoadsStartingInDateRange(@Param("startDate") LocalDateTime startDate,
                                           @Param("endDate") LocalDateTime endDate);

    // Find loads starting in the next 3 days for admin daily reports
    @Query("SELECT l FROM Load l WHERE l.pickupDate BETWEEN :now AND :threeDaysFromNow " +
           "AND l.status IN ('POSTED', 'ASSIGNED', 'IN_TRANSIT') " +
           "ORDER BY l.pickupDate ASC")
    List<Load> findLoadsStartingInNext3Days(@Param("now") LocalDateTime now,
                                           @Param("threeDaysFromNow") LocalDateTime threeDaysFromNow);
}
