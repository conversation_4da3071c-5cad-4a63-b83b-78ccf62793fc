import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../services/auth_service.dart';
import '../../core/constants/app_constants.dart';
import '../../features/auth/bloc/auth_bloc.dart';

/// A unified header component that shows the current page title in the main header
/// with global actions (notifications, profile)
class UnifiedHeader extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final VoidCallback? onNotificationPressed;
  final VoidCallback? onProfilePressed;
  final VoidCallback? onLogoutPressed;
  final bool showBackButton;
  final VoidCallback? onBackPressed;

  const UnifiedHeader({
    Key? key,
    required this.title,
    this.actions,
    this.onNotificationPressed,
    this.onProfilePressed,
    this.onLogoutPressed,
    this.showBackButton = false,
    this.onBackPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      height: kToolbarHeight,
      decoration: BoxDecoration(
        color: theme.colorScheme.primary,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            offset: const Offset(0, 1),
            blurRadius: 2,
          ),
        ],
      ),
      child: Row(
        children: [
          // Back button if needed
          if (showBackButton)
            IconButton(
              icon: Icon(
                Icons.arrow_back,
                color: theme.colorScheme.onPrimary,
              ),
              onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
              tooltip: 'Back',
            )
          else
            const SizedBox(width: 16),
          // Current page title
          Expanded(
            child: Text(
              title,
              style: theme.textTheme.titleLarge?.copyWith(
                color: theme.colorScheme.onPrimary,
                fontWeight: FontWeight.bold,
                fontSize: 20,
              ),
            ),
          ),
          // Page-specific actions
          if (actions != null) ...actions!,
          // Global actions
          IconButton(
            icon: Icon(
              Icons.notifications_outlined,
              color: theme.colorScheme.onPrimary,
            ),
            onPressed: onNotificationPressed,
            tooltip: 'Notifications',
          ),
          Consumer<AuthService>(
            builder: (context, authService, child) {
              return PopupMenuButton<String>(
                icon: Icon(
                  Icons.account_circle_outlined,
                  color: theme.colorScheme.onPrimary,
                ),
                tooltip: 'Account Menu',
                color: theme.colorScheme.surface,
                elevation: 8,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                position: PopupMenuPosition.under,
                onSelected: (value) {
                  switch (value) {
                    case 'companies':
                      context.go('/companies');
                      break;
                    case 'profile':
                      onProfilePressed?.call();
                      break;
                    case 'logout':
                      if (onLogoutPressed != null) {
                        onLogoutPressed!();
                      } else {
                        _showDefaultLogoutDialog(context);
                      }
                      break;
                  }
                },
                itemBuilder: (context) {
                  final items = <PopupMenuEntry<String>>[];
                  final menuTheme = Theme.of(context);

                  // Add Companies menu item for transporters
                  if (authService.userRole == AppConstants.roleTransporter) {
                    items.add(
                      PopupMenuItem(
                        value: 'companies',
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        child: Row(
                          children: [
                            Icon(
                              Icons.business,
                              color: menuTheme.colorScheme.onSurface,
                              size: 20,
                            ),
                            const SizedBox(width: 12),
                            Text(
                              'Company Profile',
                              style: menuTheme.textTheme.bodyMedium?.copyWith(
                                color: menuTheme.colorScheme.onSurface,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  }

                  // Add Profile menu item
                  items.add(
                    PopupMenuItem(
                      value: 'profile',
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      child: Row(
                        children: [
                          Icon(
                            Icons.person,
                            color: menuTheme.colorScheme.onSurface,
                            size: 20,
                          ),
                          const SizedBox(width: 12),
                          Text(
                            'Profile',
                            style: menuTheme.textTheme.bodyMedium?.copyWith(
                              color: menuTheme.colorScheme.onSurface,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );

                  // Add Logout menu item
                  items.add(
                    PopupMenuItem(
                      value: 'logout',
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      child: Row(
                        children: [
                          Icon(
                            Icons.logout,
                            color: menuTheme.colorScheme.error,
                            size: 20,
                          ),
                          const SizedBox(width: 12),
                          Text(
                            'Logout',
                            style: menuTheme.textTheme.bodyMedium?.copyWith(
                              color: menuTheme.colorScheme.error,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );

                  return items;
                },
              );
            },
          ),
          const SizedBox(width: 8),
        ],
      ),
    );
  }

  @override
  Size get preferredSize {
    return const Size.fromHeight(kToolbarHeight);
  }

  void _showDefaultLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<AuthBloc>().add(AuthLogoutRequested());
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }
}

/// A simplified page header for use in page body when you want
/// the content to be scrollable with the page
class PageHeader extends StatelessWidget {
  final String? title;
  final List<Widget>? actions;
  final Widget? bottom;
  final double? bottomHeight;
  final EdgeInsetsGeometry? padding;

  const PageHeader({
    Key? key,
    this.title,
    this.actions,
    this.bottom,
    this.bottomHeight,
    this.padding,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: padding ?? const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: theme.dividerColor,
            width: 0.5,
          ),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title and actions row
          if (title != null || actions != null)
            Row(
              children: [
                if (title != null)
                  Expanded(
                    child: Text(
                      title!,
                      style: theme.textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                  ),
                if (actions != null) ...actions!,
              ],
            ),
          // Bottom content
          if (bottom != null) ...[
            if (title != null || actions != null) const SizedBox(height: 16),
            SizedBox(
              height: bottomHeight,
              child: bottom!,
            ),
          ],
        ],
      ),
    );
  }
}
