# Fleet Management System

## Overview

The Fleet Management System is a comprehensive solution for transportation companies to manage their vehicle fleets, track compliance, optimize utilization, and ensure operational efficiency. This system provides end-to-end fleet management capabilities integrated with the LogiPool platform.

## Key Features

### 1. Vehicle Management
- **Truck Head Management**: Register, track, and manage truck heads with detailed specifications
- **Trailer Management**: Manage different types of trailers (curtain side, flatbed, refrigerated, etc.)
- **Vehicle Status Tracking**: Real-time status updates (Available, Assigned, In Transit, Maintenance)
- **Maintenance Scheduling**: Track maintenance schedules and service history
- **Document Management**: Store and track vehicle-related documents

### 2. Fleet Assignment
- **Load Assignment**: Assign specific vehicles and drivers to loads
- **Resource Availability**: Check availability of vehicles and drivers for specific time periods
- **Conflict Detection**: Prevent double-booking of resources
- **Assignment Tracking**: Monitor assignment status from planning to completion
- **Driver Assignment**: Assign primary and secondary drivers to loads

### 3. Compliance Management
- **Document Expiry Tracking**: Monitor expiry dates for insurance, permits, fitness certificates
- **Automated Alerts**: Receive notifications for expiring documents
- **Compliance Dashboard**: Visual overview of compliance status across the fleet
- **Regulatory Compliance**: Ensure adherence to local transportation regulations

### 4. Fleet Analytics & Reporting
- **Utilization Reports**: Track vehicle and driver utilization rates
- **Performance Metrics**: Monitor fleet performance indicators
- **Cost Analysis**: Track operational costs and profitability
- **Export Capabilities**: Export data to Excel for further analysis

### 5. Real-time Monitoring
- **Live Tracking**: Real-time location tracking of vehicles
- **Status Updates**: Instant updates on vehicle and assignment status
- **Notifications**: Automated alerts for critical events
- **Dashboard Views**: Comprehensive fleet overview with key metrics

## System Architecture

### Backend Components

#### Entities
- **TruckHead**: Represents truck heads with specifications and status
- **Trailer**: Represents trailers with type and capacity information
- **FleetAssignment**: Links vehicles to specific loads and drivers
- **Insurance**: Manages insurance policies for vehicles
- **TaxClearance**: Tracks tax clearance certificates
- **Permit**: Manages various permits and licenses

#### Services
- **TruckHeadService**: CRUD operations for truck heads
- **FleetManagementService**: Fleet assignment and availability management
- **FleetDashboardService**: Analytics and reporting
- **FleetNotificationService**: Automated alerts and reminders
- **FleetExportService**: Data export and reporting

#### Controllers
- **TruckHeadController**: REST API for truck head management
- **FleetManagementController**: Fleet assignment APIs
- **FleetDashboardController**: Dashboard and analytics APIs
- **FleetExportController**: Data export endpoints

### Frontend Components

#### Flutter Screens
- **FleetDashboardScreen**: Main fleet overview with tabs
- **TruckHeadsScreen**: Truck head management interface
- **TrailersScreen**: Trailer management interface
- **FleetAssignmentsScreen**: Assignment management
- **ComplianceScreen**: Compliance monitoring

#### Widgets
- **FleetOverviewCard**: Summary statistics display
- **ComplianceAlertsCard**: Compliance alerts visualization
- **FleetAssignmentCard**: Assignment details display
- **FleetUtilizationChart**: Utilization metrics visualization

## Database Schema

### Core Tables

```sql
-- Truck Heads
CREATE TABLE truck_heads (
    id BIGINT PRIMARY KEY,
    registration_number VARCHAR(20) UNIQUE NOT NULL,
    make VARCHAR(50) NOT NULL,
    model VARCHAR(50) NOT NULL,
    year INTEGER,
    status VARCHAR(20) NOT NULL,
    company_id BIGINT NOT NULL,
    current_driver_id BIGINT,
    -- Additional fields...
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

-- Trailers
CREATE TABLE trailers (
    id BIGINT PRIMARY KEY,
    registration_number VARCHAR(20) UNIQUE NOT NULL,
    type VARCHAR(30) NOT NULL,
    max_weight DECIMAL(10,2),
    max_volume DECIMAL(10,2),
    company_id BIGINT NOT NULL,
    -- Additional fields...
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

-- Fleet Assignments
CREATE TABLE fleet_assignments (
    id BIGINT PRIMARY KEY,
    load_id BIGINT NOT NULL,
    bid_id BIGINT,
    truck_head_id BIGINT,
    trailer1_id BIGINT,
    trailer2_id BIGINT,
    primary_driver_id BIGINT,
    secondary_driver_id BIGINT,
    company_id BIGINT NOT NULL,
    status VARCHAR(20) NOT NULL,
    scheduled_start_time TIMESTAMP NOT NULL,
    scheduled_end_time TIMESTAMP NOT NULL,
    actual_start_time TIMESTAMP,
    actual_end_time TIMESTAMP,
    assigned_by_id BIGINT,
    assigned_at TIMESTAMP,
    notes TEXT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

## API Endpoints

### Truck Head Management
- `POST /api/fleet/truck-heads` - Create truck head
- `GET /api/fleet/truck-heads` - List truck heads
- `GET /api/fleet/truck-heads/{id}` - Get truck head details
- `PUT /api/fleet/truck-heads/{id}` - Update truck head
- `DELETE /api/fleet/truck-heads/{id}` - Delete truck head
- `PATCH /api/fleet/truck-heads/{id}/status` - Update status

### Fleet Assignment
- `POST /api/fleet/assignments` - Create assignment
- `GET /api/fleet/assignments` - List assignments
- `GET /api/fleet/assignments/{id}` - Get assignment details
- `PUT /api/fleet/assignments/{id}` - Update assignment
- `DELETE /api/fleet/assignments/{id}` - Delete assignment
- `POST /api/fleet/assignments/check-availability/{type}/{id}` - Check availability

### Dashboard & Analytics
- `GET /api/fleet/dashboard/overview` - Fleet overview
- `GET /api/fleet/dashboard/compliance-alerts` - Compliance alerts
- `GET /api/fleet/dashboard/utilization` - Utilization metrics

### Data Export
- `GET /api/fleet/export/fleet-overview` - Export fleet data
- `GET /api/fleet/export/compliance-report` - Export compliance report
- `GET /api/fleet/export/utilization-report` - Export utilization report

## Configuration

### Application Properties
```properties
# Fleet Management Configuration
fleet.notifications.enabled=true
fleet.notifications.email.enabled=true
fleet.notifications.sms.enabled=true
fleet.compliance.check.schedule=0 0 8 * * *
fleet.maintenance.reminder.schedule=0 0 9 * * MON
fleet.export.max-records=10000
```

### Security Configuration
```java
@PreAuthorize("hasRole('TRANSPORTER') or hasRole('ADMIN')")
public class FleetController {
    // Fleet management endpoints
}
```

## Deployment

### Docker Configuration
```dockerfile
# Fleet service configuration
ENV FLEET_NOTIFICATIONS_ENABLED=true
ENV FLEET_COMPLIANCE_CHECK_SCHEDULE="0 0 8 * * *"
ENV FLEET_EXPORT_MAX_RECORDS=10000
```

### Kubernetes Resources
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: logipool-fleet-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: logipool-fleet
  template:
    metadata:
      labels:
        app: logipool-fleet
    spec:
      containers:
      - name: fleet-service
        image: logipool/fleet-service:latest
        env:
        - name: FLEET_NOTIFICATIONS_ENABLED
          value: "true"
```

## Testing

### Unit Tests
- Service layer tests with mocked dependencies
- Repository tests with test database
- Controller tests with MockMvc

### Integration Tests
- End-to-end API testing
- Database integration tests
- Security integration tests

### Test Coverage
- Minimum 80% code coverage required
- Critical paths must have 95% coverage
- All public APIs must be tested

## Monitoring & Observability

### Metrics
- Fleet utilization rates
- Assignment success rates
- Compliance alert counts
- API response times

### Logging
- Structured logging with correlation IDs
- Audit logs for all fleet operations
- Error tracking and alerting

### Health Checks
- Database connectivity
- External service dependencies
- Background job status

## Security Considerations

### Access Control
- Role-based access control (RBAC)
- Company-level data isolation
- API rate limiting

### Data Protection
- Encryption at rest and in transit
- PII data handling compliance
- Audit trail for sensitive operations

### API Security
- JWT token authentication
- Request validation and sanitization
- CORS configuration

## Performance Optimization

### Database Optimization
- Proper indexing on frequently queried columns
- Query optimization for large datasets
- Connection pooling configuration

### Caching Strategy
- Redis caching for frequently accessed data
- Cache invalidation strategies
- CDN for static assets

### Scalability
- Horizontal scaling capabilities
- Load balancing configuration
- Database sharding considerations

## Future Enhancements

### Planned Features
- AI-powered route optimization
- Predictive maintenance scheduling
- Advanced analytics and reporting
- Mobile driver applications
- IoT device integration

### Technical Improvements
- GraphQL API implementation
- Event-driven architecture
- Microservices decomposition
- Real-time data streaming

## Support & Documentation

### User Guides
- Fleet manager user manual
- Driver mobile app guide
- API integration guide

### Technical Documentation
- API reference documentation
- Database schema documentation
- Deployment guides

### Support Channels
- Technical support portal
- Community forums
- Developer documentation wiki
