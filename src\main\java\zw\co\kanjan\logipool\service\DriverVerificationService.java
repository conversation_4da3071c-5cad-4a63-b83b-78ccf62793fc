package zw.co.kanjan.logipool.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import zw.co.kanjan.logipool.dto.driver.DriverDto;
import zw.co.kanjan.logipool.dto.driver.DriverVerificationDto;
import zw.co.kanjan.logipool.entity.*;
import zw.co.kanjan.logipool.exception.BusinessException;
import zw.co.kanjan.logipool.exception.ResourceNotFoundException;
import zw.co.kanjan.logipool.mapper.DriverMapper;
import zw.co.kanjan.logipool.repository.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class DriverVerificationService {
    
    private final DriverVerificationRepository driverVerificationRepository;
    private final UserRepository userRepository;
    private final CompanyRepository companyRepository;
    private final CompanyMemberRepository companyMemberRepository;
    private final DriverMapper driverMapper;
    
    @Transactional(readOnly = true)
    public Page<DriverVerificationDto.VerificationTaskResponse> getPendingVerifications(Pageable pageable, String requesterUsername) {
        User requester = getUserByUsername(requesterUsername);
        
        Page<DriverVerification> verifications;
        if (isAdmin(requester)) {
            // Admin can see all pending verifications
            verifications = driverVerificationRepository.findByStatusOrderByCreatedAtAsc(
                    DriverDto.VerificationStatus.PENDING, pageable);
        } else {
            // Company admins can only see their company's pending verifications
            Company company = getCompanyByUser(requester);
            verifications = driverVerificationRepository.findByCompanyId(company.getId(), pageable);
        }
        
        return verifications.map(driverMapper::toVerificationTaskResponse);
    }
    
    @Transactional(readOnly = true)
    public Page<DriverVerificationDto.VerificationTaskResponse> getCompanyVerifications(
            Long companyId, Pageable pageable, String requesterUsername) {
        
        User requester = getUserByUsername(requesterUsername);
        Company company = getCompanyById(companyId);
        
        // Validate access permissions
        validateCompanyAccess(requester, company);
        
        Page<DriverVerification> verifications = driverVerificationRepository.findByCompanyId(companyId, pageable);
        return verifications.map(driverMapper::toVerificationTaskResponse);
    }
    
    @Transactional(readOnly = true)
    public DriverVerificationDto.VerificationSummaryResponse getDriverVerifications(Long driverId, String requesterUsername) {
        User requester = getUserByUsername(requesterUsername);
        User driver = getDriverById(driverId);
        
        // Validate access permissions
        validateDriverAccess(requester, driver);
        
        List<DriverVerification> verifications = driverVerificationRepository.findByDriverOrderByCreatedAtDesc(driver);
        return driverMapper.toVerificationSummaryResponse(driver, verifications);
    }
    
    public DriverVerificationDto.VerificationTaskResponse updateVerificationStatus(
            DriverVerificationDto.UpdateVerificationRequest request, String verifierUsername) {
        
        User verifier = getUserByUsername(verifierUsername);
        User driver = getDriverById(request.getDriverId());
        
        // Validate verifier permissions
        validateVerificationPermissions(verifier, driver);
        
        // Find the verification record
        DriverVerification verification = driverVerificationRepository
                .findByDriverAndVerificationType(driver, request.getVerificationType())
                .orElseThrow(() -> new ResourceNotFoundException("Verification task not found"));
        
        // Update verification
        verification.setStatus(request.getStatus());
        verification.setNotes(request.getNotes());
        verification.setVerifiedBy(verifier);
        verification.setVerifiedAt(request.getStatus() == DriverDto.VerificationStatus.VERIFIED ? 
                LocalDateTime.now() : null);
        
        DriverVerification savedVerification = driverVerificationRepository.save(verification);
        
        log.info("Verification {} updated for driver {} by verifier: {}", 
                request.getVerificationType(), request.getDriverId(), verifierUsername);
        
        return driverMapper.toVerificationTaskResponse(savedVerification);
    }
    
    public int bulkUpdateVerificationStatus(DriverVerificationDto.BulkVerificationUpdateRequest request, String verifierUsername) {
        User verifier = getUserByUsername(verifierUsername);
        
        int updatedCount = 0;
        for (DriverVerificationDto.UpdateVerificationRequest updateRequest : request.getVerificationUpdates()) {
            try {
                updateVerificationStatus(updateRequest, verifierUsername);
                updatedCount++;
            } catch (Exception e) {
                log.warn("Failed to update verification for driver {}: {}", 
                        updateRequest.getDriverId(), e.getMessage());
            }
        }
        
        log.info("Bulk verification update completed. Updated {} out of {} tasks by verifier: {}", 
                updatedCount, request.getVerificationUpdates().size(), verifierUsername);
        
        return updatedCount;
    }
    
    @Transactional(readOnly = true)
    public DriverVerificationDto.VerificationStatsResponse getVerificationStats(String requesterUsername) {
        User requester = getUserByUsername(requesterUsername);
        
        if (isAdmin(requester)) {
            return calculateGlobalVerificationStats();
        } else {
            Company company = getCompanyByUser(requester);
            return calculateCompanyVerificationStats(company.getId());
        }
    }
    
    @Transactional(readOnly = true)
    public DriverVerificationDto.VerificationStatsResponse getCompanyVerificationStats(Long companyId, String requesterUsername) {
        User requester = getUserByUsername(requesterUsername);
        Company company = getCompanyById(companyId);
        
        // Validate access permissions
        validateCompanyAccess(requester, company);
        
        return calculateCompanyVerificationStats(companyId);
    }
    
    @Transactional(readOnly = true)
    public Page<DriverVerificationDto.VerificationTaskResponse> getOverdueVerifications(
            int daysOverdue, Pageable pageable, String requesterUsername) {
        
        User requester = getUserByUsername(requesterUsername);
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(daysOverdue);
        
        List<DriverVerification> overdueVerifications = driverVerificationRepository.findStaleVerifications(cutoffDate);
        
        // Filter by company if not admin
        if (!isAdmin(requester)) {
            Company company = getCompanyByUser(requester);
            overdueVerifications = overdueVerifications.stream()
                    .filter(v -> {
                        CompanyMember membership = companyMemberRepository.findActiveByUser(v.getDriver()).orElse(null);
                        return membership != null && membership.getCompany().equals(company);
                    })
                    .collect(Collectors.toList());
        }
        
        // Convert to page (simplified implementation)
        List<DriverVerificationDto.VerificationTaskResponse> responses = overdueVerifications.stream()
                .map(driverMapper::toVerificationTaskResponse)
                .collect(Collectors.toList());
        
        // For simplicity, returning all results. In production, implement proper pagination
        return new org.springframework.data.domain.PageImpl<>(responses, pageable, responses.size());
    }
    
    public DriverVerificationDto.VerificationSummaryResponse resetDriverVerifications(Long driverId, String adminUsername) {
        User admin = getUserByUsername(adminUsername);
        
        if (!isAdmin(admin)) {
            throw new BusinessException("Only admins can reset driver verifications");
        }
        
        User driver = getDriverById(driverId);
        
        // Reset all verifications to pending
        List<DriverVerification> verifications = driverVerificationRepository.findByDriverOrderByCreatedAtDesc(driver);
        for (DriverVerification verification : verifications) {
            verification.setStatus(DriverDto.VerificationStatus.PENDING);
            verification.setNotes("Reset by admin: " + admin.getFirstName() + " " + admin.getLastName());
            verification.setVerifiedBy(null);
            verification.setVerifiedAt(null);
        }
        
        driverVerificationRepository.saveAll(verifications);
        
        log.info("All verification tasks reset for driver {} by admin: {}", driverId, adminUsername);
        
        return driverMapper.toVerificationSummaryResponse(driver, verifications);
    }
    
    @Transactional(readOnly = true)
    public String exportVerificationData(Long companyId, String format, String requesterUsername) {
        User requester = getUserByUsername(requesterUsername);
        
        // Validate permissions and get data
        List<DriverVerification> verifications;
        if (companyId != null) {
            Company company = getCompanyById(companyId);
            validateCompanyAccess(requester, company);
            verifications = driverVerificationRepository.findByCompanyId(companyId, Pageable.unpaged()).getContent();
        } else if (isAdmin(requester)) {
            verifications = driverVerificationRepository.findAll();
        } else {
            Company company = getCompanyByUser(requester);
            verifications = driverVerificationRepository.findByCompanyId(company.getId(), Pageable.unpaged()).getContent();
        }
        
        // Generate export data based on format
        if ("CSV".equalsIgnoreCase(format)) {
            return generateCsvExport(verifications);
        } else {
            throw new BusinessException("Unsupported export format: " + format);
        }
    }
    
    // Helper methods

    private DriverVerificationDto.VerificationStatsResponse calculateGlobalVerificationStats() {
        long totalDrivers = userRepository.countByRoles_Name(Role.RoleName.DRIVER);

        // Get status counts
        List<Object[]> statusCounts = driverVerificationRepository.getVerificationStatusCounts();

        // Get verification type statistics
        List<Object[]> typeStatusCounts = driverVerificationRepository.getVerificationTypeStatusCounts();
        List<DriverVerificationDto.VerificationTypeStats> typeStats = calculateVerificationTypeStats(typeStatusCounts);

        // Calculate overall completion rate
        long verifiedCount = statusCounts.stream()
                .filter(row -> row[0] == DriverDto.VerificationStatus.VERIFIED)
                .mapToLong(row -> (Long) row[1])
                .sum();
        long totalTasks = statusCounts.stream().mapToLong(row -> (Long) row[1]).sum();
        double completionRate = totalTasks > 0 ? (double) verifiedCount / totalTasks * 100 : 0.0;

        return DriverVerificationDto.VerificationStatsResponse.builder()
                .totalDrivers(totalDrivers)
                .fullyVerifiedDrivers(0L) // Simplified for now
                .partiallyVerifiedDrivers(0L)
                .unverifiedDrivers(0L)
                .expiredVerificationDrivers(0L)
                .verificationCompletionRate(completionRate)
                .verificationTypeStats(typeStats)
                .build();
    }

    private DriverVerificationDto.VerificationStatsResponse calculateCompanyVerificationStats(Long companyId) {
        // Simplified implementation - in production, this would be more detailed
        return calculateGlobalVerificationStats();
    }

    private List<DriverVerificationDto.VerificationTypeStats> calculateVerificationTypeStats(List<Object[]> typeStatusCounts) {
        // Group by verification type
        java.util.Map<DriverVerificationDto.VerificationType, java.util.Map<DriverDto.VerificationStatus, Long>> typeStatusMap =
                new java.util.HashMap<>();

        for (Object[] row : typeStatusCounts) {
            DriverVerificationDto.VerificationType type = (DriverVerificationDto.VerificationType) row[0];
            DriverDto.VerificationStatus status = (DriverDto.VerificationStatus) row[1];
            Long count = (Long) row[2];

            typeStatusMap.computeIfAbsent(type, k -> new java.util.HashMap<>()).put(status, count);
        }

        return typeStatusMap.entrySet().stream()
                .map(entry -> {
                    DriverVerificationDto.VerificationType type = entry.getKey();
                    java.util.Map<DriverDto.VerificationStatus, Long> statusCounts = entry.getValue();

                    long verified = statusCounts.getOrDefault(DriverDto.VerificationStatus.VERIFIED, 0L);
                    long pending = statusCounts.getOrDefault(DriverDto.VerificationStatus.PENDING, 0L);
                    long rejected = statusCounts.getOrDefault(DriverDto.VerificationStatus.REJECTED, 0L);
                    long expired = statusCounts.getOrDefault(DriverDto.VerificationStatus.EXPIRED, 0L);
                    long total = verified + pending + rejected + expired;

                    double completionRate = total > 0 ? (double) verified / total * 100 : 0.0;

                    return DriverVerificationDto.VerificationTypeStats.builder()
                            .verificationType(type)
                            .taskName(type.getTaskName())
                            .verifiedCount(verified)
                            .pendingCount(pending)
                            .rejectedCount(rejected)
                            .expiredCount(expired)
                            .completionRate(completionRate)
                            .build();
                })
                .collect(Collectors.toList());
    }

    private String generateCsvExport(List<DriverVerification> verifications) {
        StringBuilder csv = new StringBuilder();
        csv.append("Driver Name,Driver Email,Verification Type,Status,Notes,Verified By,Verified At,Created At\n");

        for (DriverVerification verification : verifications) {
            csv.append(String.format("%s %s,%s,%s,%s,%s,%s,%s,%s\n",
                    verification.getDriver().getFirstName(),
                    verification.getDriver().getLastName(),
                    verification.getDriver().getEmail(),
                    verification.getVerificationType().getTaskName(),
                    verification.getStatus(),
                    verification.getNotes() != null ? verification.getNotes().replace(",", ";") : "",
                    verification.getVerifiedBy() != null ?
                            verification.getVerifiedBy().getFirstName() + " " + verification.getVerifiedBy().getLastName() : "",
                    verification.getVerifiedAt() != null ? verification.getVerifiedAt().toString() : "",
                    verification.getCreatedAt().toString()
            ));
        }

        return csv.toString();
    }

    private User getUserByUsername(String username) {
        return userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found: " + username));
    }

    private User getDriverById(Long id) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Driver not found with id: " + id));

        if (!hasDriverRole(user)) {
            throw new BusinessException("User is not a driver");
        }

        return user;
    }

    private Company getCompanyById(Long id) {
        return companyRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Company not found with id: " + id));
    }

    private Company getCompanyByUser(User user) {
        return companyRepository.findByUser(user)
                .orElseThrow(() -> new BusinessException("Company not found for user"));
    }

    private boolean hasDriverRole(User user) {
        return user.getRoles().stream()
                .anyMatch(role -> role.getName() == Role.RoleName.DRIVER);
    }

    private boolean isAdmin(User user) {
        return user.getRoles().stream()
                .anyMatch(role -> role.getName() == Role.RoleName.ADMIN);
    }

    private void validateCompanyAccess(User requester, Company company) {
        if (isAdmin(requester)) {
            return; // Admins can access any company
        }

        CompanyMember membership = companyMemberRepository
                .findByUserAndCompanyAndStatus(requester, company, CompanyMember.MemberStatus.ACTIVE)
                .orElseThrow(() -> new BusinessException("Access denied to company data"));

        if (membership.getRole() != CompanyMember.CompanyRole.OWNER &&
            membership.getRole() != CompanyMember.CompanyRole.MANAGER) {
            throw new BusinessException("Insufficient permissions to access company verification data");
        }
    }

    private void validateDriverAccess(User requester, User driver) {
        if (isAdmin(requester)) {
            return; // Admins can access any driver
        }

        if (requester.equals(driver)) {
            return; // Drivers can access their own verification status
        }

        // Check if requester is from the same company as the driver
        CompanyMember requesterMembership = companyMemberRepository.findActiveByUser(requester).orElse(null);
        CompanyMember driverMembership = companyMemberRepository.findActiveByUser(driver).orElse(null);

        if (requesterMembership != null && driverMembership != null &&
            requesterMembership.getCompany().equals(driverMembership.getCompany()) &&
            (requesterMembership.getRole() == CompanyMember.CompanyRole.OWNER ||
             requesterMembership.getRole() == CompanyMember.CompanyRole.MANAGER)) {
            return;
        }

        throw new BusinessException("Access denied to driver verification data");
    }

    private void validateVerificationPermissions(User verifier, User driver) {
        if (isAdmin(verifier)) {
            return; // Admins can verify any driver
        }

        // Check if verifier is a company admin for the driver's company
        CompanyMember driverMembership = companyMemberRepository.findActiveByUser(driver).orElse(null);
        if (driverMembership == null) {
            throw new BusinessException("Driver is not assigned to any company");
        }

        CompanyMember verifierMembership = companyMemberRepository
                .findByUserAndCompanyAndStatus(verifier, driverMembership.getCompany(), CompanyMember.MemberStatus.ACTIVE)
                .orElseThrow(() -> new BusinessException("Access denied to verify this driver"));

        if (verifierMembership.getRole() != CompanyMember.CompanyRole.OWNER &&
            verifierMembership.getRole() != CompanyMember.CompanyRole.MANAGER) {
            throw new BusinessException("Insufficient permissions to verify drivers");
        }
    }
}
