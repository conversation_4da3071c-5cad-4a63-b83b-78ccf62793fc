package zw.co.kanjan.logipool.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import zw.co.kanjan.logipool.dto.admin.AdminDto;
import zw.co.kanjan.logipool.entity.*;
import zw.co.kanjan.logipool.exception.BusinessException;
import zw.co.kanjan.logipool.exception.ResourceNotFoundException;
import zw.co.kanjan.logipool.repository.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class AdminService {

    private final UserRepository userRepository;
    private final CompanyRepository companyRepository;
    private final LoadRepository loadRepository;
    private final BidRepository bidRepository;
    private final PaymentRepository paymentRepository;
    private final CommissionRepository commissionRepository;
    private final LoadTrackingRepository trackingRepository;
    private final NotificationRepository notificationRepository;
    private final RoleRepository roleRepository;
    private final NotificationService notificationService;

    @Transactional(readOnly = true)
    public AdminDto.DashboardOverview getDashboardOverview() {
        log.info("Generating admin dashboard overview");

        // Get basic counts
        Long totalUsers = userRepository.count();
        Long totalCompanies = companyRepository.count();
        Long totalLoads = loadRepository.count();
        Long totalBids = bidRepository.count();
        Long activeLoads = loadRepository.countByStatus(Load.LoadStatus.POSTED);
        Long completedLoads = loadRepository.countByStatus(Load.LoadStatus.DELIVERED);
        Long pendingVerifications = companyRepository.countByVerificationStatus(Company.VerificationStatus.PENDING);

        // Calculate revenue
        BigDecimal totalRevenue = commissionRepository.findAll().stream()
                .filter(c -> c.getStatus() == Commission.CommissionStatus.COLLECTED)
                .map(Commission::getCommissionAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        LocalDateTime monthStart = LocalDateTime.now().withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);
        BigDecimal monthlyRevenue = commissionRepository.findByCreatedAtAfter(monthStart).stream()
                .filter(c -> c.getStatus() == Commission.CommissionStatus.COLLECTED)
                .map(Commission::getCommissionAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // Get recent activities
        List<AdminDto.RecentActivity> recentActivities = getRecentActivities();

        // Get system health
        AdminDto.SystemHealth systemHealth = getSystemHealth();

        return AdminDto.DashboardOverview.builder()
                .totalUsers(totalUsers)
                .totalCompanies(totalCompanies)
                .totalLoads(totalLoads)
                .totalBids(totalBids)
                .activeLoads(activeLoads)
                .completedLoads(completedLoads)
                .pendingVerifications(pendingVerifications)
                .totalRevenue(totalRevenue)
                .monthlyRevenue(monthlyRevenue)
                .recentActivities(recentActivities)
                .systemHealth(systemHealth)
                .generatedAt(LocalDateTime.now())
                .build();
    }

    @Transactional(readOnly = true)
    public Page<AdminDto.UserManagementResponse> getAllUsers(Pageable pageable) {
        log.info("Getting all users for admin management");

        Page<User> users = userRepository.findAll(pageable);
        
        List<AdminDto.UserManagementResponse> userResponses = users.getContent().stream()
                .map(this::mapToUserManagementResponse)
                .collect(Collectors.toList());

        return new PageImpl<>(userResponses, pageable, users.getTotalElements());
    }

    @Transactional(readOnly = true)
    public Page<AdminDto.UserManagementResponse> getUsersByStatus(User.UserStatus status, Pageable pageable) {
        Page<User> users = userRepository.findByStatus(status, pageable);
        
        List<AdminDto.UserManagementResponse> userResponses = users.getContent().stream()
                .map(this::mapToUserManagementResponse)
                .collect(Collectors.toList());

        return new PageImpl<>(userResponses, pageable, users.getTotalElements());
    }

    @Transactional(readOnly = true)
    public Page<AdminDto.UserManagementResponse> searchUsers(String searchTerm, Pageable pageable) {
        Page<User> users = userRepository.findByUsernameContainingIgnoreCaseOrEmailContainingIgnoreCaseOrFirstNameContainingIgnoreCaseOrLastNameContainingIgnoreCase(
                searchTerm, searchTerm, searchTerm, searchTerm, pageable);
        
        List<AdminDto.UserManagementResponse> userResponses = users.getContent().stream()
                .map(this::mapToUserManagementResponse)
                .collect(Collectors.toList());

        return new PageImpl<>(userResponses, pageable, users.getTotalElements());
    }

    public AdminDto.UserManagementResponse updateUser(AdminDto.UserUpdateRequest request) {
        log.info("Admin updating user: {}", request.getUserId());

        User user = userRepository.findById(request.getUserId())
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + request.getUserId()));

        // Update user fields
        if (request.getEmail() != null) {
            user.setEmail(request.getEmail());
        }
        if (request.getFirstName() != null) {
            user.setFirstName(request.getFirstName());
        }
        if (request.getLastName() != null) {
            user.setLastName(request.getLastName());
        }
        if (request.getPhoneNumber() != null) {
            user.setPhoneNumber(request.getPhoneNumber());
        }
        if (request.getStatus() != null) {
            user.setStatus(request.getStatus());
        }

        // Update roles if provided
        if (request.getRoles() != null && !request.getRoles().isEmpty()) {
            Set<Role> newRoles = request.getRoles().stream()
                    .map(roleName -> roleRepository.findByName(roleName)
                            .orElseThrow(() -> new ResourceNotFoundException("Role not found: " + roleName)))
                    .collect(Collectors.toSet());
            user.setRoles(newRoles);
        }

        User savedUser = userRepository.save(user);

        // Send notification to user about account update
        notificationService.sendNotification(
                savedUser,
                "Account Updated",
                "Your account has been updated by an administrator.",
                "ACCOUNT_UPDATE"
        );

        return mapToUserManagementResponse(savedUser);
    }

    @Transactional(readOnly = true)
    public Page<AdminDto.CompanyManagementResponse> getAllCompanies(Pageable pageable) {
        log.info("Getting all companies for admin management");

        Page<Company> companies = companyRepository.findAll(pageable);
        
        List<AdminDto.CompanyManagementResponse> companyResponses = companies.getContent().stream()
                .map(this::mapToCompanyManagementResponse)
                .collect(Collectors.toList());

        return new PageImpl<>(companyResponses, pageable, companies.getTotalElements());
    }

    @Transactional(readOnly = true)
    public Page<AdminDto.CompanyManagementResponse> getCompaniesByVerificationStatus(
            Company.VerificationStatus status, Pageable pageable) {
        Page<Company> companies = companyRepository.findByVerificationStatus(status, pageable);

        List<AdminDto.CompanyManagementResponse> companyResponses = companies.getContent().stream()
                .map(this::mapToCompanyManagementResponse)
                .collect(Collectors.toList());

        return new PageImpl<>(companyResponses, pageable, companies.getTotalElements());
    }

    @Transactional(readOnly = true)
    public AdminDto.CompanyManagementResponse getCompanyById(Long companyId) {
        log.info("Getting company details for admin management: {}", companyId);

        Company company = companyRepository.findById(companyId)
                .orElseThrow(() -> new ResourceNotFoundException("Company not found with id: " + companyId));

        return mapToCompanyManagementResponse(company);
    }

    @Transactional
    public AdminDto.CompanyManagementResponse suspendCompany(Long companyId, AdminDto.CompanySuspensionRequest request) {
        log.info("Suspending company: {} with reason: {}", companyId, request.getReason());

        Company company = companyRepository.findById(companyId)
                .orElseThrow(() -> new ResourceNotFoundException("Company not found with id: " + companyId));

        // Update company status to SUSPENDED
        company.setVerificationStatus(Company.VerificationStatus.SUSPENDED);

        // Create audit log entry
        createAuditLog("COMPANY_SUSPENDED", "Company", companyId,
                String.format("Company suspended. Reason: %s. Notes: %s",
                        request.getReason(), request.getNotes()), "admin");

        Company savedCompany = companyRepository.save(company);
        return mapToCompanyManagementResponse(savedCompany);
    }

    @Transactional
    public AdminDto.CompanyManagementResponse unsuspendCompany(Long companyId, AdminDto.CompanyUnsuspensionRequest request) {
        log.info("Unsuspending company: {} with reason: {}", companyId, request.getReason());

        Company company = companyRepository.findById(companyId)
                .orElseThrow(() -> new ResourceNotFoundException("Company not found with id: " + companyId));

        // Update company status back to VERIFIED (assuming it was verified before suspension)
        company.setVerificationStatus(Company.VerificationStatus.VERIFIED);

        // Create audit log entry
        createAuditLog("COMPANY_UNSUSPENDED", "Company", companyId,
                String.format("Company unsuspended. Reason: %s. Notes: %s",
                        request.getReason(), request.getNotes()), "admin");

        Company savedCompany = companyRepository.save(company);
        return mapToCompanyManagementResponse(savedCompany);
    }

    public AdminDto.CompanyManagementResponse verifyCompany(AdminDto.CompanyVerificationRequest request) {
        log.info("Admin verifying company: {} with status: {}", request.getCompanyId(), request.getStatus());

        Company company = companyRepository.findById(request.getCompanyId())
                .orElseThrow(() -> new ResourceNotFoundException("Company not found with id: " + request.getCompanyId()));

        company.setVerificationStatus(request.getStatus());

        Company savedCompany = companyRepository.save(company);

        // Send notification to company owner
        String notificationTitle = request.getStatus() == Company.VerificationStatus.VERIFIED 
                ? "Company Verified" : "Company Verification Update";
        String notificationMessage = request.getStatus() == Company.VerificationStatus.VERIFIED
                ? "Your company has been successfully verified!"
                : "Your company verification status has been updated to: " + request.getStatus();

        notificationService.sendNotification(
                savedCompany.getUser(),
                notificationTitle,
                notificationMessage,
                "COMPANY_VERIFICATION"
        );

        return mapToCompanyManagementResponse(savedCompany);
    }

    @Transactional(readOnly = true)
    public Page<AdminDto.LoadManagementResponse> getAllLoads(Pageable pageable) {
        log.info("Getting all loads for admin management");

        Page<Load> loads = loadRepository.findAll(pageable);
        
        List<AdminDto.LoadManagementResponse> loadResponses = loads.getContent().stream()
                .map(this::mapToLoadManagementResponse)
                .collect(Collectors.toList());

        return new PageImpl<>(loadResponses, pageable, loads.getTotalElements());
    }

    @Transactional(readOnly = true)
    public Page<AdminDto.LoadManagementResponse> getLoadsByStatus(Load.LoadStatus status, Pageable pageable) {
        Page<Load> loads = loadRepository.findByStatus(status, pageable);
        
        List<AdminDto.LoadManagementResponse> loadResponses = loads.getContent().stream()
                .map(this::mapToLoadManagementResponse)
                .collect(Collectors.toList());

        return new PageImpl<>(loadResponses, pageable, loads.getTotalElements());
    }

    @Transactional(readOnly = true)
    public AdminDto.SystemAnalytics getSystemAnalytics() {
        log.info("Generating system analytics");

        AdminDto.UserAnalytics userAnalytics = generateUserAnalytics();
        AdminDto.LoadAnalytics loadAnalytics = generateLoadAnalytics();
        AdminDto.RevenueAnalytics revenueAnalytics = generateRevenueAnalytics();
        AdminDto.PerformanceAnalytics performanceAnalytics = generatePerformanceAnalytics();

        return AdminDto.SystemAnalytics.builder()
                .userAnalytics(userAnalytics)
                .loadAnalytics(loadAnalytics)
                .revenueAnalytics(revenueAnalytics)
                .performanceAnalytics(performanceAnalytics)
                .generatedAt(LocalDateTime.now())
                .build();
    }

    public AdminDto.BulkActionResponse performBulkUserAction(AdminDto.BulkActionRequest request) {
        log.info("Performing bulk action: {} on {} users", request.getAction(), request.getEntityIds().size());

        List<Long> processedIds = new ArrayList<>();
        List<String> errors = new ArrayList<>();
        int successful = 0;

        for (Long userId : request.getEntityIds()) {
            try {
                User user = userRepository.findById(userId)
                        .orElseThrow(() -> new ResourceNotFoundException("User not found: " + userId));

                switch (request.getAction().toUpperCase()) {
                    case "ACTIVATE":
                        user.setStatus(User.UserStatus.ACTIVE);
                        break;
                    case "DEACTIVATE":
                        user.setStatus(User.UserStatus.INACTIVE);
                        break;
                    case "SUSPEND":
                        user.setStatus(User.UserStatus.SUSPENDED);
                        break;
                    default:
                        throw new BusinessException("Unsupported action: " + request.getAction());
                }

                userRepository.save(user);
                processedIds.add(userId);
                successful++;

                // Send notification to user
                notificationService.sendNotification(
                        user,
                        "Account Status Update",
                        "Your account status has been updated by an administrator.",
                        "ACCOUNT_STATUS_UPDATE"
                );

            } catch (Exception e) {
                errors.add("User " + userId + ": " + e.getMessage());
                log.error("Failed to process user {}: {}", userId, e.getMessage());
            }
        }

        return AdminDto.BulkActionResponse.builder()
                .totalRequested(request.getEntityIds().size())
                .successful(successful)
                .failed(errors.size())
                .errors(errors)
                .processedIds(processedIds)
                .processedAt(LocalDateTime.now())
                .build();
    }

    // Private helper methods
    private AdminDto.UserManagementResponse mapToUserManagementResponse(User user) {
        List<String> roleNames = user.getRoles().stream()
                .map(role -> role.getName().name())
                .collect(Collectors.toList());

        return AdminDto.UserManagementResponse.builder()
                .id(user.getId())
                .username(user.getUsername())
                .email(user.getEmail())
                .firstName(user.getFirstName())
                .lastName(user.getLastName())
                .phoneNumber(user.getPhoneNumber())
                .status(user.getStatus())
                .roles(roleNames)
                .companyName(user.getCompany() != null ? user.getCompany().getName() : null)
                .companyId(user.getCompany() != null ? user.getCompany().getId() : null)
                .isVerified(user.getCompany() != null && 
                           user.getCompany().getVerificationStatus() == Company.VerificationStatus.VERIFIED)
                .createdAt(user.getCreatedAt())
                .lastLoginAt(user.getLastLoginAt())
                .build();
    }

    private AdminDto.CompanyManagementResponse mapToCompanyManagementResponse(Company company) {
        // Get company statistics
        Integer totalVehicles = company.getVehicles() != null ? company.getVehicles().size() : 0;
        Integer totalLoads = loadRepository.countByAssignedCompany(company);
        
        // Calculate total revenue for this company
        BigDecimal totalRevenue = paymentRepository.findByLoad_AssignedCompany(company).stream()
                .filter(payment -> payment.getStatus() == Payment.PaymentStatus.COMPLETED)
                .map(Payment::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return AdminDto.CompanyManagementResponse.builder()
                .id(company.getId())
                .name(company.getName())
                .registrationNumber(company.getRegistrationNumber())
                .email(company.getEmail())
                .phoneNumber(company.getPhoneNumber())
                .address(company.getAddress())
                .verificationStatus(company.getVerificationStatus())
                .ownerName(company.getUser().getFirstName() + " " + company.getUser().getLastName())
                .ownerUsername(company.getUser().getUsername())
                .ownerId(company.getUser().getId())
                .totalVehicles(totalVehicles)
                .totalLoads(totalLoads)
                .totalRevenue(totalRevenue)
                .createdAt(company.getCreatedAt())
                .verifiedAt(company.getVerificationStatus() == Company.VerificationStatus.VERIFIED ?
                           company.getCreatedAt() : null)
                .build();
    }

    private AdminDto.LoadManagementResponse mapToLoadManagementResponse(Load load) {
        Integer totalBids = bidRepository.countByLoadAsInteger(load);

        return AdminDto.LoadManagementResponse.builder()
                .id(load.getId())
                .title(load.getTitle())
                .description(load.getDescription())
                .pickupLocation(load.getPickupLocation())
                .deliveryLocation(load.getDeliveryLocation())
                .weight(load.getWeight())
                .price(load.getEstimatedValue() != null ? load.getEstimatedValue() : load.getPaymentRate())
                .status(load.getStatus())
                .clientName(load.getClient().getFirstName() + " " + load.getClient().getLastName())
                .clientUsername(load.getClient().getUsername())
                .clientId(load.getClient().getId())
                .assignedCompanyName(load.getAssignedCompany() != null ? load.getAssignedCompany().getName() : null)
                .assignedCompanyId(load.getAssignedCompany() != null ? load.getAssignedCompany().getId() : null)
                .totalBids(totalBids)
                .isVerified(load.getIsVerified())
                .createdAt(load.getCreatedAt())
                .deliveryDate(load.getDeliveryDate())
                .build();
    }

    private List<AdminDto.RecentActivity> getRecentActivities() {
        List<AdminDto.RecentActivity> activities = new ArrayList<>();

        // Get recent user registrations
        List<User> recentUsers = userRepository.findTop5ByOrderByCreatedAtDesc();
        for (User user : recentUsers) {
            activities.add(AdminDto.RecentActivity.builder()
                    .type("USER_REGISTERED")
                    .description("New user registered: " + user.getUsername())
                    .username(user.getUsername())
                    .entityId(user.getId())
                    .timestamp(user.getCreatedAt())
                    .build());
        }

        // Get recent loads
        List<Load> recentLoads = loadRepository.findTop5ByOrderByCreatedAtDesc();
        for (Load load : recentLoads) {
            activities.add(AdminDto.RecentActivity.builder()
                    .type("LOAD_POSTED")
                    .description("New load posted: " + load.getTitle())
                    .username(load.getClient().getUsername())
                    .entityId(load.getId())
                    .timestamp(load.getCreatedAt())
                    .build());
        }

        // Sort by timestamp descending and limit to 10
        return activities.stream()
                .sorted((a, b) -> b.getTimestamp().compareTo(a.getTimestamp()))
                .limit(10)
                .collect(Collectors.toList());
    }

    private AdminDto.SystemHealth getSystemHealth() {
        // This would typically integrate with actual system monitoring
        // For now, we'll return mock data
        return AdminDto.SystemHealth.builder()
                .status("HEALTHY")
                .cpuUsage(45.2)
                .memoryUsage(67.8)
                .diskUsage(23.4)
                .activeConnections(156L)
                .databaseConnections(12L)
                .alerts(new ArrayList<>())
                .metrics(Map.of(
                        "uptime", "7 days, 14 hours",
                        "requests_per_minute", 234,
                        "average_response_time", "120ms"
                ))
                .build();
    }

    private AdminDto.UserAnalytics generateUserAnalytics() {
        Long totalUsers = userRepository.count();
        Long activeUsers = userRepository.countByStatus(User.UserStatus.ACTIVE);
        
        LocalDateTime monthStart = LocalDateTime.now().withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);
        Long newUsersThisMonth = userRepository.countByCreatedAtAfter(monthStart);

        // Count users by role
        Long clientUsers = userRepository.countByRoles_Name(Role.RoleName.CLIENT);
        Long transporterUsers = userRepository.countByRoles_Name(Role.RoleName.TRANSPORTER);
        Long adminUsers = userRepository.countByRoles_Name(Role.RoleName.ADMIN);

        Map<String, Long> usersByStatus = Arrays.stream(User.UserStatus.values())
                .collect(Collectors.toMap(
                        Enum::name,
                        status -> userRepository.countByStatus(status)
                ));

        return AdminDto.UserAnalytics.builder()
                .totalUsers(totalUsers)
                .activeUsers(activeUsers)
                .newUsersThisMonth(newUsersThisMonth)
                .clientUsers(clientUsers)
                .transporterUsers(transporterUsers)
                .adminUsers(adminUsers)
                .usersByStatus(usersByStatus)
                .registrationTrends(new HashMap<>()) // Would be populated with actual trend data
                .build();
    }

    private AdminDto.LoadAnalytics generateLoadAnalytics() {
        Long totalLoads = loadRepository.count();
        Long activeLoads = loadRepository.countByStatus(Load.LoadStatus.POSTED);
        Long completedLoads = loadRepository.countByStatus(Load.LoadStatus.DELIVERED);
        Long cancelledLoads = loadRepository.countByStatus(Load.LoadStatus.CANCELLED);

        Map<String, Long> loadsByStatus = Arrays.stream(Load.LoadStatus.values())
                .collect(Collectors.toMap(
                        Enum::name,
                        status -> loadRepository.countByStatus(status)
                ));

        BigDecimal totalLoadValue = loadRepository.findAll().stream()
                .map(load -> load.getEstimatedValue() != null ? load.getEstimatedValue() :
                            (load.getPaymentRate() != null ? load.getPaymentRate() : BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal averageLoadValue = totalLoads > 0 
                ? totalLoadValue.divide(BigDecimal.valueOf(totalLoads), 2, RoundingMode.HALF_UP)
                : BigDecimal.ZERO;

        return AdminDto.LoadAnalytics.builder()
                .totalLoads(totalLoads)
                .activeLoads(activeLoads)
                .completedLoads(completedLoads)
                .cancelledLoads(cancelledLoads)
                .loadsByStatus(loadsByStatus)
                .loadTrends(new HashMap<>()) // Would be populated with actual trend data
                .averageLoadValue(averageLoadValue)
                .totalLoadValue(totalLoadValue)
                .build();
    }

    private AdminDto.RevenueAnalytics generateRevenueAnalytics() {
        BigDecimal totalRevenue = commissionRepository.findAll().stream()
                .filter(c -> c.getStatus() == Commission.CommissionStatus.COLLECTED)
                .map(Commission::getCommissionAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        LocalDateTime monthStart = LocalDateTime.now().withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);
        BigDecimal monthlyRevenue = commissionRepository.findByCreatedAtAfter(monthStart).stream()
                .filter(c -> c.getStatus() == Commission.CommissionStatus.COLLECTED)
                .map(Commission::getCommissionAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        LocalDateTime dayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
        BigDecimal dailyRevenue = commissionRepository.findByCreatedAtAfter(dayStart).stream()
                .filter(c -> c.getStatus() == Commission.CommissionStatus.COLLECTED)
                .map(Commission::getCommissionAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return AdminDto.RevenueAnalytics.builder()
                .totalRevenue(totalRevenue)
                .monthlyRevenue(monthlyRevenue)
                .dailyRevenue(dailyRevenue)
                .averageCommission(BigDecimal.valueOf(75.0)) // Mock data
                .revenueByMonth(new HashMap<>()) // Would be populated with actual data
                .revenueByCompany(new HashMap<>()) // Would be populated with actual data
                .projectedMonthlyRevenue(monthlyRevenue.multiply(BigDecimal.valueOf(1.15))) // 15% growth projection
                .build();
    }

    private AdminDto.PerformanceAnalytics generatePerformanceAnalytics() {
        // This would typically integrate with actual performance monitoring
        return AdminDto.PerformanceAnalytics.builder()
                .averageResponseTime(120.5)
                .totalApiCalls(45678L)
                .errorCount(23L)
                .errorRate(0.05)
                .endpointUsage(Map.of(
                        "/api/loads", 15234L,
                        "/api/bids", 8765L,
                        "/api/companies", 5432L
                ))
                .performanceMetrics(Map.of(
                        "database_query_time", 45.2,
                        "cache_hit_rate", 89.5,
                        "memory_usage", 67.8
                ))
                .build();
    }

    @Transactional(readOnly = true)
    public AdminDto.SystemConfigurationResponse getSystemConfiguration() {
        log.info("Getting system configuration");

        Map<String, Object> applicationSettings = Map.of(
                "app.name", "LogiPool",
                "app.version", "1.0.0",
                "app.environment", "production",
                "app.maintenance.enabled", false
        );

        Map<String, Object> securitySettings = Map.of(
                "jwt.expiration", "24h",
                "password.min-length", 8,
                "rate-limit.enabled", true,
                "two-factor.enabled", false
        );

        Map<String, Object> notificationSettings = Map.of(
                "email.enabled", true,
                "sms.enabled", true,
                "push.enabled", true,
                "realtime.enabled", true
        );

        Map<String, Object> paymentSettings = Map.of(
                "commission.rate", "7.5%",
                "payment.timeout", "24h",
                "auto-collection.enabled", true,
                "refund.enabled", true
        );

        Map<String, Object> trackingSettings = Map.of(
                "gps.enabled", true,
                "real-time.enabled", true,
                "update-interval", "30s",
                "stale-threshold", "6h"
        );

        Map<String, Object> fileSettings = Map.of(
                "max-file-size", "10MB",
                "allowed-types", "pdf,doc,docx,jpg,jpeg,png",
                "storage.type", "local",
                "cleanup.enabled", true
        );

        return AdminDto.SystemConfigurationResponse.builder()
                .applicationSettings(applicationSettings)
                .securitySettings(securitySettings)
                .notificationSettings(notificationSettings)
                .paymentSettings(paymentSettings)
                .trackingSettings(trackingSettings)
                .fileSettings(fileSettings)
                .build();
    }

    public AdminDto.SystemConfigurationResponse updateSystemConfiguration(
            AdminDto.SystemConfigurationUpdateRequest request) {
        log.info("Admin updating system configuration: {}", request.getCategory());

        // In a real implementation, this would update actual configuration
        // For now, we'll just log the update and return current configuration
        log.info("Configuration update for category '{}': {}",
                request.getCategory(), request.getSettings());

        // Create audit log entry
        createAuditLog("SYSTEM_CONFIG_UPDATE", "SystemConfiguration", null,
                "Updated " + request.getCategory() + " configuration", "admin");

        return getSystemConfiguration();
    }

    @Transactional(readOnly = true)
    public Page<AdminDto.AuditLogResponse> getAuditLogs(Pageable pageable) {
        log.info("Getting audit logs");

        // Mock audit log data - in real implementation, this would come from an audit log table
        List<AdminDto.AuditLogResponse> auditLogs = List.of(
                AdminDto.AuditLogResponse.builder()
                        .id(1L)
                        .action("USER_STATUS_UPDATE")
                        .entityType("User")
                        .entityId(123L)
                        .adminUsername("admin")
                        .adminName("System Administrator")
                        .details("Changed user status from ACTIVE to SUSPENDED")
                        .ipAddress("*************")
                        .userAgent("Mozilla/5.0...")
                        .timestamp(LocalDateTime.now().minusHours(2))
                        .build(),
                AdminDto.AuditLogResponse.builder()
                        .id(2L)
                        .action("COMPANY_VERIFICATION")
                        .entityType("Company")
                        .entityId(456L)
                        .adminUsername("admin")
                        .adminName("System Administrator")
                        .details("Verified company: ABC Transport Ltd")
                        .ipAddress("*************")
                        .userAgent("Mozilla/5.0...")
                        .timestamp(LocalDateTime.now().minusHours(4))
                        .build()
        );

        return new PageImpl<>(auditLogs, pageable, auditLogs.size());
    }

    @Transactional(readOnly = true)
    public List<AdminDto.SystemAlertResponse> getSystemAlerts() {
        log.info("Getting system alerts");

        // Mock alert data - in real implementation, this would come from a system alerts table
        return List.of(
                AdminDto.SystemAlertResponse.builder()
                        .id(1L)
                        .type("PERFORMANCE")
                        .severity("MEDIUM")
                        .title("High Memory Usage")
                        .message("System memory usage is above 80%")
                        .source("System Monitor")
                        .isResolved(false)
                        .createdAt(LocalDateTime.now().minusHours(1))
                        .build(),
                AdminDto.SystemAlertResponse.builder()
                        .id(2L)
                        .type("BUSINESS")
                        .severity("LOW")
                        .title("Pending Verifications")
                        .message("5 companies are pending verification")
                        .source("Business Logic")
                        .isResolved(false)
                        .createdAt(LocalDateTime.now().minusHours(3))
                        .build()
        );
    }

    public AdminDto.SystemAlertResponse resolveAlert(AdminDto.AlertResolutionRequest request) {
        log.info("Admin resolving alert: {}", request.getAlertId());

        // In real implementation, this would update the alert in the database
        createAuditLog("ALERT_RESOLVED", "SystemAlert", request.getAlertId(),
                "Resolved alert: " + request.getResolution(), "admin");

        return AdminDto.SystemAlertResponse.builder()
                .id(request.getAlertId())
                .type("PERFORMANCE")
                .severity("MEDIUM")
                .title("High Memory Usage")
                .message("System memory usage is above 80%")
                .source("System Monitor")
                .isResolved(true)
                .resolvedBy("admin")
                .resolution(request.getResolution())
                .createdAt(LocalDateTime.now().minusHours(1))
                .resolvedAt(LocalDateTime.now())
                .build();
    }

    public AdminDto.SystemBackupResponse initiateSystemBackup(String backupType) {
        log.info("Admin initiating system backup: {}", backupType);

        String backupId = "backup_" + System.currentTimeMillis();

        createAuditLog("SYSTEM_BACKUP_INITIATED", "SystemBackup", null,
                "Initiated " + backupType + " backup", "admin");

        return AdminDto.SystemBackupResponse.builder()
                .backupId(backupId)
                .type(backupType)
                .status("PENDING")
                .sizeInBytes(0L)
                .location("/backups/" + backupId)
                .initiatedBy("admin")
                .initiatedAt(LocalDateTime.now())
                .build();
    }

    private void createAuditLog(String action, String entityType, Long entityId,
                               String details, String adminUsername) {
        // In real implementation, this would save to an audit log table
        log.info("Audit Log - Action: {}, Entity: {} ({}), Details: {}, Admin: {}",
                action, entityType, entityId, details, adminUsername);
    }
}
