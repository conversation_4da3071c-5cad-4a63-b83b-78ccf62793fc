package zw.co.kanjan.logipool.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import zw.co.kanjan.logipool.entity.Company;
import zw.co.kanjan.logipool.entity.TruckHead;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface TruckHeadRepository extends JpaRepository<TruckHead, Long> {
    
    Optional<TruckHead> findByRegistrationNumber(String registrationNumber);
    
    List<TruckHead> findByCompany(Company company);
    
    Page<TruckHead> findByCompany(Company company, Pageable pageable);
    
    List<TruckHead> findByCompanyAndStatus(Company company, TruckHead.TruckStatus status);
    
    Page<TruckHead> findByCompanyAndStatus(Company company, TruckHead.TruckStatus status, Pageable pageable);
    
    @Query("SELECT t FROM TruckHead t WHERE t.company = :company AND " +
           "(:status IS NULL OR t.status = :status) AND " +
           "(:make IS NULL OR LOWER(t.make) LIKE LOWER(CONCAT('%', :make, '%'))) AND " +
           "(:model IS NULL OR LOWER(t.model) LIKE LOWER(CONCAT('%', :model, '%'))) AND " +
           "(:registrationNumber IS NULL OR LOWER(t.registrationNumber) LIKE LOWER(CONCAT('%', :registrationNumber, '%')))")
    Page<TruckHead> findByCompanyWithFilters(
            @Param("company") Company company,
            @Param("status") TruckHead.TruckStatus status,
            @Param("make") String make,
            @Param("model") String model,
            @Param("registrationNumber") String registrationNumber,
            Pageable pageable);
    
    @Query("SELECT t FROM TruckHead t WHERE t.company = :company AND t.status = 'AVAILABLE'")
    List<TruckHead> findAvailableByCompany(@Param("company") Company company);
    
    @Query("SELECT t FROM TruckHead t WHERE t.insuranceExpiryDate IS NOT NULL AND " +
           "t.insuranceExpiryDate BETWEEN :startDate AND :endDate")
    List<TruckHead> findByInsuranceExpiryDateBetween(
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT t FROM TruckHead t WHERE t.fitnessExpiryDate IS NOT NULL AND " +
           "t.fitnessExpiryDate BETWEEN :startDate AND :endDate")
    List<TruckHead> findByFitnessExpiryDateBetween(
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT t FROM TruckHead t WHERE t.roadPermitExpiryDate IS NOT NULL AND " +
           "t.roadPermitExpiryDate BETWEEN :startDate AND :endDate")
    List<TruckHead> findByRoadPermitExpiryDateBetween(
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT t FROM TruckHead t WHERE t.taxClearanceExpiryDate IS NOT NULL AND " +
           "t.taxClearanceExpiryDate BETWEEN :startDate AND :endDate")
    List<TruckHead> findByTaxClearanceExpiryDateBetween(
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT COUNT(t) FROM TruckHead t WHERE t.company = :company")
    long countByCompany(@Param("company") Company company);
    
    @Query("SELECT COUNT(t) FROM TruckHead t WHERE t.company = :company AND t.status = :status")
    long countByCompanyAndStatus(@Param("company") Company company, @Param("status") TruckHead.TruckStatus status);
    
    boolean existsByRegistrationNumber(String registrationNumber);
}
