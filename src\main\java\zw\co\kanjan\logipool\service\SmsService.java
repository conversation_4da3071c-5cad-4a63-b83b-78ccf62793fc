package zw.co.kanjan.logipool.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class SmsService {
    
    private final RestTemplate restTemplate;
    
    @Value("${app.sms.provider:twilio}")
    private String smsProvider;
    
    @Value("${app.sms.twilio.account-sid:}")
    private String twilioAccountSid;
    
    @Value("${app.sms.twilio.auth-token:}")
    private String twilioAuthToken;
    
    @Value("${app.sms.twilio.from-number:}")
    private String twilioFromNumber;
    
    @Value("${app.sms.enabled:false}")
    private boolean smsEnabled;
    
    public void sendSms(String to, String message) {
        if (!smsEnabled) {
            log.debug("SMS service is disabled. Would send SMS to {}: {}", to, message);
            return;
        }
        
        try {
            switch (smsProvider.toLowerCase()) {
                case "twilio":
                    sendTwilioSms(to, message);
                    break;
                case "mock":
                    sendMockSms(to, message);
                    break;
                default:
                    log.warn("Unknown SMS provider: {}. Using mock provider.", smsProvider);
                    sendMockSms(to, message);
            }
        } catch (Exception e) {
            log.error("Failed to send SMS to {}: {}", to, message, e);
        }
    }
    
    private void sendTwilioSms(String to, String message) {
        if (twilioAccountSid.isEmpty() || twilioAuthToken.isEmpty() || twilioFromNumber.isEmpty()) {
            log.warn("Twilio credentials not configured. Using mock SMS service.");
            sendMockSms(to, message);
            return;
        }
        
        try {
            String url = String.format("https://api.twilio.com/2010-04-01/Accounts/%s/Messages.json", twilioAccountSid);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            headers.setBasicAuth(twilioAccountSid, twilioAuthToken);
            
            Map<String, String> body = new HashMap<>();
            body.put("From", twilioFromNumber);
            body.put("To", to);
            body.put("Body", message);
            
            StringBuilder formData = new StringBuilder();
            for (Map.Entry<String, String> entry : body.entrySet()) {
                if (formData.length() > 0) {
                    formData.append("&");
                }
                formData.append(entry.getKey()).append("=").append(entry.getValue());
            }
            
            HttpEntity<String> request = new HttpEntity<>(formData.toString(), headers);
            
            ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("SMS sent successfully to: {}", to);
            } else {
                log.error("Failed to send SMS. Status: {}, Response: {}", 
                    response.getStatusCode(), response.getBody());
            }
            
        } catch (Exception e) {
            log.error("Error sending Twilio SMS to {}: {}", to, message, e);
            throw e;
        }
    }
    
    private void sendMockSms(String to, String message) {
        log.info("MOCK SMS - To: {}, Message: {}", to, message);
        // In a real implementation, this could write to a file or database for testing
    }
    
    public void sendLoadPostedSms(String phoneNumber, String loadTitle, String location) {
        String message = String.format(
            "LogiPool: New load '%s' available near %s. Check the app to place your bid!",
            truncateText(loadTitle, 30),
            truncateText(location, 20)
        );
        sendSms(phoneNumber, message);
    }
    
    public void sendBidReceivedSms(String phoneNumber, String companyName, String bidAmount) {
        String message = String.format(
            "LogiPool: New bid of $%s from %s. Check the app to review and respond.",
            bidAmount,
            truncateText(companyName, 20)
        );
        sendSms(phoneNumber, message);
    }
    
    public void sendBidAcceptedSms(String phoneNumber, String loadTitle) {
        String message = String.format(
            "LogiPool: Congratulations! Your bid for '%s' has been accepted. Check the app for details.",
            truncateText(loadTitle, 40)
        );
        sendSms(phoneNumber, message);
    }
    
    public void sendBidRejectedSms(String phoneNumber, String loadTitle) {
        String message = String.format(
            "LogiPool: Your bid for '%s' was not selected. Keep bidding for more opportunities!",
            truncateText(loadTitle, 40)
        );
        sendSms(phoneNumber, message);
    }
    
    public void sendLoadStatusUpdateSms(String phoneNumber, String loadTitle, String status) {
        String message = String.format(
            "LogiPool: Load '%s' status updated to %s. Check the app for details.",
            truncateText(loadTitle, 30),
            status
        );
        sendSms(phoneNumber, message);
    }
    
    public void sendUrgentNotificationSms(String phoneNumber, String message) {
        String smsMessage = String.format("LogiPool URGENT: %s", truncateText(message, 140));
        sendSms(phoneNumber, smsMessage);
    }

    public void sendFleetAlertSms(String phoneNumber, String title, String message) {
        String smsMessage = String.format(
            "LogiPool FLEET ALERT: %s - %s. Check app for details.",
            truncateText(title, 30),
            truncateText(message, 80)
        );
        sendSms(phoneNumber, smsMessage);
    }

    private String truncateText(String text, int maxLength) {
        if (text == null) {
            return "";
        }
        if (text.length() <= maxLength) {
            return text;
        }
        return text.substring(0, maxLength - 3) + "...";
    }

    public boolean isSmsEnabled() {
        return smsEnabled;
    }
}
