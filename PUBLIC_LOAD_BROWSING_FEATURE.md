# Public Load Browsing Feature

## Overview

This feature allows guest users (non-authenticated users) to browse posted loads on the LogiPool platform while hiding sensitive information. This provides transparency for potential transporters to see available opportunities without compromising client privacy.

## Features Implemented

### 1. Backend Implementation

#### Public Load DTO
- **Location**: `src/main/java/zw/co/kanjan/logipool/dto/load/PublicLoadResponse.java`
- **Purpose**: Sanitized load response for public viewing
- **Features**:
  - Masks exact locations to show only city/region
  - Removes sensitive payment information
  - Hides client contact details
  - Provides generic descriptions without sensitive data
  - Shows only essential load information (weight, cargo type, dates, etc.)

#### Repository Enhancements
- **Location**: `src/main/java/zw/co/kanjan/logipool/repository/LoadRepository.java`
- **New Methods**:
  - `findPubliclyVisibleLoads()` - Gets loads with POSTED status and open bidding
  - `findPubliclyVisibleLoadsByCargoType()` - Filter by cargo type
  - `findPubliclyVisibleLoadsByLoadType()` - Filter by load type
  - `searchPubliclyVisibleLoads()` - Search across title, cargo type, and locations
  - `countPubliclyVisibleLoads()` - Count available loads for statistics

#### Service Layer
- **Location**: `src/main/java/zw/co/kanjan/logipool/service/PublicBrowsingService.java`
- **New Methods**:
  - `browseLoads()` - Browse loads with filtering and pagination
  - `getPublicLoadDetails()` - Get sanitized details of a specific load
  - `toPublicLoadResponse()` - Convert Load entity to sanitized public response

#### API Endpoints
- **Location**: `src/main/java/zw/co/kanjan/logipool/controller/PublicController.java`
- **New Endpoints**:
  - `GET /api/public/loads` - Browse available loads with filtering
  - `GET /api/public/loads/{id}` - Get details of a specific load
  - `GET /api/public/loads/types` - Get available load types

### 2. Frontend Implementation

#### Public API Service
- **Location**: `logipool_flutter/lib/shared/services/public_api_service.dart`
- **New Methods**:
  - `browseLoads()` - Call backend to browse loads
  - `getLoadDetails()` - Get specific load details
  - `getLoadTypes()` - Get available load types

#### Loads Browse Screen
- **Location**: `logipool_flutter/lib/features/public/screens/loads_browse_screen.dart`
- **Features**:
  - Search functionality across titles, cargo types, and locations
  - Filter by load type and cargo type
  - Responsive grid layout for load cards
  - Infinite scroll pagination
  - Load details dialog with sanitized information
  - Integration with guest load posting

#### Navigation Updates
- **Locations**: 
  - `logipool_flutter/lib/core/utils/app_router.dart`
  - `logipool_flutter/lib/shared/widgets/public_layout.dart`
  - `logipool_flutter/lib/features/public/screens/public_home_screen.dart`
- **Changes**:
  - Added `/loads` route to public routes
  - Added "Loads" navigation item to desktop and mobile menus
  - Added featured loads section to home page

## Security Features

### Data Sanitization
- **Location Masking**: Full addresses are masked to show only city/region
- **Contact Information**: All client contact details are removed
- **Payment Details**: Payment rates and terms are hidden
- **Sensitive Descriptions**: Descriptions are cleaned to remove contact information

### Access Control
- Only loads with `POSTED` status are visible
- Only loads with open bidding (biddingClosesAt > now) are shown
- No authentication required for browsing
- No sensitive business information exposed

## API Documentation

### Browse Loads
```
GET /api/public/loads
Parameters:
- page: Page number (default: 0)
- size: Page size (default: 20)
- sortBy: Sort field (default: createdAt)
- sortDir: Sort direction (default: desc)
- loadType: Filter by load type (optional)
- cargoType: Filter by cargo type (optional)
- search: Search term (optional)

Response: Paginated list of PublicLoadResponse objects
```

### Get Load Details
```
GET /api/public/loads/{id}
Parameters:
- id: Load ID

Response: PublicLoadResponse object with sanitized details
```

### Get Load Types
```
GET /api/public/loads/types
Response: Array of available load types
```

## Frontend Routes

### Public Routes
- `/loads` - Browse available loads page
- Accessible without authentication
- Integrated with existing public layout

### Navigation
- Desktop: Header navigation bar
- Mobile: Hamburger menu drawer
- Home page: Featured loads section with "Browse All Loads" button

## Data Flow

1. **Load Creation**: When clients post loads, they become available for public browsing if status is POSTED
2. **Public Access**: Guest users can browse loads without authentication
3. **Data Sanitization**: All sensitive information is removed before sending to frontend
4. **Filtering**: Users can filter by load type, cargo type, and search terms
5. **Details View**: Users can view sanitized details in a modal dialog
6. **Lead Generation**: "Post Similar Load" button directs to guest load posting form

## Benefits

### For Transporters
- Visibility into available opportunities
- No registration required for browsing
- Ability to see load patterns and demand
- Easy access to post their own loads

### For Clients
- Increased visibility for their loads
- More potential bidders
- Privacy protection through data sanitization
- Professional presentation of their requirements

### For Platform
- Increased user engagement
- Lead generation for new users
- Transparency builds trust
- SEO benefits from public content

## Future Enhancements

1. **Advanced Filtering**: Add filters for pickup/delivery regions, date ranges, weight ranges
2. **Map Integration**: Show pickup/delivery regions on a map
3. **Load Alerts**: Allow users to set up alerts for specific types of loads
4. **Statistics Dashboard**: Show public statistics about load volumes and trends
5. **Mobile App**: Extend functionality to mobile applications
6. **API Rate Limiting**: Implement rate limiting for public endpoints
7. **Caching**: Add caching for frequently accessed load data

## Testing

### Manual Testing Checklist
- [ ] Browse loads without authentication
- [ ] Search functionality works correctly
- [ ] Filtering by load type and cargo type
- [ ] Pagination and infinite scroll
- [ ] Load details modal displays sanitized information
- [ ] Navigation links work correctly
- [ ] Responsive design on different screen sizes
- [ ] Integration with guest load posting

### Security Testing
- [ ] Verify sensitive information is not exposed
- [ ] Test that only POSTED loads with open bidding are visible
- [ ] Confirm location masking works correctly
- [ ] Validate that contact information is removed

## Deployment Notes

### Database Considerations
- No new tables required
- Uses existing Load entity with new repository methods
- Indexes on status and biddingClosesAt recommended for performance

### Performance
- Pagination implemented to handle large datasets
- Repository queries optimized for public access patterns
- Consider adding caching for frequently accessed data

### Monitoring
- Track public load browsing usage
- Monitor API response times
- Alert on high error rates for public endpoints
