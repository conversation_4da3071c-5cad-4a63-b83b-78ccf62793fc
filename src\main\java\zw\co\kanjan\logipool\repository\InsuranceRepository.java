package zw.co.kanjan.logipool.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import zw.co.kanjan.logipool.entity.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface InsuranceRepository extends JpaRepository<Insurance, Long> {
    
    List<Insurance> findByCompany(Company company);
    
    Page<Insurance> findByCompany(Company company, Pageable pageable);
    
    List<Insurance> findByCompanyAndStatus(Company company, Insurance.InsuranceStatus status);
    
    List<Insurance> findByCompanyAndType(Company company, Insurance.InsuranceType type);
    
    Page<Insurance> findByCompanyAndType(Company company, Insurance.InsuranceType type, Pageable pageable);
    
    Optional<Insurance> findByPolicyNumber(String policyNumber);
    
    List<Insurance> findByVehicle(Vehicle vehicle);
    
    List<Insurance> findByTruckHead(TruckHead truckHead);
    
    List<Insurance> findByTrailer(Trailer trailer);
    
    List<Insurance> findByEquipment(Equipment equipment);
    
    @Query("SELECT i FROM Insurance i WHERE i.company = :company AND " +
           "(:status IS NULL OR i.status = :status) AND " +
           "(:type IS NULL OR i.type = :type) AND " +
           "(:provider IS NULL OR LOWER(i.insuranceProvider) LIKE LOWER(CONCAT('%', :provider, '%'))) AND " +
           "(:policyNumber IS NULL OR LOWER(i.policyNumber) LIKE LOWER(CONCAT('%', :policyNumber, '%')))")
    Page<Insurance> findByCompanyWithFilters(
            @Param("company") Company company,
            @Param("status") Insurance.InsuranceStatus status,
            @Param("type") Insurance.InsuranceType type,
            @Param("provider") String provider,
            @Param("policyNumber") String policyNumber,
            Pageable pageable);
    
    @Query("SELECT i FROM Insurance i WHERE i.expiryDate BETWEEN :startDate AND :endDate")
    List<Insurance> findByExpiryDateBetween(
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT i FROM Insurance i WHERE i.company = :company AND " +
           "i.expiryDate BETWEEN :startDate AND :endDate")
    List<Insurance> findByCompanyAndExpiryDateBetween(
            @Param("company") Company company,
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT i FROM Insurance i WHERE i.status = 'ACTIVE' AND i.expiryDate <= :date")
    List<Insurance> findExpiredInsurances(@Param("date") LocalDateTime date);
    
    @Query("SELECT i FROM Insurance i WHERE i.company = :company AND i.status = 'ACTIVE' AND i.expiryDate <= :date")
    List<Insurance> findExpiredInsurancesByCompany(@Param("company") Company company, @Param("date") LocalDateTime date);
    
    @Query("SELECT COUNT(i) FROM Insurance i WHERE i.company = :company")
    long countByCompany(@Param("company") Company company);
    
    @Query("SELECT COUNT(i) FROM Insurance i WHERE i.company = :company AND i.status = :status")
    long countByCompanyAndStatus(@Param("company") Company company, @Param("status") Insurance.InsuranceStatus status);
    
    @Query("SELECT COUNT(i) FROM Insurance i WHERE i.company = :company AND i.type = :type")
    long countByCompanyAndType(@Param("company") Company company, @Param("type") Insurance.InsuranceType type);
    
    boolean existsByPolicyNumber(String policyNumber);
}
