package zw.co.kanjan.logipool.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import zw.co.kanjan.logipool.service.FleetExportService;

import java.security.Principal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@RestController
@RequestMapping("/api/fleet/export")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Fleet Export", description = "APIs for exporting fleet data and generating reports")
public class FleetExportController {
    
    private final FleetExportService fleetExportService;
    
    @GetMapping("/fleet-overview")
    @PreAuthorize("hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Export fleet overview", description = "Export comprehensive fleet overview data to Excel")
    public ResponseEntity<byte[]> exportFleetOverview(Principal principal) {
        
        log.info("Exporting fleet overview for user: {}", principal.getName());
        
        byte[] excelData = fleetExportService.exportFleetData(
                principal.getName(), 
                "fleet_overview", 
                null, 
                null
        );
        
        String filename = String.format("fleet_overview_%s.xlsx", 
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")));
        
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(excelData);
    }
    
    @GetMapping("/compliance-report")
    @PreAuthorize("hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Export compliance report", description = "Export fleet compliance report for a date range")
    public ResponseEntity<byte[]> exportComplianceReport(
            @Parameter(description = "Start date") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @Parameter(description = "End date") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate,
            Principal principal) {
        
        log.info("Exporting compliance report for user: {} from {} to {}", 
                principal.getName(), startDate, endDate);
        
        byte[] excelData = fleetExportService.exportFleetData(
                principal.getName(), 
                "compliance_report", 
                startDate, 
                endDate
        );
        
        String filename = String.format("compliance_report_%s_to_%s.xlsx", 
                startDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")),
                endDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(excelData);
    }
    
    @GetMapping("/utilization-report")
    @PreAuthorize("hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Export utilization report", description = "Export fleet utilization report for a date range")
    public ResponseEntity<byte[]> exportUtilizationReport(
            @Parameter(description = "Start date") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @Parameter(description = "End date") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate,
            Principal principal) {
        
        log.info("Exporting utilization report for user: {} from {} to {}", 
                principal.getName(), startDate, endDate);
        
        byte[] excelData = fleetExportService.exportFleetData(
                principal.getName(), 
                "utilization_report", 
                startDate, 
                endDate
        );
        
        String filename = String.format("utilization_report_%s_to_%s.xlsx", 
                startDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")),
                endDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(excelData);
    }
    
    @GetMapping("/maintenance-report")
    @PreAuthorize("hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Export maintenance report", description = "Export fleet maintenance schedule and history")
    public ResponseEntity<byte[]> exportMaintenanceReport(
            @Parameter(description = "Start date") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @Parameter(description = "End date") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate,
            Principal principal) {
        
        log.info("Exporting maintenance report for user: {} from {} to {}", 
                principal.getName(), startDate, endDate);
        
        byte[] excelData = fleetExportService.exportFleetData(
                principal.getName(), 
                "maintenance_report", 
                startDate, 
                endDate
        );
        
        String filename = String.format("maintenance_report_%s_to_%s.xlsx", 
                startDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")),
                endDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(excelData);
    }
    
    @GetMapping("/complete-fleet-data")
    @PreAuthorize("hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Export complete fleet data", description = "Export all fleet data including vehicles, assignments, compliance, etc.")
    public ResponseEntity<byte[]> exportCompleteFleetData(Principal principal) {
        
        log.info("Exporting complete fleet data for user: {}", principal.getName());
        
        byte[] excelData = fleetExportService.exportFleetData(
                principal.getName(), 
                "complete_fleet_data", 
                null, 
                null
        );
        
        String filename = String.format("complete_fleet_data_%s.xlsx", 
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")));
        
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(excelData);
    }
    
    @GetMapping("/custom-report")
    @PreAuthorize("hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Export custom report", description = "Export custom fleet report based on specified parameters")
    public ResponseEntity<byte[]> exportCustomReport(
            @Parameter(description = "Report type") @RequestParam String reportType,
            @Parameter(description = "Start date (optional)") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @Parameter(description = "End date (optional)") @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate,
            Principal principal) {
        
        log.info("Exporting custom report '{}' for user: {}", reportType, principal.getName());
        
        byte[] excelData = fleetExportService.exportFleetData(
                principal.getName(), 
                reportType, 
                startDate, 
                endDate
        );
        
        String filename = String.format("%s_%s.xlsx", 
                reportType,
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")));
        
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(excelData);
    }
}
