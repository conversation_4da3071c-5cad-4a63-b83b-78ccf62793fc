package zw.co.kanjan.logipool.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import zw.co.kanjan.logipool.dto.driver.DriverDto;
import zw.co.kanjan.logipool.dto.driver.DriverVerificationDto;
import zw.co.kanjan.logipool.entity.*;
import zw.co.kanjan.logipool.exception.BusinessException;
import zw.co.kanjan.logipool.exception.ResourceNotFoundException;
import zw.co.kanjan.logipool.exception.ValidationException;
import zw.co.kanjan.logipool.mapper.DriverMapper;
import zw.co.kanjan.logipool.repository.*;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class DriverService {
    
    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    private final CompanyRepository companyRepository;
    private final CompanyMemberRepository companyMemberRepository;
    private final DriverVerificationRepository driverVerificationRepository;
    private final DriverMapper driverMapper;
    private final PasswordEncoder passwordEncoder;
    
    public DriverDto.DriverResponse registerDriver(DriverDto.DriverRegistrationRequest request, String registrarUsername) {
        log.info("Registering driver with email: {} by user: {}", request.getEmail(), registrarUsername);
        
        // Validate registrar permissions
        User registrar = getUserByUsername(registrarUsername);
        validateRegistrarPermissions(registrar, request.getCompanyId());
        
        // Check if email already exists
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new ValidationException("Email already exists: " + request.getEmail());
        }
        
        // Create driver user
        User driver = createDriverUser(request);
        
        // Create verification tasks
        createVerificationTasks(driver);
        
        // Assign to company if specified
        CompanyMember companyMember = null;
        if (request.getCompanyId() != null) {
            companyMember = assignDriverToCompanyInternal(driver, request.getCompanyId(), registrar);
        }
        
        // Load verifications for response
        List<DriverVerification> verifications = driverVerificationRepository.findByDriverOrderByCreatedAtDesc(driver);
        
        log.info("Driver registered successfully with ID: {}", driver.getId());
        return driverMapper.toFullResponse(driver, companyMember, verifications);
    }
    
    public DriverDto.DriverResponse selfRegisterDriver(DriverDto.DriverRegistrationRequest request) {
        log.info("Driver self-registering with email: {}", request.getEmail());
        
        // Check if email already exists
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new ValidationException("Email already exists: " + request.getEmail());
        }
        
        // Self-registration cannot assign to company directly
        if (request.getCompanyId() != null) {
            throw new ValidationException("Cannot assign to company during self-registration");
        }
        
        // Create driver user
        User driver = createDriverUser(request);
        
        // Create verification tasks
        createVerificationTasks(driver);
        
        // Load verifications for response
        List<DriverVerification> verifications = driverVerificationRepository.findByDriverOrderByCreatedAtDesc(driver);
        
        log.info("Driver self-registered successfully with ID: {}", driver.getId());
        return driverMapper.toFullResponse(driver, null, verifications);
    }
    
    @Transactional(readOnly = true)
    public Page<DriverDto.DriverResponse> getAllDrivers(Pageable pageable, String requesterUsername) {
        User requester = getUserByUsername(requesterUsername);
        
        Page<User> drivers;
        if (isAdmin(requester)) {
            // Admin can see all drivers
            drivers = userRepository.findByRoles_Name(Role.RoleName.DRIVER, pageable);
        } else {
            // Company admins can only see their company's drivers
            Company company = getCompanyByUser(requester);
            drivers = userRepository.findDriversByCompany(company.getId(), pageable);
        }
        
        return drivers.map(driver -> {
            CompanyMember companyMember = getDriverCompanyMembership(driver);
            List<DriverVerification> verifications = driverVerificationRepository.findByDriverOrderByCreatedAtDesc(driver);
            return driverMapper.toFullResponse(driver, companyMember, verifications);
        });
    }
    
    @Transactional(readOnly = true)
    public DriverDto.DriverResponse getDriverById(Long id, String requesterUsername) {
        User requester = getUserByUsername(requesterUsername);
        User driver = getDriverById(id);
        
        // Validate access permissions
        validateDriverAccess(requester, driver);
        
        CompanyMember companyMember = getDriverCompanyMembership(driver);
        List<DriverVerification> verifications = driverVerificationRepository.findByDriverOrderByCreatedAtDesc(driver);
        
        return driverMapper.toFullResponse(driver, companyMember, verifications);
    }
    
    @Transactional(readOnly = true)
    public DriverDto.DriverResponse getCurrentDriverProfile(String driverUsername) {
        User driver = getUserByUsername(driverUsername);
        
        if (!hasDriverRole(driver)) {
            throw new BusinessException("User is not a driver");
        }
        
        CompanyMember companyMember = getDriverCompanyMembership(driver);
        List<DriverVerification> verifications = driverVerificationRepository.findByDriverOrderByCreatedAtDesc(driver);
        
        return driverMapper.toFullResponse(driver, companyMember, verifications);
    }
    
    public DriverDto.DriverResponse updateDriver(Long id, DriverDto.DriverUpdateRequest request, String updaterUsername) {
        User updater = getUserByUsername(updaterUsername);
        User driver = getDriverById(id);
        
        // Validate update permissions
        validateDriverUpdateAccess(updater, driver);
        
        // Update driver fields
        updateDriverFields(driver, request);
        User savedDriver = userRepository.save(driver);
        
        CompanyMember companyMember = getDriverCompanyMembership(savedDriver);
        List<DriverVerification> verifications = driverVerificationRepository.findByDriverOrderByCreatedAtDesc(savedDriver);
        
        log.info("Driver {} updated successfully by user: {}", id, updaterUsername);
        return driverMapper.toFullResponse(savedDriver, companyMember, verifications);
    }
    
    public DriverDto.DriverResponse updateCurrentDriverProfile(DriverDto.DriverUpdateRequest request, String driverUsername) {
        User driver = getUserByUsername(driverUsername);
        
        if (!hasDriverRole(driver)) {
            throw new BusinessException("User is not a driver");
        }
        
        // Update driver fields
        updateDriverFields(driver, request);
        User savedDriver = userRepository.save(driver);
        
        CompanyMember companyMember = getDriverCompanyMembership(savedDriver);
        List<DriverVerification> verifications = driverVerificationRepository.findByDriverOrderByCreatedAtDesc(savedDriver);
        
        log.info("Driver profile updated successfully by driver: {}", driverUsername);
        return driverMapper.toFullResponse(savedDriver, companyMember, verifications);
    }
    
    public DriverDto.DriverResponse assignDriverToCompany(DriverDto.AssignToCompanyRequest request, String assignerUsername) {
        User assigner = getUserByUsername(assignerUsername);
        User driver = getDriverById(request.getDriverId());
        Company company = getCompanyById(request.getCompanyId());
        
        // Validate assignment permissions
        validateCompanyAssignmentPermissions(assigner, company);
        
        // Check if driver is already assigned to this company
        Optional<CompanyMember> existingMembership = companyMemberRepository
                .findByUserAndCompanyAndStatus(driver, company, CompanyMember.MemberStatus.ACTIVE);
        
        if (existingMembership.isPresent()) {
            throw new ValidationException("Driver is already assigned to this company");
        }
        
        // Create company membership
        CompanyMember companyMember = assignDriverToCompanyInternal(driver, company.getId(), assigner);
        List<DriverVerification> verifications = driverVerificationRepository.findByDriverOrderByCreatedAtDesc(driver);
        
        log.info("Driver {} assigned to company {} successfully", request.getDriverId(), request.getCompanyId());
        return driverMapper.toFullResponse(driver, companyMember, verifications);
    }
    
    public void removeDriverFromCompany(Long driverId, String removerUsername) {
        User remover = getUserByUsername(removerUsername);
        User driver = getDriverById(driverId);
        
        CompanyMember companyMember = getDriverCompanyMembership(driver);
        if (companyMember == null) {
            throw new BusinessException("Driver is not assigned to any company");
        }
        
        // Validate removal permissions
        validateCompanyAssignmentPermissions(remover, companyMember.getCompany());
        
        // Remove company membership
        companyMemberRepository.delete(companyMember);
        
        log.info("Driver {} removed from company successfully", driverId);
    }
    
    @Transactional(readOnly = true)
    public Page<DriverDto.DriverResponse> getDriversByCompany(Long companyId, Pageable pageable, String requesterUsername) {
        User requester = getUserByUsername(requesterUsername);
        Company company = getCompanyById(companyId);
        
        // Validate access permissions
        validateCompanyAccess(requester, company);
        
        Page<User> drivers = userRepository.findDriversByCompany(companyId, pageable);
        
        return drivers.map(driver -> {
            CompanyMember companyMember = getDriverCompanyMembership(driver);
            List<DriverVerification> verifications = driverVerificationRepository.findByDriverOrderByCreatedAtDesc(driver);
            return driverMapper.toFullResponse(driver, companyMember, verifications);
        });
    }
    
    @Transactional(readOnly = true)
    public Page<DriverDto.DriverResponse> getUnassignedDrivers(Pageable pageable, String requesterUsername) {
        User requester = getUserByUsername(requesterUsername);
        
        if (!isAdmin(requester)) {
            throw new BusinessException("Only admins can view unassigned drivers");
        }
        
        Page<User> drivers = userRepository.findUnassignedDrivers(pageable);
        
        return drivers.map(driver -> {
            List<DriverVerification> verifications = driverVerificationRepository.findByDriverOrderByCreatedAtDesc(driver);
            return driverMapper.toFullResponse(driver, null, verifications);
        });
    }
    
    public void deleteDriver(Long driverId, String deleterUsername) {
        User deleter = getUserByUsername(deleterUsername);
        
        if (!isAdmin(deleter)) {
            throw new BusinessException("Only admins can delete drivers");
        }
        
        User driver = getDriverById(driverId);
        
        // Delete related data
        driverVerificationRepository.deleteByDriver(driver);
        
        // Remove from company if assigned
        CompanyMember companyMember = getDriverCompanyMembership(driver);
        if (companyMember != null) {
            companyMemberRepository.delete(companyMember);
        }
        
        // Delete user
        userRepository.delete(driver);
        
        log.info("Driver {} deleted successfully by admin: {}", driverId, deleterUsername);
    }
    
    // Verification methods

    @Transactional(readOnly = true)
    public DriverVerificationDto.VerificationSummaryResponse getDriverVerificationStatus(Long driverId, String requesterUsername) {
        User requester = getUserByUsername(requesterUsername);
        User driver = getDriverById(driverId);

        // Validate access permissions
        validateDriverAccess(requester, driver);

        List<DriverVerification> verifications = driverVerificationRepository.findByDriverOrderByCreatedAtDesc(driver);

        return driverMapper.toVerificationSummaryResponse(driver, verifications);
    }

    public DriverVerificationDto.VerificationSummaryResponse updateDriverVerification(
            DriverVerificationDto.UpdateVerificationRequest request, String verifierUsername) {

        User verifier = getUserByUsername(verifierUsername);
        User driver = getDriverById(request.getDriverId());

        // Validate verifier permissions
        validateVerificationPermissions(verifier, driver);

        // Find or create verification record
        DriverVerification verification = driverVerificationRepository
                .findByDriverAndVerificationType(driver, request.getVerificationType())
                .orElseThrow(() -> new ResourceNotFoundException("Verification task not found"));

        // Update verification
        verification.setStatus(request.getStatus());
        verification.setNotes(request.getNotes());
        verification.setVerifiedBy(verifier);
        verification.setVerifiedAt(request.getStatus() == DriverDto.VerificationStatus.VERIFIED ?
                LocalDateTime.now() : null);

        driverVerificationRepository.save(verification);

        // Return updated summary
        List<DriverVerification> allVerifications = driverVerificationRepository.findByDriverOrderByCreatedAtDesc(driver);

        log.info("Verification {} updated for driver {} by verifier: {}",
                request.getVerificationType(), request.getDriverId(), verifierUsername);

        return driverMapper.toVerificationSummaryResponse(driver, allVerifications);
    }

    @Transactional(readOnly = true)
    public DriverVerificationDto.VerificationStatsResponse getVerificationStats(String requesterUsername) {
        User requester = getUserByUsername(requesterUsername);

        // Calculate statistics based on user permissions
        if (isAdmin(requester)) {
            return calculateGlobalVerificationStats();
        } else {
            Company company = getCompanyByUser(requester);
            return calculateCompanyVerificationStats(company);
        }
    }

    private DriverVerificationDto.VerificationStatsResponse calculateGlobalVerificationStats() {
        long totalDrivers = userRepository.countByRoles_Name(Role.RoleName.DRIVER);

        // Get status counts
        List<Object[]> statusCounts = driverVerificationRepository.getVerificationStatusCounts();
        Map<DriverDto.VerificationStatus, Long> statusMap = statusCounts.stream()
                .collect(Collectors.toMap(
                        row -> (DriverDto.VerificationStatus) row[0],
                        row -> (Long) row[1]
                ));

        // Calculate verification type statistics
        List<Object[]> typeStatusCounts = driverVerificationRepository.getVerificationTypeStatusCounts();
        List<DriverVerificationDto.VerificationTypeStats> typeStats = calculateVerificationTypeStats(typeStatusCounts);

        // Calculate overall completion rate
        long verifiedCount = statusMap.getOrDefault(DriverDto.VerificationStatus.VERIFIED, 0L);
        long totalTasks = statusCounts.stream().mapToLong(row -> (Long) row[1]).sum();
        double completionRate = totalTasks > 0 ? (double) verifiedCount / totalTasks * 100 : 0.0;

        return DriverVerificationDto.VerificationStatsResponse.builder()
                .totalDrivers(totalDrivers)
                .fullyVerifiedDrivers(0L) // Will be calculated separately if needed
                .partiallyVerifiedDrivers(0L)
                .unverifiedDrivers(0L)
                .expiredVerificationDrivers(statusMap.getOrDefault(DriverDto.VerificationStatus.EXPIRED, 0L))
                .verificationCompletionRate(completionRate)
                .verificationTypeStats(typeStats)
                .build();
    }

    private DriverVerificationDto.VerificationStatsResponse calculateCompanyVerificationStats(Company company) {
        // Implementation for company-specific stats
        // This would be similar to global stats but filtered by company
        return calculateGlobalVerificationStats(); // Simplified for now
    }

    private List<DriverVerificationDto.VerificationTypeStats> calculateVerificationTypeStats(List<Object[]> typeStatusCounts) {
        Map<DriverVerificationDto.VerificationType, Map<DriverDto.VerificationStatus, Long>> typeStatusMap = new HashMap<>();

        for (Object[] row : typeStatusCounts) {
            DriverVerificationDto.VerificationType type = (DriverVerificationDto.VerificationType) row[0];
            DriverDto.VerificationStatus status = (DriverDto.VerificationStatus) row[1];
            Long count = (Long) row[2];

            typeStatusMap.computeIfAbsent(type, k -> new HashMap<>()).put(status, count);
        }

        return typeStatusMap.entrySet().stream()
                .map(entry -> {
                    DriverVerificationDto.VerificationType type = entry.getKey();
                    Map<DriverDto.VerificationStatus, Long> statusCounts = entry.getValue();

                    long verified = statusCounts.getOrDefault(DriverDto.VerificationStatus.VERIFIED, 0L);
                    long pending = statusCounts.getOrDefault(DriverDto.VerificationStatus.PENDING, 0L);
                    long rejected = statusCounts.getOrDefault(DriverDto.VerificationStatus.REJECTED, 0L);
                    long expired = statusCounts.getOrDefault(DriverDto.VerificationStatus.EXPIRED, 0L);
                    long total = verified + pending + rejected + expired;

                    double completionRate = total > 0 ? (double) verified / total * 100 : 0.0;

                    return DriverVerificationDto.VerificationTypeStats.builder()
                            .verificationType(type)
                            .taskName(type.getTaskName())
                            .verifiedCount(verified)
                            .pendingCount(pending)
                            .rejectedCount(rejected)
                            .expiredCount(expired)
                            .completionRate(completionRate)
                            .build();
                })
                .collect(Collectors.toList());
    }

    // Helper methods
    private User createDriverUser(DriverDto.DriverRegistrationRequest request) {
        Role driverRole = roleRepository.findByName(Role.RoleName.DRIVER)
                .orElseThrow(() -> new RuntimeException("Driver role not found"));
        
        User driver = User.builder()
                .username(request.getEmail()) // Use email as username
                .email(request.getEmail())
                .firstName(request.getFirstName())
                .lastName(request.getLastName())
                .phoneNumber(request.getPhoneNumber())
                .password(passwordEncoder.encode(request.getPassword()))
                .roles(Set.of(driverRole))
                .build();
        
        return userRepository.save(driver);
    }
    
    private void createVerificationTasks(User driver) {
        List<DriverVerification> verifications = new ArrayList<>();
        
        for (DriverVerificationDto.VerificationType type : DriverVerificationDto.VerificationType.values()) {
            DriverVerification verification = DriverVerification.builder()
                    .driver(driver)
                    .verificationType(type)
                    .status(DriverDto.VerificationStatus.PENDING)
                    .build();
            verifications.add(verification);
        }
        
        driverVerificationRepository.saveAll(verifications);
    }
    
    private CompanyMember assignDriverToCompanyInternal(User driver, Long companyId, User assigner) {
        Company company = getCompanyById(companyId);
        
        CompanyMember companyMember = CompanyMember.builder()
                .user(driver)
                .company(company)
                .role(CompanyMember.CompanyRole.DRIVER)
                .status(CompanyMember.MemberStatus.ACTIVE)
                .canUpdateLoadStatus(true)
                .canTrackLocation(true)
                .invitedBy(assigner)
                .joinedAt(LocalDateTime.now())
                .build();
        
        return companyMemberRepository.save(companyMember);
    }
    
    private void updateDriverFields(User driver, DriverDto.DriverUpdateRequest request) {
        if (request.getFirstName() != null) {
            driver.setFirstName(request.getFirstName());
        }
        if (request.getLastName() != null) {
            driver.setLastName(request.getLastName());
        }
        if (request.getPhoneNumber() != null) {
            driver.setPhoneNumber(request.getPhoneNumber());
        }
        // Note: License and ID fields would be stored in a separate DriverProfile entity
        // For now, we'll just update the basic user fields
    }

    private User getUserByUsername(String username) {
        return userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found: " + username));
    }

    private User getDriverById(Long id) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Driver not found with id: " + id));

        if (!hasDriverRole(user)) {
            throw new BusinessException("User is not a driver");
        }

        return user;
    }

    private Company getCompanyById(Long id) {
        return companyRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Company not found with id: " + id));
    }

    private Company getCompanyByUser(User user) {
        return companyRepository.findByUser(user)
                .orElseThrow(() -> new BusinessException("Company not found for user"));
    }

    private CompanyMember getDriverCompanyMembership(User driver) {
        return companyMemberRepository.findActiveByUser(driver).orElse(null);
    }

    private boolean hasDriverRole(User user) {
        return user.getRoles().stream()
                .anyMatch(role -> role.getName() == Role.RoleName.DRIVER);
    }

    private boolean isAdmin(User user) {
        return user.getRoles().stream()
                .anyMatch(role -> role.getName() == Role.RoleName.ADMIN);
    }

    private boolean isCompanyOwnerOrAdmin(User user) {
        return user.getRoles().stream()
                .anyMatch(role -> role.getName() == Role.RoleName.ADMIN) ||
               companyMemberRepository.findActiveByUser(user)
                       .map(member -> member.getRole() == CompanyMember.CompanyRole.OWNER ||
                                     member.getRole() == CompanyMember.CompanyRole.MANAGER)
                       .orElse(false);
    }

    private void validateRegistrarPermissions(User registrar, Long companyId) {
        if (isAdmin(registrar)) {
            return; // Admins can register drivers for any company
        }

        if (companyId != null) {
            Company company = getCompanyById(companyId);
            validateCompanyAssignmentPermissions(registrar, company);
        } else {
            throw new BusinessException("Non-admin users must specify a company when registering drivers");
        }
    }

    private void validateDriverAccess(User requester, User driver) {
        if (isAdmin(requester)) {
            return; // Admins can access any driver
        }

        if (requester.equals(driver)) {
            return; // Drivers can access their own profile
        }

        // Check if requester is from the same company as the driver
        CompanyMember requesterMembership = getDriverCompanyMembership(requester);
        CompanyMember driverMembership = getDriverCompanyMembership(driver);

        if (requesterMembership != null && driverMembership != null &&
            requesterMembership.getCompany().equals(driverMembership.getCompany()) &&
            (requesterMembership.getRole() == CompanyMember.CompanyRole.OWNER ||
             requesterMembership.getRole() == CompanyMember.CompanyRole.MANAGER)) {
            return;
        }

        throw new BusinessException("Access denied to driver profile");
    }

    private void validateDriverUpdateAccess(User updater, User driver) {
        if (isAdmin(updater)) {
            return; // Admins can update any driver
        }

        if (updater.equals(driver)) {
            return; // Drivers can update their own profile
        }

        // Check if updater is a company admin for the driver's company
        CompanyMember updaterMembership = getDriverCompanyMembership(updater);
        CompanyMember driverMembership = getDriverCompanyMembership(driver);

        if (updaterMembership != null && driverMembership != null &&
            updaterMembership.getCompany().equals(driverMembership.getCompany()) &&
            (updaterMembership.getRole() == CompanyMember.CompanyRole.OWNER ||
             updaterMembership.getRole() == CompanyMember.CompanyRole.MANAGER)) {
            return;
        }

        throw new BusinessException("Access denied to update driver profile");
    }

    private void validateCompanyAssignmentPermissions(User assigner, Company company) {
        if (isAdmin(assigner)) {
            return; // Admins can assign drivers to any company
        }

        CompanyMember assignerMembership = companyMemberRepository
                .findByUserAndCompanyAndStatus(assigner, company, CompanyMember.MemberStatus.ACTIVE)
                .orElseThrow(() -> new BusinessException("Access denied to assign drivers to this company"));

        if (assignerMembership.getRole() != CompanyMember.CompanyRole.OWNER &&
            assignerMembership.getRole() != CompanyMember.CompanyRole.MANAGER) {
            throw new BusinessException("Insufficient permissions to assign drivers to company");
        }
    }

    private void validateCompanyAccess(User requester, Company company) {
        if (isAdmin(requester)) {
            return; // Admins can access any company
        }

        CompanyMember membership = companyMemberRepository
                .findByUserAndCompanyAndStatus(requester, company, CompanyMember.MemberStatus.ACTIVE)
                .orElseThrow(() -> new BusinessException("Access denied to company data"));

        if (membership.getRole() != CompanyMember.CompanyRole.OWNER &&
            membership.getRole() != CompanyMember.CompanyRole.MANAGER) {
            throw new BusinessException("Insufficient permissions to access company drivers");
        }
    }

    private void validateVerificationPermissions(User verifier, User driver) {
        if (isAdmin(verifier)) {
            return; // Admins can verify any driver
        }

        // Check if verifier is a company admin for the driver's company
        CompanyMember driverMembership = getDriverCompanyMembership(driver);
        if (driverMembership == null) {
            throw new BusinessException("Driver is not assigned to any company");
        }

        CompanyMember verifierMembership = companyMemberRepository
                .findByUserAndCompanyAndStatus(verifier, driverMembership.getCompany(), CompanyMember.MemberStatus.ACTIVE)
                .orElseThrow(() -> new BusinessException("Access denied to verify this driver"));

        if (verifierMembership.getRole() != CompanyMember.CompanyRole.OWNER &&
            verifierMembership.getRole() != CompanyMember.CompanyRole.MANAGER) {
            throw new BusinessException("Insufficient permissions to verify drivers");
        }
    }
}
