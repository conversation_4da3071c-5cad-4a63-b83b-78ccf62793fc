package zw.co.kanjan.logipool.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import zw.co.kanjan.logipool.dto.fleet.FleetDashboardDto;
import zw.co.kanjan.logipool.service.FleetDashboardService;

import java.security.Principal;
import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/api/fleet/dashboard")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Fleet Dashboard", description = "APIs for fleet management dashboard and analytics")
public class FleetDashboardController {
    
    private final FleetDashboardService fleetDashboardService;
    
    @GetMapping("/overview")
    @PreAuthorize("hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Get fleet overview", description = "Get comprehensive fleet statistics and overview")
    public ResponseEntity<FleetDashboardDto.FleetOverviewResponse> getFleetOverview(Principal principal) {
        
        FleetDashboardDto.FleetOverviewResponse response = fleetDashboardService.getFleetOverview(principal.getName());
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/compliance-alerts")
    @PreAuthorize("hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Get compliance alerts", description = "Get list of compliance alerts for expiring documents")
    public ResponseEntity<List<FleetDashboardDto.ComplianceAlertResponse>> getComplianceAlerts(Principal principal) {
        
        List<FleetDashboardDto.ComplianceAlertResponse> response = fleetDashboardService.getComplianceAlerts(principal.getName());
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/utilization")
    @PreAuthorize("hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Get fleet utilization", description = "Get fleet utilization statistics for a date range")
    public ResponseEntity<List<FleetDashboardDto.FleetUtilizationResponse>> getFleetUtilization(
            @Parameter(description = "Start date") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @Parameter(description = "End date") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate,
            Principal principal) {
        
        List<FleetDashboardDto.FleetUtilizationResponse> response = 
                fleetDashboardService.getFleetUtilization(principal.getName(), startDate, endDate);
        return ResponseEntity.ok(response);
    }
}
