package zw.co.kanjan.logipool.dto.driver;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

public class DriverVerificationDto {
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Request to update driver verification status")
    public static class UpdateVerificationRequest {
        
        @NotNull(message = "Driver ID is required")
        @Schema(description = "Driver's user ID", example = "1")
        private Long driverId;
        
        @NotNull(message = "Verification type is required")
        @Schema(description = "Type of verification to update", example = "LICENSE")
        private VerificationType verificationType;
        
        @NotNull(message = "Verification status is required")
        @Schema(description = "New verification status", example = "VERIFIED")
        private DriverDto.VerificationStatus status;
        
        @Size(max = 500, message = "Notes should not exceed 500 characters")
        @Schema(description = "Verification notes or comments")
        private String notes;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Driver verification task details")
    public static class VerificationTaskResponse {
        
        @Schema(description = "Task ID", example = "1")
        private Long id;
        
        @Schema(description = "Driver ID", example = "1")
        private Long driverId;
        
        @Schema(description = "Driver name", example = "John Doe")
        private String driverName;
        
        @Schema(description = "Verification type", example = "LICENSE")
        private VerificationType verificationType;
        
        @Schema(description = "Task name", example = "Driver License")
        private String taskName;
        
        @Schema(description = "Task description")
        private String taskDescription;
        
        @Schema(description = "Current verification status", example = "PENDING")
        private DriverDto.VerificationStatus status;
        
        @Schema(description = "Verification notes or comments")
        private String notes;
        
        @Schema(description = "Verified by admin user ID")
        private Long verifiedBy;
        
        @Schema(description = "Admin name who verified")
        private String verifiedByName;
        
        @Schema(description = "Verification date")
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime verifiedAt;
        
        @Schema(description = "Task creation date")
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime createdAt;
        
        @Schema(description = "Task last update date")
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime updatedAt;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Driver verification summary")
    public static class VerificationSummaryResponse {
        
        @Schema(description = "Driver ID", example = "1")
        private Long driverId;
        
        @Schema(description = "Driver name", example = "John Doe")
        private String driverName;
        
        @Schema(description = "Driver email", example = "<EMAIL>")
        private String driverEmail;
        
        @Schema(description = "Overall verification status")
        private DriverDto.OverallVerificationStatus overallStatus;
        
        @Schema(description = "List of verification tasks")
        private List<VerificationTaskResponse> verificationTasks;
        
        @Schema(description = "Total number of verification tasks")
        private Integer totalTasks;
        
        @Schema(description = "Number of completed verification tasks")
        private Integer completedTasks;
        
        @Schema(description = "Number of pending verification tasks")
        private Integer pendingTasks;
        
        @Schema(description = "Number of rejected verification tasks")
        private Integer rejectedTasks;
        
        @Schema(description = "Verification completion percentage")
        private Double completionPercentage;
        
        @Schema(description = "Last verification update date")
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime lastUpdated;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Bulk verification update request")
    public static class BulkVerificationUpdateRequest {
        
        @Schema(description = "List of verification updates")
        private List<UpdateVerificationRequest> verificationUpdates;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Verification statistics")
    public static class VerificationStatsResponse {
        
        @Schema(description = "Total number of drivers")
        private Long totalDrivers;
        
        @Schema(description = "Number of fully verified drivers")
        private Long fullyVerifiedDrivers;
        
        @Schema(description = "Number of partially verified drivers")
        private Long partiallyVerifiedDrivers;
        
        @Schema(description = "Number of unverified drivers")
        private Long unverifiedDrivers;
        
        @Schema(description = "Number of drivers with expired verifications")
        private Long expiredVerificationDrivers;
        
        @Schema(description = "Verification completion rate percentage")
        private Double verificationCompletionRate;
        
        @Schema(description = "Statistics by verification type")
        private List<VerificationTypeStats> verificationTypeStats;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Statistics for a specific verification type")
    public static class VerificationTypeStats {
        
        @Schema(description = "Verification type", example = "LICENSE")
        private VerificationType verificationType;
        
        @Schema(description = "Task name", example = "Driver License")
        private String taskName;
        
        @Schema(description = "Number of verified tasks")
        private Long verifiedCount;
        
        @Schema(description = "Number of pending tasks")
        private Long pendingCount;
        
        @Schema(description = "Number of rejected tasks")
        private Long rejectedCount;
        
        @Schema(description = "Number of expired tasks")
        private Long expiredCount;
        
        @Schema(description = "Completion rate percentage")
        private Double completionRate;
    }
    
    public enum VerificationType {
        LICENSE("Driver License", "Company admin to Confirm license validity, class, and expiration date against authority database."),
        DEFENSIVE_TRAINING("Defensive Training", "Company admin to Validate driver has attended certified defensive driving courses."),
        NATIONAL_ID("National ID", "Company admin to Cross-check ID number format and existence against national registry."),
        PASSPORT("Passport", "Optional: Company admin to Verify passport for cross-border drivers where required."),
        PHONE_NUMBER("Phone Number", "Company admin to Ensure driver phone is registered and can be used for GPS or OTP flows.");
        
        private final String taskName;
        private final String description;
        
        VerificationType(String taskName, String description) {
            this.taskName = taskName;
            this.description = description;
        }
        
        public String getTaskName() {
            return taskName;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
