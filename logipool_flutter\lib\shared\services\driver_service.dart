import 'package:dio/dio.dart';
import '../models/driver_model.dart';
import '../models/api_response.dart';
import '../models/pagination_model.dart';
import 'api_client.dart';

class DriverService {
  final ApiClient _apiClient;

  DriverService(this._apiClient);

  /// Register a new driver
  Future<DriverModel> registerDriver(DriverRegistrationRequest request) async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>(
        '/drivers/register',
        data: request.toJson(),
      );
      return DriverModel.fromJson(response.data!);
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// Driver self-registration
  Future<DriverModel> selfRegisterDriver(DriverRegistrationRequest request) async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>(
        '/drivers/self-register',
        data: request.toJson(),
      );
      return DriverModel.fromJson(response.data!);
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// Get all drivers (paginated)
  Future<PaginatedResponse<DriverModel>> getAllDrivers({
    int page = 0,
    int size = 20,
    String? sort,
  }) async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>(
        '/drivers',
        queryParameters: {
          'page': page,
          'size': size,
          if (sort != null) 'sort': sort,
        },
      );
      return PaginatedResponse.fromJson(
        response.data!,
        (json) => DriverModel.fromJson(json as Map<String, dynamic>),
      );
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// Get driver by ID
  Future<DriverModel> getDriverById(int id) async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>(
        '/drivers/$id',
      );
      return DriverModel.fromJson(response.data!);
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// Get current driver profile
  Future<DriverModel> getCurrentDriverProfile() async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>(
        '/drivers/profile',
      );
      return DriverModel.fromJson(response.data!);
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// Update driver profile
  Future<DriverModel> updateDriver(int id, DriverUpdateRequest request) async {
    try {
      final response = await _apiClient.put<Map<String, dynamic>>(
        '/drivers/$id',
        data: request.toJson(),
      );
      return DriverModel.fromJson(response.data!);
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// Update current driver profile
  Future<DriverModel> updateCurrentDriverProfile(DriverUpdateRequest request) async {
    try {
      final response = await _apiClient.put<Map<String, dynamic>>(
        '/drivers/profile',
        data: request.toJson(),
      );
      return DriverModel.fromJson(response.data!);
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// Assign driver to company
  Future<DriverModel> assignDriverToCompany(AssignToCompanyRequest request) async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>(
        '/drivers/assign-to-company',
        data: request.toJson(),
      );
      return DriverModel.fromJson(response.data!);
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// Remove driver from company
  Future<void> removeDriverFromCompany(int driverId) async {
    try {
      await _apiClient.delete('/drivers/$driverId/company');
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// Get drivers by company
  Future<PaginatedResponse<DriverModel>> getDriversByCompany(
    int companyId, {
    int page = 0,
    int size = 20,
    String? sort,
  }) async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>(
        '/drivers/company/$companyId',
        queryParameters: {
          'page': page,
          'size': size,
          if (sort != null) 'sort': sort,
        },
      );
      return PaginatedResponse.fromJson(
        response.data!,
        (json) => DriverModel.fromJson(json as Map<String, dynamic>),
      );
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// Get unassigned drivers
  Future<PaginatedResponse<DriverModel>> getUnassignedDrivers({
    int page = 0,
    int size = 20,
    String? sort,
  }) async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>(
        '/drivers/unassigned',
        queryParameters: {
          'page': page,
          'size': size,
          if (sort != null) 'sort': sort,
        },
      );
      return PaginatedResponse.fromJson(
        response.data!,
        (json) => DriverModel.fromJson(json as Map<String, dynamic>),
      );
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// Delete driver
  Future<void> deleteDriver(int id) async {
    try {
      await _apiClient.delete('/drivers/$id');
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// Get driver verification status
  Future<DriverVerificationSummary> getDriverVerificationStatus(int driverId) async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>(
        '/drivers/$driverId/verification',
      );
      return DriverVerificationSummary.fromJson(response.data!);
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// Update driver verification
  Future<DriverVerificationSummary> updateDriverVerification(
    int driverId,
    UpdateVerificationRequest request,
  ) async {
    try {
      final response = await _apiClient.put<Map<String, dynamic>>(
        '/drivers/$driverId/verification',
        data: request.toJson(),
      );
      return DriverVerificationSummary.fromJson(response.data!);
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// Get verification statistics
  Future<VerificationStats> getVerificationStats() async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>(
        '/drivers/verification/stats',
      );
      return VerificationStats.fromJson(response.data!);
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  Exception _handleError(DioException e) {
    if (e.response?.data != null && e.response!.data is Map) {
      final errorData = e.response!.data as Map<String, dynamic>;
      final message = errorData['message'] ?? 'An error occurred';
      return Exception(message);
    }
    return Exception(e.message ?? 'Network error occurred');
  }
}
