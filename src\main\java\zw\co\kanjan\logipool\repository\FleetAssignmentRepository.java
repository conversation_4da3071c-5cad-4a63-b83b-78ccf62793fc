package zw.co.kanjan.logipool.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import zw.co.kanjan.logipool.entity.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface FleetAssignmentRepository extends JpaRepository<FleetAssignment, Long> {
    
    List<FleetAssignment> findByLoad(Load load);
    
    List<FleetAssignment> findByBid(Bid bid);
    
    List<FleetAssignment> findByCompany(Company company);
    
    Page<FleetAssignment> findByCompany(Company company, Pageable pageable);
    
    List<FleetAssignment> findByCompanyAndStatus(Company company, FleetAssignment.AssignmentStatus status);
    
    Page<FleetAssignment> findByCompanyAndStatus(Company company, FleetAssignment.AssignmentStatus status, Pageable pageable);
    
    Optional<FleetAssignment> findByLoadAndStatus(Load load, FleetAssignment.AssignmentStatus status);
    
    Optional<FleetAssignment> findByBidAndStatus(Bid bid, FleetAssignment.AssignmentStatus status);
    
    List<FleetAssignment> findByTruckHead(TruckHead truckHead);
    
    List<FleetAssignment> findByTrailer1(Trailer trailer);
    
    List<FleetAssignment> findByTrailer2(Trailer trailer);
    
    List<FleetAssignment> findByPrimaryDriver(User driver);
    
    List<FleetAssignment> findBySecondaryDriver(User driver);
    
    @Query("SELECT fa FROM FleetAssignment fa WHERE fa.truckHead = :truckHead AND " +
           "fa.status IN ('PLANNED', 'ACTIVE', 'IN_PROGRESS') AND " +
           "((fa.scheduledStartTime <= :endTime AND fa.scheduledEndTime >= :startTime) OR " +
           "(fa.actualStartTime <= :endTime AND (fa.actualEndTime IS NULL OR fa.actualEndTime >= :startTime)))")
    List<FleetAssignment> findConflictingTruckHeadAssignments(
            @Param("truckHead") TruckHead truckHead,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);
    
    @Query("SELECT fa FROM FleetAssignment fa WHERE fa.trailer1 = :trailer AND " +
           "fa.status IN ('PLANNED', 'ACTIVE', 'IN_PROGRESS') AND " +
           "((fa.scheduledStartTime <= :endTime AND fa.scheduledEndTime >= :startTime) OR " +
           "(fa.actualStartTime <= :endTime AND (fa.actualEndTime IS NULL OR fa.actualEndTime >= :startTime)))")
    List<FleetAssignment> findConflictingTrailer1Assignments(
            @Param("trailer") Trailer trailer,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);
    
    @Query("SELECT fa FROM FleetAssignment fa WHERE fa.trailer2 = :trailer AND " +
           "fa.status IN ('PLANNED', 'ACTIVE', 'IN_PROGRESS') AND " +
           "((fa.scheduledStartTime <= :endTime AND fa.scheduledEndTime >= :startTime) OR " +
           "(fa.actualStartTime <= :endTime AND (fa.actualEndTime IS NULL OR fa.actualEndTime >= :startTime)))")
    List<FleetAssignment> findConflictingTrailer2Assignments(
            @Param("trailer") Trailer trailer,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);
    
    @Query("SELECT fa FROM FleetAssignment fa WHERE fa.primaryDriver = :driver AND " +
           "fa.status IN ('PLANNED', 'ACTIVE', 'IN_PROGRESS') AND " +
           "((fa.scheduledStartTime <= :endTime AND fa.scheduledEndTime >= :startTime) OR " +
           "(fa.actualStartTime <= :endTime AND (fa.actualEndTime IS NULL OR fa.actualEndTime >= :startTime)))")
    List<FleetAssignment> findConflictingDriverAssignments(
            @Param("driver") User driver,
            @Param("startTime") LocalDateTime startTime,
            @Param("endTime") LocalDateTime endTime);
    
    @Query("SELECT COUNT(fa) FROM FleetAssignment fa WHERE fa.company = :company")
    long countByCompany(@Param("company") Company company);
    
    @Query("SELECT COUNT(fa) FROM FleetAssignment fa WHERE fa.company = :company AND fa.status = :status")
    long countByCompanyAndStatus(@Param("company") Company company, @Param("status") FleetAssignment.AssignmentStatus status);
    
    @Query("SELECT fa FROM FleetAssignment fa WHERE fa.scheduledStartTime BETWEEN :startDate AND :endDate")
    List<FleetAssignment> findByScheduledStartTimeBetween(
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate);
}
