package zw.co.kanjan.logipool.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import zw.co.kanjan.logipool.dto.fleet.TruckHeadDto;
import zw.co.kanjan.logipool.entity.Company;
import zw.co.kanjan.logipool.entity.TruckHead;
import zw.co.kanjan.logipool.entity.User;
import zw.co.kanjan.logipool.exception.BusinessException;
import zw.co.kanjan.logipool.exception.ResourceNotFoundException;
import zw.co.kanjan.logipool.mapper.fleet.TruckHeadMapper;
import zw.co.kanjan.logipool.repository.CompanyRepository;
import zw.co.kanjan.logipool.repository.TruckHeadRepository;
import zw.co.kanjan.logipool.repository.UserRepository;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TruckHeadServiceTest {

    @Mock
    private TruckHeadRepository truckHeadRepository;

    @Mock
    private CompanyRepository companyRepository;

    @Mock
    private UserRepository userRepository;

    @Mock
    private TruckHeadMapper truckHeadMapper;

    @InjectMocks
    private TruckHeadService truckHeadService;

    private User testUser;
    private Company testCompany;
    private TruckHead testTruckHead;
    private TruckHeadDto.TruckHeadCreateRequest createRequest;
    private TruckHeadDto.TruckHeadUpdateRequest updateRequest;
    private TruckHeadDto.TruckHeadResponse response;

    @BeforeEach
    void setUp() {
        testCompany = Company.builder()
                .id(1L)
                .name("Test Transport Company")
                .build();

        testUser = User.builder()
                .id(1L)
                .username("testuser")
                .firstName("Test")
                .lastName("User")
                .company(testCompany)
                .build();

        testTruckHead = TruckHead.builder()
                .id(1L)
                .registrationNumber("ABC123")
                .make("Volvo")
                .model("FH16")
                .year(2020)
                .status(TruckHead.TruckStatus.AVAILABLE)
                .company(testCompany)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        createRequest = TruckHeadDto.TruckHeadCreateRequest.builder()
                .registrationNumber("ABC123")
                .make("Volvo")
                .model("FH16")
                .year(2020)
                .build();

        updateRequest = TruckHeadDto.TruckHeadUpdateRequest.builder()
                .make("Scania")
                .model("R500")
                .build();

        response = TruckHeadDto.TruckHeadResponse.builder()
                .id(1L)
                .registrationNumber("ABC123")
                .make("Volvo")
                .model("FH16")
                .year(2020)
                .status(TruckHead.TruckStatus.AVAILABLE)
                .companyId(1L)
                .companyName("Test Transport Company")
                .build();
    }

    @Test
    void createTruckHead_Success() {
        // Given
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));
        when(truckHeadRepository.existsByRegistrationNumber("ABC123")).thenReturn(false);
        when(truckHeadMapper.toEntity(createRequest)).thenReturn(testTruckHead);
        when(truckHeadRepository.save(any(TruckHead.class))).thenReturn(testTruckHead);
        when(truckHeadMapper.toResponse(testTruckHead)).thenReturn(response);

        // When
        TruckHeadDto.TruckHeadResponse result = truckHeadService.createTruckHead(createRequest, "testuser");

        // Then
        assertNotNull(result);
        assertEquals("ABC123", result.getRegistrationNumber());
        assertEquals("Volvo", result.getMake());
        assertEquals("FH16", result.getModel());
        verify(truckHeadRepository).save(any(TruckHead.class));
    }

    @Test
    void createTruckHead_DuplicateRegistrationNumber_ThrowsException() {
        // Given
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));
        when(truckHeadRepository.existsByRegistrationNumber("ABC123")).thenReturn(true);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
                () -> truckHeadService.createTruckHead(createRequest, "testuser"));
        
        assertEquals("Truck head with registration number ABC123 already exists", exception.getMessage());
        verify(truckHeadRepository, never()).save(any(TruckHead.class));
    }

    @Test
    void createTruckHead_UserNotFound_ThrowsException() {
        // Given
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.empty());

        // When & Then
        ResourceNotFoundException exception = assertThrows(ResourceNotFoundException.class, 
                () -> truckHeadService.createTruckHead(createRequest, "testuser"));
        
        assertEquals("User not found", exception.getMessage());
    }

    @Test
    void updateTruckHead_Success() {
        // Given
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));
        when(truckHeadRepository.findById(1L)).thenReturn(Optional.of(testTruckHead));
        when(truckHeadRepository.save(any(TruckHead.class))).thenReturn(testTruckHead);
        when(truckHeadMapper.toResponse(testTruckHead)).thenReturn(response);

        // When
        TruckHeadDto.TruckHeadResponse result = truckHeadService.updateTruckHead(1L, updateRequest, "testuser");

        // Then
        assertNotNull(result);
        verify(truckHeadRepository).save(testTruckHead);
    }

    @Test
    void updateTruckHead_NotFound_ThrowsException() {
        // Given
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));
        when(truckHeadRepository.findById(1L)).thenReturn(Optional.empty());

        // When & Then
        ResourceNotFoundException exception = assertThrows(ResourceNotFoundException.class, 
                () -> truckHeadService.updateTruckHead(1L, updateRequest, "testuser"));
        
        assertEquals("Truck head not found with id: 1", exception.getMessage());
    }

    @Test
    void updateTruckHead_WrongCompany_ThrowsException() {
        // Given
        Company otherCompany = Company.builder().id(2L).name("Other Company").build();
        testTruckHead.setCompany(otherCompany);
        
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));
        when(truckHeadRepository.findById(1L)).thenReturn(Optional.of(testTruckHead));

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
                () -> truckHeadService.updateTruckHead(1L, updateRequest, "testuser"));
        
        assertEquals("You can only update your own truck heads", exception.getMessage());
    }

    @Test
    void getTruckHead_Success() {
        // Given
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));
        when(truckHeadRepository.findById(1L)).thenReturn(Optional.of(testTruckHead));
        when(truckHeadMapper.toResponse(testTruckHead)).thenReturn(response);

        // When
        TruckHeadDto.TruckHeadResponse result = truckHeadService.getTruckHead(1L, "testuser");

        // Then
        assertNotNull(result);
        assertEquals(1L, result.getId());
        assertEquals("ABC123", result.getRegistrationNumber());
    }

    @Test
    void getTruckHeads_Success() {
        // Given
        List<TruckHead> truckHeads = Arrays.asList(testTruckHead);
        Page<TruckHead> page = new PageImpl<>(truckHeads);
        Pageable pageable = PageRequest.of(0, 10);
        
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));
        when(truckHeadRepository.findByCompanyWithFilters(eq(testCompany), any(), any(), any(), any(), eq(pageable)))
                .thenReturn(page);
        when(truckHeadMapper.toResponse(testTruckHead)).thenReturn(response);

        // When
        Page<TruckHeadDto.TruckHeadResponse> result = truckHeadService.getTruckHeads(
                "testuser", pageable, null, null, null, null);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        assertEquals("ABC123", result.getContent().get(0).getRegistrationNumber());
    }

    @Test
    void getAvailableTruckHeads_Success() {
        // Given
        List<TruckHead> availableTruckHeads = Arrays.asList(testTruckHead);
        
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));
        when(truckHeadRepository.findAvailableByCompany(testCompany)).thenReturn(availableTruckHeads);
        when(truckHeadMapper.toResponse(testTruckHead)).thenReturn(response);

        // When
        List<TruckHeadDto.TruckHeadResponse> result = truckHeadService.getAvailableTruckHeads("testuser");

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(TruckHead.TruckStatus.AVAILABLE, testTruckHead.getStatus());
    }

    @Test
    void deleteTruckHead_Success() {
        // Given
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));
        when(truckHeadRepository.findById(1L)).thenReturn(Optional.of(testTruckHead));

        // When
        truckHeadService.deleteTruckHead(1L, "testuser");

        // Then
        verify(truckHeadRepository).delete(testTruckHead);
    }

    @Test
    void deleteTruckHead_AssignedTruck_ThrowsException() {
        // Given
        testTruckHead.setStatus(TruckHead.TruckStatus.ASSIGNED);
        
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));
        when(truckHeadRepository.findById(1L)).thenReturn(Optional.of(testTruckHead));

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
                () -> truckHeadService.deleteTruckHead(1L, "testuser"));
        
        assertEquals("Cannot delete truck head that is currently assigned or in transit", exception.getMessage());
        verify(truckHeadRepository, never()).delete(any(TruckHead.class));
    }

    @Test
    void updateTruckHeadStatus_Success() {
        // Given
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));
        when(truckHeadRepository.findById(1L)).thenReturn(Optional.of(testTruckHead));
        when(truckHeadRepository.save(testTruckHead)).thenReturn(testTruckHead);
        when(truckHeadMapper.toResponse(testTruckHead)).thenReturn(response);

        // When
        TruckHeadDto.TruckHeadResponse result = truckHeadService.updateTruckHeadStatus(
                1L, TruckHead.TruckStatus.MAINTENANCE, "testuser");

        // Then
        assertNotNull(result);
        assertEquals(TruckHead.TruckStatus.MAINTENANCE, testTruckHead.getStatus());
        verify(truckHeadRepository).save(testTruckHead);
    }

    @Test
    void getTruckHeadsExpiringBetween_Insurance_Success() {
        // Given
        LocalDateTime startDate = LocalDateTime.now();
        LocalDateTime endDate = LocalDateTime.now().plusDays(30);
        List<TruckHead> expiringTruckHeads = Arrays.asList(testTruckHead);
        
        when(truckHeadRepository.findByInsuranceExpiryDateBetween(startDate, endDate))
                .thenReturn(expiringTruckHeads);
        when(truckHeadMapper.toResponse(testTruckHead)).thenReturn(response);

        // When
        List<TruckHeadDto.TruckHeadResponse> result = truckHeadService.getTruckHeadsExpiringBetween(
                startDate, endDate, "insurance");

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(truckHeadRepository).findByInsuranceExpiryDateBetween(startDate, endDate);
    }

    @Test
    void getTruckHeadsExpiringBetween_InvalidType_ThrowsException() {
        // Given
        LocalDateTime startDate = LocalDateTime.now();
        LocalDateTime endDate = LocalDateTime.now().plusDays(30);

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class, 
                () -> truckHeadService.getTruckHeadsExpiringBetween(startDate, endDate, "invalid"));
        
        assertEquals("Invalid expiry type: invalid", exception.getMessage());
    }
}
