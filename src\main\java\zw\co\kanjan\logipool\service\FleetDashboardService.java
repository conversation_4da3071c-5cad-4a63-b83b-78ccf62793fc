package zw.co.kanjan.logipool.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import zw.co.kanjan.logipool.dto.fleet.FleetDashboardDto;
import zw.co.kanjan.logipool.entity.*;
import zw.co.kanjan.logipool.exception.BusinessException;
import zw.co.kanjan.logipool.exception.ResourceNotFoundException;
import zw.co.kanjan.logipool.repository.*;

import java.time.LocalDateTime;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional(readOnly = true)
public class FleetDashboardService {
    
    private final TruckHeadRepository truckHeadRepository;
    private final TrailerRepository trailerRepository;
    private final FleetAssignmentRepository fleetAssignmentRepository;
    private final InsuranceRepository insuranceRepository;
    private final TaxClearanceRepository taxClearanceRepository;
    private final PermitRepository permitRepository;
    private final UserRepository userRepository;
    private final CompanyRepository companyRepository;
    
    public FleetDashboardDto.FleetOverviewResponse getFleetOverview(String username) {
        User user = getUserByUsername(username);
        Company company = getCompanyByUser(user);
        
        // Get fleet counts
        long totalTruckHeads = truckHeadRepository.countByCompany(company);
        long availableTruckHeads = truckHeadRepository.countByCompanyAndStatus(company, TruckHead.TruckStatus.AVAILABLE);
        long assignedTruckHeads = truckHeadRepository.countByCompanyAndStatus(company, TruckHead.TruckStatus.ASSIGNED);
        long inTransitTruckHeads = truckHeadRepository.countByCompanyAndStatus(company, TruckHead.TruckStatus.IN_TRANSIT);
        long maintenanceTruckHeads = truckHeadRepository.countByCompanyAndStatus(company, TruckHead.TruckStatus.MAINTENANCE);
        
        long totalTrailers = trailerRepository.countByCompany(company);
        long availableTrailers = trailerRepository.countByCompanyAndStatus(company, Trailer.TrailerStatus.AVAILABLE);
        long assignedTrailers = trailerRepository.countByCompanyAndStatus(company, Trailer.TrailerStatus.ASSIGNED);
        long inTransitTrailers = trailerRepository.countByCompanyAndStatus(company, Trailer.TrailerStatus.IN_TRANSIT);
        long maintenanceTrailers = trailerRepository.countByCompanyAndStatus(company, Trailer.TrailerStatus.MAINTENANCE);
        
        // Get assignment counts
        long totalAssignments = fleetAssignmentRepository.countByCompany(company);
        long activeAssignments = fleetAssignmentRepository.countByCompanyAndStatus(company, FleetAssignment.AssignmentStatus.ACTIVE);
        long plannedAssignments = fleetAssignmentRepository.countByCompanyAndStatus(company, FleetAssignment.AssignmentStatus.PLANNED);
        long completedAssignments = fleetAssignmentRepository.countByCompanyAndStatus(company, FleetAssignment.AssignmentStatus.COMPLETED);
        
        // Get compliance counts
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime thirtyDaysFromNow = now.plusDays(30);
        
        List<TruckHead> truckHeadsWithExpiringInsurance = truckHeadRepository.findByInsuranceExpiryDateBetween(now, thirtyDaysFromNow);
        List<TruckHead> truckHeadsWithExpiringFitness = truckHeadRepository.findByFitnessExpiryDateBetween(now, thirtyDaysFromNow);
        List<TruckHead> truckHeadsWithExpiringPermits = truckHeadRepository.findByRoadPermitExpiryDateBetween(now, thirtyDaysFromNow);
        List<TruckHead> truckHeadsWithExpiringTax = truckHeadRepository.findByTaxClearanceExpiryDateBetween(now, thirtyDaysFromNow);
        
        List<Trailer> trailersWithExpiringInsurance = trailerRepository.findByInsuranceExpiryDateBetween(now, thirtyDaysFromNow);
        List<Trailer> trailersWithExpiringFitness = trailerRepository.findByFitnessExpiryDateBetween(now, thirtyDaysFromNow);
        List<Trailer> trailersWithExpiringPermits = trailerRepository.findByRoadPermitExpiryDateBetween(now, thirtyDaysFromNow);
        
        return FleetDashboardDto.FleetOverviewResponse.builder()
                .totalTruckHeads(totalTruckHeads)
                .availableTruckHeads(availableTruckHeads)
                .assignedTruckHeads(assignedTruckHeads)
                .inTransitTruckHeads(inTransitTruckHeads)
                .maintenanceTruckHeads(maintenanceTruckHeads)
                .totalTrailers(totalTrailers)
                .availableTrailers(availableTrailers)
                .assignedTrailers(assignedTrailers)
                .inTransitTrailers(inTransitTrailers)
                .maintenanceTrailers(maintenanceTrailers)
                .totalAssignments(totalAssignments)
                .activeAssignments(activeAssignments)
                .plannedAssignments(plannedAssignments)
                .completedAssignments(completedAssignments)
                .truckHeadsWithExpiringInsurance(truckHeadsWithExpiringInsurance.size())
                .truckHeadsWithExpiringFitness(truckHeadsWithExpiringFitness.size())
                .truckHeadsWithExpiringPermits(truckHeadsWithExpiringPermits.size())
                .truckHeadsWithExpiringTax(truckHeadsWithExpiringTax.size())
                .trailersWithExpiringInsurance(trailersWithExpiringInsurance.size())
                .trailersWithExpiringFitness(trailersWithExpiringFitness.size())
                .trailersWithExpiringPermits(trailersWithExpiringPermits.size())
                .build();
    }
    
    public List<FleetDashboardDto.ComplianceAlertResponse> getComplianceAlerts(String username) {
        User user = getUserByUsername(username);
        Company company = getCompanyByUser(user);
        
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime thirtyDaysFromNow = now.plusDays(30);
        
        List<FleetDashboardDto.ComplianceAlertResponse> alerts = new java.util.ArrayList<>();
        
        // Insurance expiry alerts
        List<Insurance> expiringInsurances = insuranceRepository.findByCompanyAndExpiryDateBetween(company, now, thirtyDaysFromNow);
        for (Insurance insurance : expiringInsurances) {
            alerts.add(FleetDashboardDto.ComplianceAlertResponse.builder()
                    .alertType("INSURANCE_EXPIRY")
                    .severity(getSeverityBasedOnDaysToExpiry(insurance.getExpiryDate(), now))
                    .title("Insurance Expiring")
                    .message(String.format("Insurance policy %s expires on %s", 
                            insurance.getPolicyNumber(), insurance.getExpiryDate().toLocalDate()))
                    .resourceType(getResourceType(insurance))
                    .resourceId(getResourceId(insurance))
                    .resourceName(getResourceName(insurance))
                    .expiryDate(insurance.getExpiryDate())
                    .daysToExpiry(java.time.temporal.ChronoUnit.DAYS.between(now.toLocalDate(), insurance.getExpiryDate().toLocalDate()))
                    .build());
        }
        
        // Tax clearance expiry alerts
        List<TaxClearance> expiringTaxClearances = taxClearanceRepository.findByCompanyAndExpiryDateBetween(company, now, thirtyDaysFromNow);
        for (TaxClearance taxClearance : expiringTaxClearances) {
            alerts.add(FleetDashboardDto.ComplianceAlertResponse.builder()
                    .alertType("TAX_CLEARANCE_EXPIRY")
                    .severity(getSeverityBasedOnDaysToExpiry(taxClearance.getExpiryDate(), now))
                    .title("Tax Clearance Expiring")
                    .message(String.format("Tax clearance %s expires on %s", 
                            taxClearance.getCertificateNumber(), taxClearance.getExpiryDate().toLocalDate()))
                    .resourceType(getTaxClearanceResourceType(taxClearance))
                    .resourceId(getTaxClearanceResourceId(taxClearance))
                    .resourceName(getTaxClearanceResourceName(taxClearance))
                    .expiryDate(taxClearance.getExpiryDate())
                    .daysToExpiry(java.time.temporal.ChronoUnit.DAYS.between(now.toLocalDate(), taxClearance.getExpiryDate().toLocalDate()))
                    .build());
        }
        
        // Permit expiry alerts
        List<Permit> expiringPermits = permitRepository.findByCompanyAndExpiryDateBetween(company, now, thirtyDaysFromNow);
        for (Permit permit : expiringPermits) {
            alerts.add(FleetDashboardDto.ComplianceAlertResponse.builder()
                    .alertType("PERMIT_EXPIRY")
                    .severity(getSeverityBasedOnDaysToExpiry(permit.getExpiryDate(), now))
                    .title("Permit Expiring")
                    .message(String.format("Permit %s expires on %s", 
                            permit.getPermitNumber(), permit.getExpiryDate().toLocalDate()))
                    .resourceType(getPermitResourceType(permit))
                    .resourceId(getPermitResourceId(permit))
                    .resourceName(getPermitResourceName(permit))
                    .expiryDate(permit.getExpiryDate())
                    .daysToExpiry(java.time.temporal.ChronoUnit.DAYS.between(now.toLocalDate(), permit.getExpiryDate().toLocalDate()))
                    .build());
        }
        
        // Sort alerts by severity and days to expiry
        alerts.sort((a, b) -> {
            int severityCompare = getSeverityOrder(a.getSeverity()) - getSeverityOrder(b.getSeverity());
            if (severityCompare != 0) return severityCompare;
            return Long.compare(a.getDaysToExpiry(), b.getDaysToExpiry());
        });
        
        return alerts;
    }
    
    public List<FleetDashboardDto.FleetUtilizationResponse> getFleetUtilization(String username, LocalDateTime startDate, LocalDateTime endDate) {
        User user = getUserByUsername(username);
        Company company = getCompanyByUser(user);
        
        List<FleetDashboardDto.FleetUtilizationResponse> utilization = new java.util.ArrayList<>();
        
        // Get all truck heads
        List<TruckHead> truckHeads = truckHeadRepository.findByCompany(company);
        for (TruckHead truckHead : truckHeads) {
            List<FleetAssignment> assignments = fleetAssignmentRepository.findByScheduledStartTimeBetween(startDate, endDate)
                    .stream()
                    .filter(assignment -> assignment.getTruckHead() != null && 
                                        assignment.getTruckHead().getId().equals(truckHead.getId()))
                    .toList();
            
            long totalHours = java.time.temporal.ChronoUnit.HOURS.between(startDate, endDate);
            long assignedHours = assignments.stream()
                    .mapToLong(assignment -> {
                        LocalDateTime start = assignment.getScheduledStartTime().isBefore(startDate) ? startDate : assignment.getScheduledStartTime();
                        LocalDateTime end = assignment.getScheduledEndTime().isAfter(endDate) ? endDate : assignment.getScheduledEndTime();
                        return java.time.temporal.ChronoUnit.HOURS.between(start, end);
                    })
                    .sum();
            
            double utilizationPercentage = totalHours > 0 ? (double) assignedHours / totalHours * 100 : 0;
            
            utilization.add(FleetDashboardDto.FleetUtilizationResponse.builder()
                    .resourceType("TRUCK_HEAD")
                    .resourceId(truckHead.getId())
                    .resourceName(truckHead.getRegistrationNumber())
                    .totalHours(totalHours)
                    .assignedHours(assignedHours)
                    .utilizationPercentage(utilizationPercentage)
                    .totalAssignments(assignments.size())
                    .build());
        }
        
        return utilization;
    }
    
    private User getUserByUsername(String username) {
        return userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
    }
    
    private Company getCompanyByUser(User user) {
        if (user.getCompany() == null) {
            throw new BusinessException("User must be associated with a company");
        }
        return user.getCompany();
    }
    
    private String getSeverityBasedOnDaysToExpiry(LocalDateTime expiryDate, LocalDateTime now) {
        long daysToExpiry = java.time.temporal.ChronoUnit.DAYS.between(now.toLocalDate(), expiryDate.toLocalDate());
        if (daysToExpiry <= 0) return "CRITICAL";
        if (daysToExpiry <= 7) return "HIGH";
        if (daysToExpiry <= 30) return "MEDIUM";
        return "LOW";
    }
    
    private int getSeverityOrder(String severity) {
        switch (severity) {
            case "CRITICAL": return 1;
            case "HIGH": return 2;
            case "MEDIUM": return 3;
            case "LOW": return 4;
            default: return 5;
        }
    }
    
    private String getResourceType(Insurance insurance) {
        if (insurance.getTruckHead() != null) return "TRUCK_HEAD";
        if (insurance.getTrailer() != null) return "TRAILER";
        if (insurance.getVehicle() != null) return "VEHICLE";
        if (insurance.getEquipment() != null) return "EQUIPMENT";
        return "COMPANY";
    }
    
    private Long getResourceId(Insurance insurance) {
        if (insurance.getTruckHead() != null) return insurance.getTruckHead().getId();
        if (insurance.getTrailer() != null) return insurance.getTrailer().getId();
        if (insurance.getVehicle() != null) return insurance.getVehicle().getId();
        if (insurance.getEquipment() != null) return insurance.getEquipment().getId();
        return insurance.getCompany().getId();
    }
    
    private String getResourceName(Insurance insurance) {
        if (insurance.getTruckHead() != null) return insurance.getTruckHead().getRegistrationNumber();
        if (insurance.getTrailer() != null) return insurance.getTrailer().getRegistrationNumber();
        if (insurance.getVehicle() != null) return insurance.getVehicle().getRegistrationNumber();
        if (insurance.getEquipment() != null) return insurance.getEquipment().getName();
        return insurance.getCompany().getName();
    }
    
    private String getTaxClearanceResourceType(TaxClearance taxClearance) {
        if (taxClearance.getTruckHead() != null) return "TRUCK_HEAD";
        if (taxClearance.getVehicle() != null) return "VEHICLE";
        return "COMPANY";
    }
    
    private Long getTaxClearanceResourceId(TaxClearance taxClearance) {
        if (taxClearance.getTruckHead() != null) return taxClearance.getTruckHead().getId();
        if (taxClearance.getVehicle() != null) return taxClearance.getVehicle().getId();
        return taxClearance.getCompany().getId();
    }
    
    private String getTaxClearanceResourceName(TaxClearance taxClearance) {
        if (taxClearance.getTruckHead() != null) return taxClearance.getTruckHead().getRegistrationNumber();
        if (taxClearance.getVehicle() != null) return taxClearance.getVehicle().getRegistrationNumber();
        return taxClearance.getCompany().getName();
    }
    
    private String getPermitResourceType(Permit permit) {
        if (permit.getTruckHead() != null) return "TRUCK_HEAD";
        if (permit.getTrailer() != null) return "TRAILER";
        if (permit.getVehicle() != null) return "VEHICLE";
        return "COMPANY";
    }
    
    private Long getPermitResourceId(Permit permit) {
        if (permit.getTruckHead() != null) return permit.getTruckHead().getId();
        if (permit.getTrailer() != null) return permit.getTrailer().getId();
        if (permit.getVehicle() != null) return permit.getVehicle().getId();
        return permit.getCompany().getId();
    }
    
    private String getPermitResourceName(Permit permit) {
        if (permit.getTruckHead() != null) return permit.getTruckHead().getRegistrationNumber();
        if (permit.getTrailer() != null) return permit.getTrailer().getRegistrationNumber();
        if (permit.getVehicle() != null) return permit.getVehicle().getRegistrationNumber();
        return permit.getCompany().getName();
    }
}
