import 'package:equatable/equatable.dart';

class LoadModel extends Equatable {
  final int? id;
  final String? trackingNumber;
  final String title;
  final String? description;
  final String cargoType;
  final double weight;
  final String weightUnit;
  final double? volume;
  final String? volumeUnit;
  final String pickupLocation;
  final double? pickupLatitude;
  final double? pickupLongitude;
  final String deliveryLocation;
  final double? deliveryLatitude;
  final double? deliveryLongitude;
  final DateTime pickupDate;
  final DateTime deliveryDate;
  final double? estimatedDistance;
  final String? distanceUnit;
  final LoadType? loadType;
  final PaymentType? paymentType;
  final double? paymentRate;
  final String? paymentUnit;
  final double? estimatedValue;
  final LoadStatus status;
  final Priority priority;
  final bool isVerified;
  final bool requiresInsurance;
  final bool requiresSpecialHandling;
  final String? specialInstructions;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? biddingClosesAt;
  final int? clientId;
  final String? clientName;
  final int? assignedCompanyId;
  final String? assignedCompanyName;
  final int bidCount;

  // Fleet assignment information
  final int? fleetAssignmentId;
  final int? assignedTruckHeadId;
  final String? assignedTruckHeadRegistration;
  final String? assignedTruckHeadMake;
  final String? assignedTruckHeadModel;
  final int? assignedTrailer1Id;
  final String? assignedTrailer1Registration;
  final String? assignedTrailer1Type;
  final int? assignedTrailer2Id;
  final String? assignedTrailer2Registration;
  final String? assignedTrailer2Type;
  final int? assignedPrimaryDriverId;
  final String? assignedPrimaryDriverName;
  final int? assignedSecondaryDriverId;
  final String? assignedSecondaryDriverName;
  final String? fleetAssignmentStatus;
  final DateTime? fleetAssignmentScheduledStartTime;
  final DateTime? fleetAssignmentScheduledEndTime;
  final bool isLiveTrackingActive;
  final String? liveTrackingUser;
  final DateTime? liveTrackingStartedAt;

  const LoadModel({
    this.id,
    this.trackingNumber,
    required this.title,
    this.description,
    required this.cargoType,
    required this.weight,
    this.weightUnit = 'kg',
    this.volume,
    this.volumeUnit = 'm3',
    required this.pickupLocation,
    this.pickupLatitude,
    this.pickupLongitude,
    required this.deliveryLocation,
    this.deliveryLatitude,
    this.deliveryLongitude,
    required this.pickupDate,
    required this.deliveryDate,
    this.estimatedDistance,
    this.distanceUnit = 'km',
    this.loadType,
    this.paymentType,
    this.paymentRate,
    this.paymentUnit,
    this.estimatedValue,
    this.status = LoadStatus.posted,
    this.priority = Priority.normal,
    this.isVerified = false,
    this.requiresInsurance = false,
    this.requiresSpecialHandling = false,
    this.specialInstructions,
    this.createdAt,
    this.updatedAt,
    this.biddingClosesAt,
    this.clientId,
    this.clientName,
    this.assignedCompanyId,
    this.assignedCompanyName,
    this.bidCount = 0,
    this.fleetAssignmentId,
    this.assignedTruckHeadId,
    this.assignedTruckHeadRegistration,
    this.assignedTruckHeadMake,
    this.assignedTruckHeadModel,
    this.assignedTrailer1Id,
    this.assignedTrailer1Registration,
    this.assignedTrailer1Type,
    this.assignedTrailer2Id,
    this.assignedTrailer2Registration,
    this.assignedTrailer2Type,
    this.assignedPrimaryDriverId,
    this.assignedPrimaryDriverName,
    this.assignedSecondaryDriverId,
    this.assignedSecondaryDriverName,
    this.fleetAssignmentStatus,
    this.fleetAssignmentScheduledStartTime,
    this.fleetAssignmentScheduledEndTime,
    this.isLiveTrackingActive = false,
    this.liveTrackingUser,
    this.liveTrackingStartedAt,
  });

  factory LoadModel.fromJson(Map<String, dynamic> json) {
    return LoadModel(
      id: json['id'],
      trackingNumber: json['trackingNumber'],
      title: json['title'] ?? '',
      description: json['description'],
      cargoType: json['cargoType'] ?? '',
      weight: (json['weight'] as num?)?.toDouble() ?? 0.0,
      weightUnit: json['weightUnit'] ?? 'kg',
      volume: (json['volume'] as num?)?.toDouble(),
      volumeUnit: json['volumeUnit'] ?? 'm3',
      pickupLocation: json['pickupLocation'] ?? '',
      pickupLatitude: (json['pickupLatitude'] as num?)?.toDouble(),
      pickupLongitude: (json['pickupLongitude'] as num?)?.toDouble(),
      deliveryLocation: json['deliveryLocation'] ?? '',
      deliveryLatitude: (json['deliveryLatitude'] as num?)?.toDouble(),
      deliveryLongitude: (json['deliveryLongitude'] as num?)?.toDouble(),
      pickupDate: DateTime.parse(json['pickupDate'] as String),
      deliveryDate: DateTime.parse(json['deliveryDate'] as String),
      estimatedDistance: (json['estimatedDistance'] as num?)?.toDouble(),
      distanceUnit: json['distanceUnit'] as String? ?? 'km',
      loadType: json['loadType'] != null
          ? LoadType.values.firstWhere(
              (e) =>
                  e.name.toUpperCase() ==
                  json['loadType'].toString().toUpperCase(),
              orElse: () => LoadType.local,
            )
          : null,
      paymentType: json['paymentType'] != null
          ? PaymentType.values.firstWhere(
              (e) =>
                  e.name.toUpperCase() ==
                  json['paymentType'].toString().toUpperCase(),
              orElse: () => PaymentType.negotiable,
            )
          : null,
      paymentRate: (json['paymentRate'] as num?)?.toDouble(),
      paymentUnit: json['paymentUnit'] as String?,
      estimatedValue: (json['estimatedValue'] as num?)?.toDouble(),
      status: LoadStatus.fromBackendValue(json['status'].toString()),
      priority: Priority.values.firstWhere(
        (e) =>
            e.name.toUpperCase() == json['priority'].toString().toUpperCase(),
        orElse: () => Priority.normal,
      ),
      isVerified: json['isVerified'] as bool? ?? false,
      requiresInsurance: json['requiresInsurance'] as bool? ?? false,
      requiresSpecialHandling:
          json['requiresSpecialHandling'] as bool? ?? false,
      specialInstructions: json['specialInstructions'] as String?,
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'] as String)
          : null,
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
      biddingClosesAt: json['biddingClosesAt'] != null
          ? DateTime.parse(json['biddingClosesAt'] as String)
          : null,
      clientId: json['clientId'] as int?,
      clientName: json['clientName'] as String?,
      assignedCompanyId: json['assignedCompanyId'] as int?,
      assignedCompanyName: json['assignedCompanyName'] as String?,
      bidCount: json['bidCount'] as int? ?? 0,
      fleetAssignmentId: json['fleetAssignmentId'] as int?,
      assignedTruckHeadId: json['assignedTruckHeadId'] as int?,
      assignedTruckHeadRegistration: json['assignedTruckHeadRegistration'] as String?,
      assignedTruckHeadMake: json['assignedTruckHeadMake'] as String?,
      assignedTruckHeadModel: json['assignedTruckHeadModel'] as String?,
      assignedTrailer1Id: json['assignedTrailer1Id'] as int?,
      assignedTrailer1Registration: json['assignedTrailer1Registration'] as String?,
      assignedTrailer1Type: json['assignedTrailer1Type'] as String?,
      assignedTrailer2Id: json['assignedTrailer2Id'] as int?,
      assignedTrailer2Registration: json['assignedTrailer2Registration'] as String?,
      assignedTrailer2Type: json['assignedTrailer2Type'] as String?,
      assignedPrimaryDriverId: json['assignedPrimaryDriverId'] as int?,
      assignedPrimaryDriverName: json['assignedPrimaryDriverName'] as String?,
      assignedSecondaryDriverId: json['assignedSecondaryDriverId'] as int?,
      assignedSecondaryDriverName: json['assignedSecondaryDriverName'] as String?,
      fleetAssignmentStatus: json['fleetAssignmentStatus'] as String?,
      fleetAssignmentScheduledStartTime: json['fleetAssignmentScheduledStartTime'] != null
          ? DateTime.parse(json['fleetAssignmentScheduledStartTime'] as String)
          : null,
      fleetAssignmentScheduledEndTime: json['fleetAssignmentScheduledEndTime'] != null
          ? DateTime.parse(json['fleetAssignmentScheduledEndTime'] as String)
          : null,
      isLiveTrackingActive: json['isLiveTrackingActive'] as bool? ?? false,
      liveTrackingUser: json['liveTrackingUser'] as String?,
      liveTrackingStartedAt: json['liveTrackingStartedAt'] != null
          ? DateTime.parse(json['liveTrackingStartedAt'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      if (trackingNumber != null) 'trackingNumber': trackingNumber,
      'title': title,
      if (description != null) 'description': description,
      'cargoType': cargoType,
      'weight': weight,
      'weightUnit': weightUnit,
      if (volume != null) 'volume': volume,
      if (volumeUnit != null) 'volumeUnit': volumeUnit,
      'pickupLocation': pickupLocation,
      if (pickupLatitude != null) 'pickupLatitude': pickupLatitude,
      if (pickupLongitude != null) 'pickupLongitude': pickupLongitude,
      'deliveryLocation': deliveryLocation,
      if (deliveryLatitude != null) 'deliveryLatitude': deliveryLatitude,
      if (deliveryLongitude != null) 'deliveryLongitude': deliveryLongitude,
      'pickupDate': pickupDate.toIso8601String(),
      'deliveryDate': deliveryDate.toIso8601String(),
      if (estimatedDistance != null) 'estimatedDistance': estimatedDistance,
      if (distanceUnit != null) 'distanceUnit': distanceUnit,
      if (loadType != null) 'loadType': loadType!.name.toUpperCase(),
      if (paymentType != null) 'paymentType': paymentType!.name.toUpperCase(),
      if (paymentRate != null) 'paymentRate': paymentRate,
      if (paymentUnit != null) 'paymentUnit': paymentUnit,
      if (estimatedValue != null) 'estimatedValue': estimatedValue,
      'status': status.name.toUpperCase(),
      'priority': priority.name.toUpperCase(),
      'isVerified': isVerified,
      'requiresInsurance': requiresInsurance,
      'requiresSpecialHandling': requiresSpecialHandling,
      if (specialInstructions != null)
        'specialInstructions': specialInstructions,
      if (createdAt != null) 'createdAt': createdAt!.toIso8601String(),
      if (updatedAt != null) 'updatedAt': updatedAt!.toIso8601String(),
      if (biddingClosesAt != null)
        'biddingClosesAt': biddingClosesAt!.toIso8601String(),
      if (clientId != null) 'clientId': clientId,
      if (clientName != null) 'clientName': clientName,
      if (assignedCompanyId != null) 'assignedCompanyId': assignedCompanyId,
      if (assignedCompanyName != null)
        'assignedCompanyName': assignedCompanyName,
      'bidCount': bidCount,
      'isLiveTrackingActive': isLiveTrackingActive,
      if (liveTrackingUser != null) 'liveTrackingUser': liveTrackingUser,
      if (liveTrackingStartedAt != null)
        'liveTrackingStartedAt': liveTrackingStartedAt!.toIso8601String(),
    };
  }

  LoadModel copyWith({
    int? id,
    String? trackingNumber,
    String? title,
    String? description,
    String? cargoType,
    double? weight,
    String? weightUnit,
    double? volume,
    String? volumeUnit,
    String? pickupLocation,
    double? pickupLatitude,
    double? pickupLongitude,
    String? deliveryLocation,
    double? deliveryLatitude,
    double? deliveryLongitude,
    DateTime? pickupDate,
    DateTime? deliveryDate,
    double? estimatedDistance,
    String? distanceUnit,
    LoadType? loadType,
    PaymentType? paymentType,
    double? paymentRate,
    String? paymentUnit,
    double? estimatedValue,
    LoadStatus? status,
    Priority? priority,
    bool? isVerified,
    bool? requiresInsurance,
    bool? requiresSpecialHandling,
    String? specialInstructions,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? biddingClosesAt,
    int? clientId,
    String? clientName,
    int? assignedCompanyId,
    String? assignedCompanyName,
    int? bidCount,
    int? fleetAssignmentId,
    int? assignedTruckHeadId,
    String? assignedTruckHeadRegistration,
    String? assignedTruckHeadMake,
    String? assignedTruckHeadModel,
    int? assignedTrailer1Id,
    String? assignedTrailer1Registration,
    String? assignedTrailer1Type,
    int? assignedTrailer2Id,
    String? assignedTrailer2Registration,
    String? assignedTrailer2Type,
    int? assignedPrimaryDriverId,
    String? assignedPrimaryDriverName,
    int? assignedSecondaryDriverId,
    String? assignedSecondaryDriverName,
    String? fleetAssignmentStatus,
    DateTime? fleetAssignmentScheduledStartTime,
    DateTime? fleetAssignmentScheduledEndTime,
    bool? isLiveTrackingActive,
    String? liveTrackingUser,
    DateTime? liveTrackingStartedAt,
  }) {
    return LoadModel(
      id: id ?? this.id,
      trackingNumber: trackingNumber ?? this.trackingNumber,
      title: title ?? this.title,
      description: description ?? this.description,
      cargoType: cargoType ?? this.cargoType,
      weight: weight ?? this.weight,
      weightUnit: weightUnit ?? this.weightUnit,
      volume: volume ?? this.volume,
      volumeUnit: volumeUnit ?? this.volumeUnit,
      pickupLocation: pickupLocation ?? this.pickupLocation,
      pickupLatitude: pickupLatitude ?? this.pickupLatitude,
      pickupLongitude: pickupLongitude ?? this.pickupLongitude,
      deliveryLocation: deliveryLocation ?? this.deliveryLocation,
      deliveryLatitude: deliveryLatitude ?? this.deliveryLatitude,
      deliveryLongitude: deliveryLongitude ?? this.deliveryLongitude,
      pickupDate: pickupDate ?? this.pickupDate,
      deliveryDate: deliveryDate ?? this.deliveryDate,
      estimatedDistance: estimatedDistance ?? this.estimatedDistance,
      distanceUnit: distanceUnit ?? this.distanceUnit,
      loadType: loadType ?? this.loadType,
      paymentType: paymentType ?? this.paymentType,
      paymentRate: paymentRate ?? this.paymentRate,
      paymentUnit: paymentUnit ?? this.paymentUnit,
      estimatedValue: estimatedValue ?? this.estimatedValue,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      isVerified: isVerified ?? this.isVerified,
      requiresInsurance: requiresInsurance ?? this.requiresInsurance,
      requiresSpecialHandling:
          requiresSpecialHandling ?? this.requiresSpecialHandling,
      specialInstructions: specialInstructions ?? this.specialInstructions,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      biddingClosesAt: biddingClosesAt ?? this.biddingClosesAt,
      clientId: clientId ?? this.clientId,
      clientName: clientName ?? this.clientName,
      assignedCompanyId: assignedCompanyId ?? this.assignedCompanyId,
      assignedCompanyName: assignedCompanyName ?? this.assignedCompanyName,
      bidCount: bidCount ?? this.bidCount,
      fleetAssignmentId: fleetAssignmentId ?? this.fleetAssignmentId,
      assignedTruckHeadId: assignedTruckHeadId ?? this.assignedTruckHeadId,
      assignedTruckHeadRegistration: assignedTruckHeadRegistration ?? this.assignedTruckHeadRegistration,
      assignedTruckHeadMake: assignedTruckHeadMake ?? this.assignedTruckHeadMake,
      assignedTruckHeadModel: assignedTruckHeadModel ?? this.assignedTruckHeadModel,
      assignedTrailer1Id: assignedTrailer1Id ?? this.assignedTrailer1Id,
      assignedTrailer1Registration: assignedTrailer1Registration ?? this.assignedTrailer1Registration,
      assignedTrailer1Type: assignedTrailer1Type ?? this.assignedTrailer1Type,
      assignedTrailer2Id: assignedTrailer2Id ?? this.assignedTrailer2Id,
      assignedTrailer2Registration: assignedTrailer2Registration ?? this.assignedTrailer2Registration,
      assignedTrailer2Type: assignedTrailer2Type ?? this.assignedTrailer2Type,
      assignedPrimaryDriverId: assignedPrimaryDriverId ?? this.assignedPrimaryDriverId,
      assignedPrimaryDriverName: assignedPrimaryDriverName ?? this.assignedPrimaryDriverName,
      assignedSecondaryDriverId: assignedSecondaryDriverId ?? this.assignedSecondaryDriverId,
      assignedSecondaryDriverName: assignedSecondaryDriverName ?? this.assignedSecondaryDriverName,
      fleetAssignmentStatus: fleetAssignmentStatus ?? this.fleetAssignmentStatus,
      fleetAssignmentScheduledStartTime: fleetAssignmentScheduledStartTime ?? this.fleetAssignmentScheduledStartTime,
      fleetAssignmentScheduledEndTime: fleetAssignmentScheduledEndTime ?? this.fleetAssignmentScheduledEndTime,
      isLiveTrackingActive: isLiveTrackingActive ?? this.isLiveTrackingActive,
      liveTrackingUser: liveTrackingUser ?? this.liveTrackingUser,
      liveTrackingStartedAt: liveTrackingStartedAt ?? this.liveTrackingStartedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        trackingNumber,
        title,
        description,
        cargoType,
        weight,
        weightUnit,
        volume,
        volumeUnit,
        pickupLocation,
        pickupLatitude,
        pickupLongitude,
        deliveryLocation,
        deliveryLatitude,
        deliveryLongitude,
        pickupDate,
        deliveryDate,
        estimatedDistance,
        distanceUnit,
        loadType,
        paymentType,
        paymentRate,
        paymentUnit,
        estimatedValue,
        status,
        priority,
        isVerified,
        requiresInsurance,
        requiresSpecialHandling,
        specialInstructions,
        createdAt,
        updatedAt,
        biddingClosesAt,
        clientId,
        clientName,
        assignedCompanyId,
        assignedCompanyName,
        bidCount,
        fleetAssignmentId,
        assignedTruckHeadId,
        assignedTruckHeadRegistration,
        assignedTruckHeadMake,
        assignedTruckHeadModel,
        assignedTrailer1Id,
        assignedTrailer1Registration,
        assignedTrailer1Type,
        assignedTrailer2Id,
        assignedTrailer2Registration,
        assignedTrailer2Type,
        assignedPrimaryDriverId,
        assignedPrimaryDriverName,
        assignedSecondaryDriverId,
        assignedSecondaryDriverName,
        fleetAssignmentStatus,
        fleetAssignmentScheduledStartTime,
        fleetAssignmentScheduledEndTime,
        isLiveTrackingActive,
        liveTrackingUser,
        liveTrackingStartedAt,
      ];
}

enum LoadType {
  local,
  regional,
  contract,
  onceOff;

  String get displayName {
    switch (this) {
      case LoadType.local:
        return 'Local';
      case LoadType.regional:
        return 'Regional';
      case LoadType.contract:
        return 'Contract';
      case LoadType.onceOff:
        return 'Once Off';
    }
  }
}

enum PaymentType {
  perKm,
  perTonne,
  fixedRate,
  negotiable;

  String get displayName {
    switch (this) {
      case PaymentType.perKm:
        return 'Per KM';
      case PaymentType.perTonne:
        return 'Per Tonne';
      case PaymentType.fixedRate:
        return 'Fixed Rate';
      case PaymentType.negotiable:
        return 'Negotiable';
    }
  }
}

enum LoadStatus {
  posted,
  biddingClosed,
  assigned,
  inTransit,
  delivered,
  completed,
  cancelled;

  String get displayName {
    switch (this) {
      case LoadStatus.posted:
        return 'Posted';
      case LoadStatus.biddingClosed:
        return 'Bidding Closed';
      case LoadStatus.assigned:
        return 'Assigned';
      case LoadStatus.inTransit:
        return 'In Transit';
      case LoadStatus.delivered:
        return 'Delivered';
      case LoadStatus.completed:
        return 'Completed';
      case LoadStatus.cancelled:
        return 'Cancelled';
    }
  }

  /// Converts the enum to the backend format
  String get backendValue {
    switch (this) {
      case LoadStatus.posted:
        return 'POSTED';
      case LoadStatus.biddingClosed:
        return 'BIDDING_CLOSED';
      case LoadStatus.assigned:
        return 'ASSIGNED';
      case LoadStatus.inTransit:
        return 'IN_TRANSIT';
      case LoadStatus.delivered:
        return 'DELIVERED';
      case LoadStatus.completed:
        return 'COMPLETED';
      case LoadStatus.cancelled:
        return 'CANCELLED';
    }
  }

  /// Converts from backend format to enum
  static LoadStatus fromBackendValue(String value) {
    switch (value.toUpperCase()) {
      case 'POSTED':
        return LoadStatus.posted;
      case 'BIDDING_CLOSED':
        return LoadStatus.biddingClosed;
      case 'ASSIGNED':
        return LoadStatus.assigned;
      case 'IN_TRANSIT':
        return LoadStatus.inTransit;
      case 'DELIVERED':
        return LoadStatus.delivered;
      case 'COMPLETED':
        return LoadStatus.completed;
      case 'CANCELLED':
        return LoadStatus.cancelled;
      default:
        return LoadStatus.posted;
    }
  }

  bool get isActive =>
      this == LoadStatus.posted || this == LoadStatus.biddingClosed;
  bool get isCompleted =>
      this == LoadStatus.delivered ||
      this == LoadStatus.completed ||
      this == LoadStatus.cancelled;
}

enum Priority {
  low,
  normal,
  high,
  urgent;

  String get displayName {
    switch (this) {
      case Priority.low:
        return 'Low';
      case Priority.normal:
        return 'Normal';
      case Priority.high:
        return 'High';
      case Priority.urgent:
        return 'Urgent';
    }
  }
}
