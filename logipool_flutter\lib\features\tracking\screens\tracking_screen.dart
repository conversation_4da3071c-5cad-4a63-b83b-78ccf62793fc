import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:io' show Platform;

import '../../../shared/models/tracking_model.dart';
import '../../../shared/models/load_model.dart';
import '../../../shared/services/tracking_service.dart';
import '../../../shared/widgets/unified_header.dart';
import '../../../core/di/service_locator.dart';
import '../bloc/tracking_bloc.dart';
import '../widgets/tracking_info_card.dart';
import '../widgets/tracking_status_timeline.dart';
import '../widgets/tracking_controls.dart';

class TrackingScreen extends StatefulWidget {
  final int? loadId;

  const TrackingScreen({
    super.key,
    this.loadId,
  });

  @override
  State<TrackingScreen> createState() => _TrackingScreenState();
}

class _TrackingScreenState extends State<TrackingScreen>
    with TickerProviderStateMixin {
  GoogleMapController? _mapController;
  Position? _currentPosition;
  Set<Marker> _markers = {};
  Set<Polyline> _polylines = {};
  late TabController _tabController;

  static const CameraPosition _defaultPosition = CameraPosition(
    target: LatLng(-17.8252, 31.0335), // Harare, Zimbabwe
    zoom: 10,
  );

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _requestLocationPermission();
    _getCurrentLocation();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _mapController?.dispose();
    super.dispose();
  }

  Future<void> _requestLocationPermission() async {
    final status = await Permission.location.request();
    if (status.isGranted) {
      _getCurrentLocation();
    }
  }

  Future<void> _getCurrentLocation() async {
    try {
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
      setState(() {
        _currentPosition = position;
      });

      if (_mapController != null) {
        _mapController!.animateCamera(
          CameraUpdate.newLatLng(
            LatLng(position.latitude, position.longitude),
          ),
        );
      }
    } catch (e) {
      debugPrint('Error getting location: $e');
    }
  }

  void _onMapCreated(GoogleMapController controller) {
    _mapController = controller;
    if (_currentPosition != null) {
      controller.animateCamera(
        CameraUpdate.newLatLng(
          LatLng(_currentPosition!.latitude, _currentPosition!.longitude),
        ),
      );
    }
  }

  Widget _buildMapWidget() {
    // Check if we're on Windows or if Google Maps is not supported
    if (!kIsWeb && (Platform.isWindows || Platform.isLinux)) {
      return _buildFallbackMapWidget();
    }

    // Use Google Maps for supported platforms (Android, iOS, Web)
    return GoogleMap(
      onMapCreated: _onMapCreated,
      initialCameraPosition: _defaultPosition,
      markers: _markers,
      polylines: _polylines,
      myLocationEnabled: true,
      myLocationButtonEnabled: false,
      zoomControlsEnabled: false,
      mapType: MapType.normal,
    );
  }

  Widget _buildFallbackMapWidget() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        border: Border.all(color: Colors.grey[400]!),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.map_outlined,
            size: 64,
            color: Colors.grey[600],
          ),
          const SizedBox(height: 16),
          Text(
            'Map View',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Colors.grey[700],
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'Interactive maps are not available on this platform.\nLocation data will be shown in the tracking details.',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
          ),
          const SizedBox(height: 24),
          if (_currentPosition != null)
            Card(
              margin: const EdgeInsets.symmetric(horizontal: 32),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    Text(
                      'Current Location',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Lat: ${_currentPosition!.latitude.toStringAsFixed(6)}',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    Text(
                      'Lng: ${_currentPosition!.longitude.toStringAsFixed(6)}',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  void _updateMapWithTracking(List<RealTimeLocation> locations) {
    final markers = <Marker>{};
    final polylineCoordinates = <LatLng>[];

    for (int i = 0; i < locations.length; i++) {
      final location = locations[i];
      if (location.latitude != null && location.longitude != null) {
        final latLng = LatLng(location.latitude!, location.longitude!);
        polylineCoordinates.add(latLng);

        markers.add(
          Marker(
            markerId: MarkerId('load_${location.loadId}'),
            position: latLng,
            infoWindow: InfoWindow(
              title: 'Load ${location.loadId}',
              snippet: location.status?.displayName ?? 'Unknown Status',
            ),
            icon: _getMarkerIcon(location.status),
          ),
        );
      }
    }

    setState(() {
      _markers = markers;
      _polylines = {
        if (polylineCoordinates.isNotEmpty)
          Polyline(
            polylineId: const PolylineId('tracking_route'),
            points: polylineCoordinates,
            color: Theme.of(context).primaryColor,
            width: 3,
          ),
      };
    });

    // Fit map to show all markers
    if (markers.isNotEmpty && _mapController != null) {
      _fitMapToMarkers();
    }
  }

  BitmapDescriptor _getMarkerIcon(TrackingStatus? status) {
    switch (status) {
      case TrackingStatus.inTransitToPickup:
      case TrackingStatus.inTransitToDelivery:
        return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue);
      case TrackingStatus.arrivedAtPickup:
      case TrackingStatus.arrivedAtDelivery:
        return BitmapDescriptor.defaultMarkerWithHue(
            BitmapDescriptor.hueOrange);
      case TrackingStatus.delivered:
        return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen);
      case TrackingStatus.delayed:
      case TrackingStatus.issueReported:
        return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed);
      default:
        return BitmapDescriptor.defaultMarker;
    }
  }

  void _fitMapToMarkers() {
    if (_markers.isEmpty || _mapController == null) return;

    final bounds = _calculateBounds(_markers.map((m) => m.position).toList());
    _mapController!.animateCamera(
      CameraUpdate.newLatLngBounds(bounds, 100.0),
    );
  }

  LatLngBounds _calculateBounds(List<LatLng> positions) {
    double minLat = positions.first.latitude;
    double maxLat = positions.first.latitude;
    double minLng = positions.first.longitude;
    double maxLng = positions.first.longitude;

    for (final position in positions) {
      minLat = minLat < position.latitude ? minLat : position.latitude;
      maxLat = maxLat > position.latitude ? maxLat : position.latitude;
      minLng = minLng < position.longitude ? minLng : position.longitude;
      maxLng = maxLng > position.longitude ? maxLng : position.longitude;
    }

    return LatLngBounds(
      southwest: LatLng(minLat, minLng),
      northeast: LatLng(maxLat, maxLng),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => TrackingBloc(
        trackingService: getIt<TrackingService>(),
      )..add(const ActiveLocationsRequested()),
      child: Scaffold(
        appBar: UnifiedHeader(
          title: 'Tracking',
          actions: [
            IconButton(
              icon: Icon(
                Icons.refresh,
                color: Theme.of(context).colorScheme.onPrimary,
              ),
              onPressed: () {
                context
                    .read<TrackingBloc>()
                    .add(const ActiveLocationsRequested());
              },
              tooltip: 'Refresh Tracking Data',
            ),
            IconButton(
              icon: Icon(
                Icons.my_location,
                color: Theme.of(context).colorScheme.onPrimary,
              ),
              onPressed: _getCurrentLocation,
              tooltip: 'Get Current Location',
            ),
          ],
        ),
        body: Column(
          children: [
            // Tab bar positioned just above content
            Container(
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                border: Border(
                  bottom: BorderSide(
                    color: Theme.of(context).dividerColor,
                    width: 0.5,
                  ),
                ),
              ),
              child: TabBar(
                controller: _tabController,
                tabs: const [
                  Tab(icon: Icon(Icons.map), text: 'Map'),
                  Tab(icon: Icon(Icons.timeline), text: 'Timeline'),
                  Tab(icon: Icon(Icons.dashboard), text: 'Dashboard'),
                ],
              ),
            ),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildMapView(),
                  _buildTimelineView(),
                  _buildDashboardView(),
                ],
              ),
            ),
          ],
        ),
        floatingActionButton: widget.loadId != null
            ? FloatingActionButton(
                onPressed: () => _showTrackingControls(context),
                child: const Icon(Icons.add_location),
              )
            : null,
      ),
    );
  }

  Widget _buildMapView() {
    return BlocConsumer<TrackingBloc, TrackingState>(
      listener: (context, state) {
        if (state is ActiveLocationsLoaded) {
          _updateMapWithTracking(state.locations);
        }
      },
      builder: (context, state) {
        return Stack(
          children: [
            _buildMapWidget(),
            if (state is TrackingLoading)
              const Center(
                child: CircularProgressIndicator(),
              ),
            if (state is TrackingError)
              Positioned(
                top: 16,
                left: 16,
                right: 16,
                child: Card(
                  color: Theme.of(context).colorScheme.errorContainer,
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Text(
                      state.message,
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.onErrorContainer,
                      ),
                    ),
                  ),
                ),
              ),
          ],
        );
      },
    );
  }

  Widget _buildTimelineView() {
    if (widget.loadId == null) {
      return const Center(
        child: Text('Select a load to view tracking timeline'),
      );
    }

    return BlocProvider(
      create: (context) => TrackingBloc(
        trackingService: getIt<TrackingService>(),
      )..add(TrackingHistoryFetched(loadId: widget.loadId!)),
      child: BlocBuilder<TrackingBloc, TrackingState>(
        builder: (context, state) {
          if (state is TrackingLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state is TrackingError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading tracking history',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(state.message),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      context.read<TrackingBloc>().add(
                            TrackingHistoryFetched(loadId: widget.loadId!),
                          );
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          if (state is TrackingHistoryLoaded) {
            return TrackingStatusTimeline(
              tracking: state.tracking,
              onLoadMore: state.hasMore
                  ? () => context.read<TrackingBloc>().add(
                        const TrackingLoadMoreRequested(),
                      )
                  : null,
            );
          }

          return const Center(
            child: Text('No tracking data available'),
          );
        },
      ),
    );
  }

  Widget _buildDashboardView() {
    return BlocProvider(
      create: (context) => TrackingBloc(
        trackingService: getIt<TrackingService>(),
      )..add(const TrackingStatisticsRequested()),
      child: BlocBuilder<TrackingBloc, TrackingState>(
        builder: (context, state) {
          if (state is TrackingLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state is TrackingError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading statistics',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(state.message),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      context.read<TrackingBloc>().add(
                            const TrackingStatisticsRequested(),
                          );
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          if (state is TrackingStatisticsLoaded) {
            return _buildStatisticsDashboard(state.statistics);
          }

          return const Center(
            child: Text('No statistics available'),
          );
        },
      ),
    );
  }

  Widget _buildStatisticsDashboard(TrackingStatistics statistics) {
            final screenWidth = MediaQuery.of(context).size.width;
    final itemWidth = screenWidth / (screenWidth > 768 ? 2 : 1);
    final itemHeight = 150 ; // desired height
    final aspectRatio = itemWidth / itemHeight;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Tracking Overview',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 16),
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: aspectRatio,
            children: [
              _buildStatCard(
                'Total Loads',
                statistics.totalLoads.toString(),
                Icons.local_shipping,
                Colors.blue,
              ),
              _buildStatCard(
                'In Transit to Pickup',
                statistics.loadsInTransitToPickup.toString(),
                Icons.departure_board,
                Colors.orange,
              ),
              _buildStatCard(
                'In Transit to Delivery',
                statistics.loadsInTransitToDelivery.toString(),
                Icons.local_shipping,
                Colors.purple,
              ),
              _buildStatCard(
                'Delivered',
                statistics.loadsDelivered.toString(),
                Icons.check_circle,
                Colors.green,
              ),
              _buildStatCard(
                'Delayed',
                statistics.loadsDelayed.toString(),
                Icons.schedule,
                Colors.amber,
              ),
              _buildStatCard(
                'Issues Reported',
                statistics.loadsWithIssues.toString(),
                Icons.warning,
                Colors.red,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    color: color,
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
      ),
    );
  }

  void _showTrackingControls(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => TrackingControls(
        loadId: widget.loadId!,
        onTrackingUpdated: () {
          context.read<TrackingBloc>().add(const ActiveLocationsRequested());
        },
      ),
    );
  }
}
