import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../shared/models/admin_model.dart';
import '../../../shared/models/paginated_response.dart';
import '../../../shared/services/admin_api_service.dart';

// Events
abstract class AdminEvent extends Equatable {
  const AdminEvent();

  @override
  List<Object?> get props => [];
}

class LoadDashboard extends AdminEvent {
  const LoadDashboard();
}

class LoadUsers extends AdminEvent {
  final int page;
  final String? status;
  final String? role;
  final String? search;
  final bool refresh;

  const LoadUsers({
    this.page = 0,
    this.status,
    this.role,
    this.search,
    this.refresh = false,
  });

  @override
  List<Object?> get props => [page, status, role, search, refresh];
}

class LoadCompanies extends AdminEvent {
  final int page;
  final String? verificationStatus;
  final String? search;
  final bool refresh;

  const LoadCompanies({
    this.page = 0,
    this.verificationStatus,
    this.search,
    this.refresh = false,
  });

  @override
  List<Object?> get props => [page, verificationStatus, search, refresh];
}

class LoadLoads extends AdminEvent {
  final int page;
  final String? status;
  final String? search;
  final bool refresh;

  const LoadLoads({
    this.page = 0,
    this.status,
    this.search,
    this.refresh = false,
  });

  @override
  List<Object?> get props => [page, status, search, refresh];
}

class LoadAnalytics extends AdminEvent {
  const LoadAnalytics();
}

class LoadCompanyDetails extends AdminEvent {
  final int companyId;

  const LoadCompanyDetails({required this.companyId});

  @override
  List<Object?> get props => [companyId];
}

class LoadAuditLogs extends AdminEvent {
  final int page;
  final String? action;
  final String? entityType;
  final String? performedBy;
  final DateTime? startDate;
  final DateTime? endDate;
  final bool refresh;

  const LoadAuditLogs({
    this.page = 0,
    this.action,
    this.entityType,
    this.performedBy,
    this.startDate,
    this.endDate,
    this.refresh = false,
  });

  @override
  List<Object?> get props => [page, action, entityType, performedBy, startDate, endDate, refresh];
}

class LoadSystemAlerts extends AdminEvent {
  final int page;
  final String? severity;
  final bool? acknowledged;
  final bool refresh;

  const LoadSystemAlerts({
    this.page = 0,
    this.severity,
    this.acknowledged,
    this.refresh = false,
  });

  @override
  List<Object?> get props => [page, severity, acknowledged, refresh];
}

class LoadSystemConfiguration extends AdminEvent {
  const LoadSystemConfiguration();
}

class UpdateUserStatus extends AdminEvent {
  final int userId;
  final String status;

  const UpdateUserStatus({required this.userId, required this.status});

  @override
  List<Object?> get props => [userId, status];
}

class UpdateUserRoles extends AdminEvent {
  final int userId;
  final List<String> roles;

  const UpdateUserRoles({required this.userId, required this.roles});

  @override
  List<Object?> get props => [userId, roles];
}

class DeleteUser extends AdminEvent {
  final int userId;

  const DeleteUser({required this.userId});

  @override
  List<Object?> get props => [userId];
}

class VerifyCompany extends AdminEvent {
  final int companyId;
  final String status;
  final String? notes;

  const VerifyCompany({required this.companyId, required this.status, this.notes});

  @override
  List<Object?> get props => [companyId, status, notes];
}

class SuspendCompany extends AdminEvent {
  final int companyId;
  final String reason;
  final String? notes;

  const SuspendCompany({
    required this.companyId,
    required this.reason,
    this.notes,
  });

  @override
  List<Object?> get props => [companyId, reason, notes];
}

class UnsuspendCompany extends AdminEvent {
  final int companyId;
  final String? reason;
  final String? notes;

  const UnsuspendCompany({
    required this.companyId,
    this.reason,
    this.notes,
  });

  @override
  List<Object?> get props => [companyId, reason, notes];
}

class DeleteCompany extends AdminEvent {
  final int companyId;

  const DeleteCompany({required this.companyId});

  @override
  List<Object?> get props => [companyId];
}

class AssignLoadToCompany extends AdminEvent {
  final int loadId;
  final int companyId;

  const AssignLoadToCompany({required this.loadId, required this.companyId});

  @override
  List<Object?> get props => [loadId, companyId];
}

class DeleteLoad extends AdminEvent {
  final int loadId;

  const DeleteLoad({required this.loadId});

  @override
  List<Object?> get props => [loadId];
}

class AcknowledgeAlert extends AdminEvent {
  final int alertId;

  const AcknowledgeAlert({required this.alertId});

  @override
  List<Object?> get props => [alertId];
}

class InitiateSystemBackup extends AdminEvent {
  final String backupType;

  const InitiateSystemBackup({required this.backupType});

  @override
  List<Object?> get props => [backupType];
}

class UpdateSystemConfiguration extends AdminEvent {
  final Map<String, dynamic> settings;

  const UpdateSystemConfiguration({required this.settings});

  @override
  List<Object?> get props => [settings];
}

// States
abstract class AdminState extends Equatable {
  const AdminState();

  @override
  List<Object?> get props => [];
}

class AdminInitial extends AdminState {
  const AdminInitial();
}

class AdminLoading extends AdminState {
  const AdminLoading();
}

class AdminError extends AdminState {
  final String message;

  const AdminError({required this.message});

  @override
  List<Object?> get props => [message];
}

class AdminOperationSuccess extends AdminState {
  final String message;

  const AdminOperationSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

class DashboardLoaded extends AdminState {
  final DashboardOverview dashboard;

  const DashboardLoaded({required this.dashboard});

  @override
  List<Object?> get props => [dashboard];
}

class UsersLoaded extends AdminState {
  final PaginatedResponse<UserManagementResponse> users;
  final bool hasReachedMax;

  const UsersLoaded({required this.users, this.hasReachedMax = false});

  @override
  List<Object?> get props => [users, hasReachedMax];
}

class CompaniesLoaded extends AdminState {
  final PaginatedResponse<CompanyManagementResponse> companies;
  final bool hasReachedMax;

  const CompaniesLoaded({required this.companies, this.hasReachedMax = false});

  @override
  List<Object?> get props => [companies, hasReachedMax];
}

class CompanyDetailsLoaded extends AdminState {
  final CompanyManagementResponse company;

  const CompanyDetailsLoaded({required this.company});

  @override
  List<Object?> get props => [company];
}

class LoadsLoaded extends AdminState {
  final PaginatedResponse<LoadManagementResponse> loads;
  final bool hasReachedMax;

  const LoadsLoaded({required this.loads, this.hasReachedMax = false});

  @override
  List<Object?> get props => [loads, hasReachedMax];
}

class AnalyticsLoaded extends AdminState {
  final SystemAnalytics analytics;

  const AnalyticsLoaded({required this.analytics});

  @override
  List<Object?> get props => [analytics];
}

class AuditLogsLoaded extends AdminState {
  final PaginatedResponse<AuditLogResponse> auditLogs;
  final bool hasReachedMax;

  const AuditLogsLoaded({required this.auditLogs, this.hasReachedMax = false});

  @override
  List<Object?> get props => [auditLogs, hasReachedMax];
}

class SystemAlertsLoaded extends AdminState {
  final PaginatedResponse<SystemAlertResponse> alerts;
  final bool hasReachedMax;

  const SystemAlertsLoaded({required this.alerts, this.hasReachedMax = false});

  @override
  List<Object?> get props => [alerts, hasReachedMax];
}

class SystemConfigurationLoaded extends AdminState {
  final SystemConfigurationResponse configuration;

  const SystemConfigurationLoaded({required this.configuration});

  @override
  List<Object?> get props => [configuration];
}

// BLoC
class AdminBloc extends Bloc<AdminEvent, AdminState> {
  final AdminApiService _adminApiService;

  AdminBloc(this._adminApiService) : super(const AdminInitial()) {
    on<LoadDashboard>(_onLoadDashboard);
    on<LoadUsers>(_onLoadUsers);
    on<LoadCompanies>(_onLoadCompanies);
    on<LoadCompanyDetails>(_onLoadCompanyDetails);
    on<LoadLoads>(_onLoadLoads);
    on<LoadAnalytics>(_onLoadAnalytics);
    on<LoadAuditLogs>(_onLoadAuditLogs);
    on<LoadSystemAlerts>(_onLoadSystemAlerts);
    on<LoadSystemConfiguration>(_onLoadSystemConfiguration);
    on<UpdateUserStatus>(_onUpdateUserStatus);
    on<UpdateUserRoles>(_onUpdateUserRoles);
    on<DeleteUser>(_onDeleteUser);
    on<VerifyCompany>(_onVerifyCompany);
    on<SuspendCompany>(_onSuspendCompany);
    on<UnsuspendCompany>(_onUnsuspendCompany);
    on<DeleteCompany>(_onDeleteCompany);
    on<AssignLoadToCompany>(_onAssignLoadToCompany);
    on<DeleteLoad>(_onDeleteLoad);
    on<AcknowledgeAlert>(_onAcknowledgeAlert);
    on<InitiateSystemBackup>(_onInitiateSystemBackup);
    on<UpdateSystemConfiguration>(_onUpdateSystemConfiguration);
  }

  Future<void> _onLoadDashboard(LoadDashboard event, Emitter<AdminState> emit) async {
    emit(const AdminLoading());
    try {
      final dashboard = await _adminApiService.getDashboardOverview();
      emit(DashboardLoaded(dashboard: dashboard));
    } catch (e) {
      emit(AdminError(message: e.toString()));
    }
  }

  Future<void> _onLoadUsers(LoadUsers event, Emitter<AdminState> emit) async {
    if (event.refresh) emit(const AdminLoading());

    try {
      final users = await _adminApiService.getAllUsers(
        page: event.page,
        status: event.status,
        role: event.role,
        search: event.search,
      );

      final hasReachedMax = users.content.length < 20;
      emit(UsersLoaded(users: users, hasReachedMax: hasReachedMax));
    } catch (e) {
      emit(AdminError(message: e.toString()));
    }
  }

  Future<void> _onLoadCompanies(LoadCompanies event, Emitter<AdminState> emit) async {
    if (event.refresh) emit(const AdminLoading());

    try {
      final companies = await _adminApiService.getAllCompanies(
        page: event.page,
        verificationStatus: event.verificationStatus,
        search: event.search,
      );

      final hasReachedMax = companies.content.length < 20;
      emit(CompaniesLoaded(companies: companies, hasReachedMax: hasReachedMax));
    } catch (e) {
      emit(AdminError(message: e.toString()));
    }
  }

  Future<void> _onLoadCompanyDetails(LoadCompanyDetails event, Emitter<AdminState> emit) async {
    emit(const AdminLoading());

    try {
      final company = await _adminApiService.getCompanyById(event.companyId);
      emit(CompanyDetailsLoaded(company: company));
    } catch (e) {
      emit(AdminError(message: e.toString()));
    }
  }

  Future<void> _onLoadLoads(LoadLoads event, Emitter<AdminState> emit) async {
    if (event.refresh) emit(const AdminLoading());

    try {
      final loads = await _adminApiService.getAllLoads(
        page: event.page,
        status: event.status,
        search: event.search,
      );

      final hasReachedMax = loads.content.length < 20;
      emit(LoadsLoaded(loads: loads, hasReachedMax: hasReachedMax));
    } catch (e) {
      emit(AdminError(message: e.toString()));
    }
  }

  Future<void> _onLoadAnalytics(LoadAnalytics event, Emitter<AdminState> emit) async {
    emit(const AdminLoading());
    try {
      final analytics = await _adminApiService.getSystemAnalytics();
      emit(AnalyticsLoaded(analytics: analytics));
    } catch (e) {
      emit(AdminError(message: e.toString()));
    }
  }

  Future<void> _onLoadAuditLogs(LoadAuditLogs event, Emitter<AdminState> emit) async {
    if (event.refresh) emit(const AdminLoading());

    try {
      final auditLogs = await _adminApiService.getAuditLogs(
        page: event.page,
        action: event.action,
        entityType: event.entityType,
        performedBy: event.performedBy,
        startDate: event.startDate,
        endDate: event.endDate,
      );

      final hasReachedMax = auditLogs.content.length < 20;
      emit(AuditLogsLoaded(auditLogs: auditLogs, hasReachedMax: hasReachedMax));
    } catch (e) {
      emit(AdminError(message: e.toString()));
    }
  }

  Future<void> _onLoadSystemAlerts(LoadSystemAlerts event, Emitter<AdminState> emit) async {
    if (event.refresh) emit(const AdminLoading());

    try {
      final alerts = await _adminApiService.getSystemAlerts(
        page: event.page,
        severity: event.severity,
        acknowledged: event.acknowledged,
      );

      final hasReachedMax = alerts.content.length < 20;
      emit(SystemAlertsLoaded(alerts: alerts, hasReachedMax: hasReachedMax));
    } catch (e) {
      emit(AdminError(message: e.toString()));
    }
  }

  Future<void> _onLoadSystemConfiguration(LoadSystemConfiguration event, Emitter<AdminState> emit) async {
    emit(const AdminLoading());
    try {
      final configuration = await _adminApiService.getSystemConfiguration();
      emit(SystemConfigurationLoaded(configuration: configuration));
    } catch (e) {
      emit(AdminError(message: e.toString()));
    }
  }

  Future<void> _onUpdateUserStatus(UpdateUserStatus event, Emitter<AdminState> emit) async {
    try {
      await _adminApiService.updateUserStatus(event.userId, event.status);
      emit(const AdminOperationSuccess(message: 'User status updated successfully'));
    } catch (e) {
      emit(AdminError(message: e.toString()));
    }
  }

  Future<void> _onUpdateUserRoles(UpdateUserRoles event, Emitter<AdminState> emit) async {
    try {
      await _adminApiService.updateUserRoles(event.userId, event.roles);
      emit(const AdminOperationSuccess(message: 'User roles updated successfully'));
    } catch (e) {
      emit(AdminError(message: e.toString()));
    }
  }

  Future<void> _onDeleteUser(DeleteUser event, Emitter<AdminState> emit) async {
    try {
      await _adminApiService.deleteUser(event.userId);
      emit(const AdminOperationSuccess(message: 'User deleted successfully'));
    } catch (e) {
      emit(AdminError(message: e.toString()));
    }
  }

  Future<void> _onVerifyCompany(VerifyCompany event, Emitter<AdminState> emit) async {
    try {
      await _adminApiService.verifyCompany(event.companyId, event.status, notes: event.notes);
      emit(const AdminOperationSuccess(message: 'Company verification updated successfully'));
    } catch (e) {
      emit(AdminError(message: e.toString()));
    }
  }

  Future<void> _onSuspendCompany(SuspendCompany event, Emitter<AdminState> emit) async {
    try {
      await _adminApiService.suspendCompany(event.companyId, event.reason, notes: event.notes);
      emit(const AdminOperationSuccess(message: 'Company suspended successfully'));
    } catch (e) {
      emit(AdminError(message: e.toString()));
    }
  }

  Future<void> _onUnsuspendCompany(UnsuspendCompany event, Emitter<AdminState> emit) async {
    try {
      await _adminApiService.unsuspendCompany(event.companyId, reason: event.reason, notes: event.notes);
      emit(const AdminOperationSuccess(message: 'Company unsuspended successfully'));
    } catch (e) {
      emit(AdminError(message: e.toString()));
    }
  }

  Future<void> _onDeleteCompany(DeleteCompany event, Emitter<AdminState> emit) async {
    try {
      await _adminApiService.deleteCompany(event.companyId);
      emit(const AdminOperationSuccess(message: 'Company deleted successfully'));
    } catch (e) {
      emit(AdminError(message: e.toString()));
    }
  }

  Future<void> _onAssignLoadToCompany(AssignLoadToCompany event, Emitter<AdminState> emit) async {
    try {
      await _adminApiService.assignLoadToCompany(event.loadId, event.companyId);
      emit(const AdminOperationSuccess(message: 'Load assigned successfully'));
    } catch (e) {
      emit(AdminError(message: e.toString()));
    }
  }

  Future<void> _onDeleteLoad(DeleteLoad event, Emitter<AdminState> emit) async {
    try {
      await _adminApiService.deleteLoad(event.loadId);
      emit(const AdminOperationSuccess(message: 'Load deleted successfully'));
    } catch (e) {
      emit(AdminError(message: e.toString()));
    }
  }

  Future<void> _onAcknowledgeAlert(AcknowledgeAlert event, Emitter<AdminState> emit) async {
    try {
      await _adminApiService.acknowledgeAlert(event.alertId);
      emit(const AdminOperationSuccess(message: 'Alert acknowledged successfully'));
    } catch (e) {
      emit(AdminError(message: e.toString()));
    }
  }

  Future<void> _onInitiateSystemBackup(InitiateSystemBackup event, Emitter<AdminState> emit) async {
    try {
      await _adminApiService.initiateSystemBackup(event.backupType);
      emit(const AdminOperationSuccess(message: 'System backup initiated successfully'));
    } catch (e) {
      emit(AdminError(message: e.toString()));
    }
  }

  Future<void> _onUpdateSystemConfiguration(UpdateSystemConfiguration event, Emitter<AdminState> emit) async {
    try {
      await _adminApiService.updateSystemConfiguration(event.settings);
      emit(const AdminOperationSuccess(message: 'System configuration updated successfully'));
    } catch (e) {
      emit(AdminError(message: e.toString()));
    }
  }
}
