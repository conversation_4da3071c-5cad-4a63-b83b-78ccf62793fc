package zw.co.kanjan.logipool.dto.driver;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import zw.co.kanjan.logipool.entity.CompanyMember;

import java.time.LocalDateTime;
import java.util.List;

public class DriverDto {
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Request to register a new driver")
    public static class DriverRegistrationRequest {
        
        @NotBlank(message = "First name is required")
        @Size(max = 50, message = "First name should not exceed 50 characters")
        @Schema(description = "Driver's first name", example = "John")
        private String firstName;
        
        @NotBlank(message = "Last name is required")
        @Size(max = 50, message = "Last name should not exceed 50 characters")
        @Schema(description = "Driver's last name", example = "Doe")
        private String lastName;
        
        @NotBlank(message = "Email is required")
        @Email(message = "Email should be valid")
        @Size(max = 100, message = "Email should not exceed 100 characters")
        @Schema(description = "Driver's email address", example = "<EMAIL>")
        private String email;
        
        @NotBlank(message = "Password is required")
        @Size(min = 6, max = 40, message = "Password should be between 6 and 40 characters")
        @Schema(description = "Driver's password", example = "password123")
        private String password;
        
        @Size(max = 20, message = "Phone number should not exceed 20 characters")
        @Schema(description = "Driver's phone number", example = "+263771234567")
        private String phoneNumber;
        
        @Size(max = 50, message = "License number should not exceed 50 characters")
        @Schema(description = "Driver's license number", example = "DL123456789")
        private String licenseNumber;
        
        @Schema(description = "Driver's license class", example = "C")
        private String licenseClass;
        
        @Schema(description = "Driver's license expiry date")
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime licenseExpiryDate;
        
        @Size(max = 50, message = "National ID should not exceed 50 characters")
        @Schema(description = "Driver's national ID number", example = "63-123456-A-12")
        private String nationalId;
        
        @Size(max = 50, message = "Passport number should not exceed 50 characters")
        @Schema(description = "Driver's passport number (optional)", example = "AN123456")
        private String passportNumber;
        
        @Schema(description = "Company ID to assign driver to (optional)")
        private Long companyId;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Driver profile response")
    public static class DriverResponse {
        
        @Schema(description = "Driver's user ID", example = "1")
        private Long id;
        
        @Schema(description = "Driver's first name", example = "John")
        private String firstName;
        
        @Schema(description = "Driver's last name", example = "Doe")
        private String lastName;
        
        @Schema(description = "Driver's email address", example = "<EMAIL>")
        private String email;
        
        @Schema(description = "Driver's phone number", example = "+263771234567")
        private String phoneNumber;
        
        @Schema(description = "Driver's license number", example = "DL123456789")
        private String licenseNumber;
        
        @Schema(description = "Driver's license class", example = "C")
        private String licenseClass;
        
        @Schema(description = "Driver's license expiry date")
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime licenseExpiryDate;
        
        @Schema(description = "Driver's national ID number", example = "63-123456-A-12")
        private String nationalId;
        
        @Schema(description = "Driver's passport number", example = "AN123456")
        private String passportNumber;
        
        @Schema(description = "Driver verification status")
        private DriverVerificationStatus verificationStatus;
        
        @Schema(description = "Company information if driver is assigned to a company")
        private CompanyInfo company;
        
        @Schema(description = "Driver's account creation date")
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime createdAt;
        
        @Schema(description = "Driver's last login date")
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime lastLoginAt;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Driver profile update request")
    public static class DriverUpdateRequest {
        
        @Size(max = 50, message = "First name should not exceed 50 characters")
        @Schema(description = "Driver's first name", example = "John")
        private String firstName;
        
        @Size(max = 50, message = "Last name should not exceed 50 characters")
        @Schema(description = "Driver's last name", example = "Doe")
        private String lastName;
        
        @Size(max = 20, message = "Phone number should not exceed 20 characters")
        @Schema(description = "Driver's phone number", example = "+263771234567")
        private String phoneNumber;
        
        @Size(max = 50, message = "License number should not exceed 50 characters")
        @Schema(description = "Driver's license number", example = "DL123456789")
        private String licenseNumber;
        
        @Schema(description = "Driver's license class", example = "C")
        private String licenseClass;
        
        @Schema(description = "Driver's license expiry date")
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime licenseExpiryDate;
        
        @Size(max = 50, message = "National ID should not exceed 50 characters")
        @Schema(description = "Driver's national ID number", example = "63-123456-A-12")
        private String nationalId;
        
        @Size(max = 50, message = "Passport number should not exceed 50 characters")
        @Schema(description = "Driver's passport number", example = "AN123456")
        private String passportNumber;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Request to assign driver to company")
    public static class AssignToCompanyRequest {
        
        @NotNull(message = "Driver ID is required")
        @Schema(description = "Driver's user ID", example = "1")
        private Long driverId;
        
        @NotNull(message = "Company ID is required")
        @Schema(description = "Company ID to assign driver to", example = "1")
        private Long companyId;
        
        @Schema(description = "Driver's role in the company", example = "DRIVER")
        @Builder.Default
        private CompanyMember.CompanyRole role = CompanyMember.CompanyRole.DRIVER;
        
        @Schema(description = "Whether the driver can update load status", example = "true")
        @Builder.Default
        private Boolean canUpdateLoadStatus = true;
        
        @Schema(description = "Whether the driver can track location", example = "true")
        @Builder.Default
        private Boolean canTrackLocation = true;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Driver verification status")
    public static class DriverVerificationStatus {
        
        @Schema(description = "License verification status")
        private VerificationTaskStatus licenseVerification;
        
        @Schema(description = "Defensive training verification status")
        private VerificationTaskStatus defensiveTrainingVerification;
        
        @Schema(description = "National ID verification status")
        private VerificationTaskStatus nationalIdVerification;
        
        @Schema(description = "Passport verification status")
        private VerificationTaskStatus passportVerification;
        
        @Schema(description = "Phone number verification status")
        private VerificationTaskStatus phoneVerification;
        
        @Schema(description = "Overall verification status")
        private OverallVerificationStatus overallStatus;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Individual verification task status")
    public static class VerificationTaskStatus {
        
        @Schema(description = "Verification status", example = "PENDING")
        private VerificationStatus status;
        
        @Schema(description = "Verification notes or comments")
        private String notes;
        
        @Schema(description = "Verified by admin user ID")
        private Long verifiedBy;
        
        @Schema(description = "Verification date")
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime verifiedAt;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Company information")
    public static class CompanyInfo {
        
        @Schema(description = "Company ID", example = "1")
        private Long id;
        
        @Schema(description = "Company name", example = "ABC Transport")
        private String name;
        
        @Schema(description = "Driver's role in the company", example = "DRIVER")
        private CompanyMember.CompanyRole role;
        
        @Schema(description = "Driver's membership status", example = "ACTIVE")
        private CompanyMember.MemberStatus status;
    }
    
    public enum VerificationStatus {
        PENDING,
        VERIFIED,
        REJECTED,
        EXPIRED,
        NOT_REQUIRED
    }
    
    public enum OverallVerificationStatus {
        UNVERIFIED,
        PARTIALLY_VERIFIED,
        FULLY_VERIFIED,
        VERIFICATION_EXPIRED
    }
}
