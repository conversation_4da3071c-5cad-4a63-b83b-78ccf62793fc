package zw.co.kanjan.logipool.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import zw.co.kanjan.logipool.dto.fleet.TruckHeadDto;
import zw.co.kanjan.logipool.entity.Company;
import zw.co.kanjan.logipool.entity.TruckHead;
import zw.co.kanjan.logipool.entity.User;
import zw.co.kanjan.logipool.exception.BusinessException;
import zw.co.kanjan.logipool.exception.ResourceNotFoundException;
import zw.co.kanjan.logipool.mapper.fleet.TruckHeadMapper;
import zw.co.kanjan.logipool.repository.CompanyRepository;
import zw.co.kanjan.logipool.repository.TruckHeadRepository;
import zw.co.kanjan.logipool.repository.UserRepository;

import java.time.LocalDateTime;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class TruckHeadService {
    
    private final TruckHeadRepository truckHeadRepository;
    private final CompanyRepository companyRepository;
    private final UserRepository userRepository;
    private final TruckHeadMapper truckHeadMapper;
    
    public TruckHeadDto.TruckHeadResponse createTruckHead(TruckHeadDto.TruckHeadCreateRequest request, String username) {
        User user = getUserByUsername(username);
        Company company = getCompanyByUser(user);
        
        // Validate registration number uniqueness
        if (truckHeadRepository.existsByRegistrationNumber(request.getRegistrationNumber())) {
            throw new BusinessException("Truck head with registration number " + request.getRegistrationNumber() + " already exists");
        }
        
        TruckHead truckHead = truckHeadMapper.toEntity(request);
        truckHead.setCompany(company);
        truckHead.setStatus(TruckHead.TruckStatus.AVAILABLE);
        
        TruckHead savedTruckHead = truckHeadRepository.save(truckHead);
        log.info("Truck head created successfully: {} for company: {}", savedTruckHead.getRegistrationNumber(), company.getName());
        
        return truckHeadMapper.toResponse(savedTruckHead);
    }
    
    public TruckHeadDto.TruckHeadResponse updateTruckHead(Long id, TruckHeadDto.TruckHeadUpdateRequest request, String username) {
        User user = getUserByUsername(username);
        Company company = getCompanyByUser(user);
        
        TruckHead truckHead = truckHeadRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Truck head not found with id: " + id));
        
        // Validate ownership
        if (!truckHead.getCompany().getId().equals(company.getId())) {
            throw new BusinessException("You can only update your own truck heads");
        }
        
        // Update fields
        updateTruckHeadFromRequest(truckHead, request);
        
        TruckHead savedTruckHead = truckHeadRepository.save(truckHead);
        log.info("Truck head updated successfully: {}", savedTruckHead.getRegistrationNumber());
        
        return truckHeadMapper.toResponse(savedTruckHead);
    }
    
    @Transactional(readOnly = true)
    public TruckHeadDto.TruckHeadResponse getTruckHead(Long id, String username) {
        User user = getUserByUsername(username);
        Company company = getCompanyByUser(user);
        
        TruckHead truckHead = truckHeadRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Truck head not found with id: " + id));
        
        // Validate ownership
        if (!truckHead.getCompany().getId().equals(company.getId())) {
            throw new BusinessException("You can only view your own truck heads");
        }
        
        return truckHeadMapper.toResponse(truckHead);
    }
    
    @Transactional(readOnly = true)
    public Page<TruckHeadDto.TruckHeadResponse> getTruckHeads(String username, Pageable pageable,
                                                             TruckHead.TruckStatus status, String make, String model, String registrationNumber) {
        User user = getUserByUsername(username);
        Company company = getCompanyByUser(user);
        
        Page<TruckHead> truckHeads = truckHeadRepository.findByCompanyWithFilters(
                company, status, make, model, registrationNumber, pageable);
        
        return truckHeads.map(truckHeadMapper::toResponse);
    }
    
    @Transactional(readOnly = true)
    public List<TruckHeadDto.TruckHeadResponse> getAvailableTruckHeads(String username) {
        User user = getUserByUsername(username);
        Company company = getCompanyByUser(user);
        
        List<TruckHead> availableTruckHeads = truckHeadRepository.findAvailableByCompany(company);
        return availableTruckHeads.stream()
                .map(truckHeadMapper::toResponse)
                .toList();
    }
    
    public void deleteTruckHead(Long id, String username) {
        User user = getUserByUsername(username);
        Company company = getCompanyByUser(user);
        
        TruckHead truckHead = truckHeadRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Truck head not found with id: " + id));
        
        // Validate ownership
        if (!truckHead.getCompany().getId().equals(company.getId())) {
            throw new BusinessException("You can only delete your own truck heads");
        }
        
        // Check if truck head is currently assigned
        if (truckHead.getStatus() == TruckHead.TruckStatus.ASSIGNED || 
            truckHead.getStatus() == TruckHead.TruckStatus.IN_TRANSIT) {
            throw new BusinessException("Cannot delete truck head that is currently assigned or in transit");
        }
        
        truckHeadRepository.delete(truckHead);
        log.info("Truck head deleted successfully: {}", truckHead.getRegistrationNumber());
    }
    
    public TruckHeadDto.TruckHeadResponse updateTruckHeadStatus(Long id, TruckHead.TruckStatus status, String username) {
        User user = getUserByUsername(username);
        Company company = getCompanyByUser(user);
        
        TruckHead truckHead = truckHeadRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Truck head not found with id: " + id));
        
        // Validate ownership
        if (!truckHead.getCompany().getId().equals(company.getId())) {
            throw new BusinessException("You can only update your own truck heads");
        }
        
        truckHead.setStatus(status);
        TruckHead savedTruckHead = truckHeadRepository.save(truckHead);
        
        log.info("Truck head status updated: {} to {}", savedTruckHead.getRegistrationNumber(), status);
        return truckHeadMapper.toResponse(savedTruckHead);
    }
    
    @Transactional(readOnly = true)
    public List<TruckHeadDto.TruckHeadResponse> getTruckHeadsExpiringBetween(LocalDateTime startDate, LocalDateTime endDate, String type) {
        List<TruckHead> truckHeads;
        
        switch (type.toLowerCase()) {
            case "insurance":
                truckHeads = truckHeadRepository.findByInsuranceExpiryDateBetween(startDate, endDate);
                break;
            case "fitness":
                truckHeads = truckHeadRepository.findByFitnessExpiryDateBetween(startDate, endDate);
                break;
            case "permit":
                truckHeads = truckHeadRepository.findByRoadPermitExpiryDateBetween(startDate, endDate);
                break;
            case "tax":
                truckHeads = truckHeadRepository.findByTaxClearanceExpiryDateBetween(startDate, endDate);
                break;
            default:
                throw new BusinessException("Invalid expiry type: " + type);
        }
        
        return truckHeads.stream()
                .map(truckHeadMapper::toResponse)
                .toList();
    }
    
    private User getUserByUsername(String username) {
        return userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
    }
    
    private Company getCompanyByUser(User user) {
        if (user.getCompany() == null) {
            throw new BusinessException("User must be associated with a company");
        }
        return user.getCompany();
    }
    
    private void updateTruckHeadFromRequest(TruckHead truckHead, TruckHeadDto.TruckHeadUpdateRequest request) {
        if (request.getMake() != null) truckHead.setMake(request.getMake());
        if (request.getModel() != null) truckHead.setModel(request.getModel());
        if (request.getYear() != null) truckHead.setYear(request.getYear());
        if (request.getEngineNumber() != null) truckHead.setEngineNumber(request.getEngineNumber());
        if (request.getChassisNumber() != null) truckHead.setChassisNumber(request.getChassisNumber());
        if (request.getStatus() != null) truckHead.setStatus(request.getStatus());
        if (request.getFuelType() != null) truckHead.setFuelType(request.getFuelType());
        if (request.getFuelCapacity() != null) truckHead.setFuelCapacity(request.getFuelCapacity());
        if (request.getMaxTowingCapacity() != null) truckHead.setMaxTowingCapacity(request.getMaxTowingCapacity());
        if (request.getCapacityUnit() != null) truckHead.setCapacityUnit(request.getCapacityUnit());
        if (request.getDescription() != null) truckHead.setDescription(request.getDescription());
        if (request.getImageUrl() != null) truckHead.setImageUrl(request.getImageUrl());
        if (request.getCurrentLocation() != null) truckHead.setCurrentLocation(request.getCurrentLocation());
        if (request.getCurrentLatitude() != null) truckHead.setCurrentLatitude(request.getCurrentLatitude());
        if (request.getCurrentLongitude() != null) truckHead.setCurrentLongitude(request.getCurrentLongitude());
        if (request.getHasInsurance() != null) truckHead.setHasInsurance(request.getHasInsurance());
        if (request.getHasFitnessCertificate() != null) truckHead.setHasFitnessCertificate(request.getHasFitnessCertificate());
        if (request.getHasRoadPermit() != null) truckHead.setHasRoadPermit(request.getHasRoadPermit());
        if (request.getHasTaxClearance() != null) truckHead.setHasTaxClearance(request.getHasTaxClearance());
        if (request.getInsuranceExpiryDate() != null) truckHead.setInsuranceExpiryDate(request.getInsuranceExpiryDate());
        if (request.getFitnessExpiryDate() != null) truckHead.setFitnessExpiryDate(request.getFitnessExpiryDate());
        if (request.getRoadPermitExpiryDate() != null) truckHead.setRoadPermitExpiryDate(request.getRoadPermitExpiryDate());
        if (request.getTaxClearanceExpiryDate() != null) truckHead.setTaxClearanceExpiryDate(request.getTaxClearanceExpiryDate());
        if (request.getLastMaintenanceDate() != null) truckHead.setLastMaintenanceDate(request.getLastMaintenanceDate());
        if (request.getNextMaintenanceDate() != null) truckHead.setNextMaintenanceDate(request.getNextMaintenanceDate());
        
        if (request.getCurrentDriverId() != null) {
            User driver = userRepository.findById(request.getCurrentDriverId())
                    .orElseThrow(() -> new ResourceNotFoundException("Driver not found"));
            truckHead.setCurrentDriver(driver);
        }
    }
}
