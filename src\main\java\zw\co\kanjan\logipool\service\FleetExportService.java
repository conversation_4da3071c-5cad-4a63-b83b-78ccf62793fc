package zw.co.kanjan.logipool.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import zw.co.kanjan.logipool.entity.*;
import zw.co.kanjan.logipool.exception.BusinessException;
import zw.co.kanjan.logipool.exception.ResourceNotFoundException;
import zw.co.kanjan.logipool.repository.*;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional(readOnly = true)
public class FleetExportService {
    
    private final TruckHeadRepository truckHeadRepository;
    private final TrailerRepository trailerRepository;
    private final FleetAssignmentRepository fleetAssignmentRepository;
    private final InsuranceRepository insuranceRepository;
    private final TaxClearanceRepository taxClearanceRepository;
    private final PermitRepository permitRepository;
    private final UserRepository userRepository;
    private final CompanyRepository companyRepository;
    
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    public byte[] exportFleetData(String username, String exportType, LocalDateTime startDate, LocalDateTime endDate) {
        User user = getUserByUsername(username);
        Company company = getCompanyByUser(user);
        
        try (Workbook workbook = new XSSFWorkbook()) {
            switch (exportType.toLowerCase()) {
                case "fleet_overview":
                    return exportFleetOverview(workbook, company);
                case "compliance_report":
                    return exportComplianceReport(workbook, company, startDate, endDate);
                case "utilization_report":
                    return exportUtilizationReport(workbook, company, startDate, endDate);
                case "maintenance_report":
                    return exportMaintenanceReport(workbook, company, startDate, endDate);
                case "complete_fleet_data":
                    return exportCompleteFleetData(workbook, company);
                default:
                    throw new BusinessException("Invalid export type: " + exportType);
            }
        } catch (IOException e) {
            log.error("Error creating fleet export: {}", e.getMessage());
            throw new BusinessException("Failed to generate fleet export");
        }
    }
    
    private byte[] exportFleetOverview(Workbook workbook, Company company) throws IOException {
        // Truck Heads Sheet
        Sheet truckHeadsSheet = workbook.createSheet("Truck Heads");
        createTruckHeadsSheet(truckHeadsSheet, company);
        
        // Trailers Sheet
        Sheet trailersSheet = workbook.createSheet("Trailers");
        createTrailersSheet(trailersSheet, company);
        
        // Fleet Assignments Sheet
        Sheet assignmentsSheet = workbook.createSheet("Fleet Assignments");
        createFleetAssignmentsSheet(assignmentsSheet, company);
        
        return writeWorkbookToBytes(workbook);
    }
    
    private byte[] exportComplianceReport(Workbook workbook, Company company, LocalDateTime startDate, LocalDateTime endDate) throws IOException {
        // Insurance Sheet
        Sheet insuranceSheet = workbook.createSheet("Insurance");
        createInsuranceSheet(insuranceSheet, company, startDate, endDate);
        
        // Tax Clearance Sheet
        Sheet taxSheet = workbook.createSheet("Tax Clearance");
        createTaxClearanceSheet(taxSheet, company, startDate, endDate);
        
        // Permits Sheet
        Sheet permitsSheet = workbook.createSheet("Permits");
        createPermitsSheet(permitsSheet, company, startDate, endDate);
        
        // Compliance Summary Sheet
        Sheet summarySheet = workbook.createSheet("Compliance Summary");
        createComplianceSummarySheet(summarySheet, company);
        
        return writeWorkbookToBytes(workbook);
    }
    
    private byte[] exportUtilizationReport(Workbook workbook, Company company, LocalDateTime startDate, LocalDateTime endDate) throws IOException {
        Sheet utilizationSheet = workbook.createSheet("Fleet Utilization");
        createUtilizationSheet(utilizationSheet, company, startDate, endDate);
        
        return writeWorkbookToBytes(workbook);
    }
    
    private byte[] exportMaintenanceReport(Workbook workbook, Company company, LocalDateTime startDate, LocalDateTime endDate) throws IOException {
        Sheet maintenanceSheet = workbook.createSheet("Maintenance Schedule");
        createMaintenanceSheet(maintenanceSheet, company, startDate, endDate);
        
        return writeWorkbookToBytes(workbook);
    }
    
    private byte[] exportCompleteFleetData(Workbook workbook, Company company) throws IOException {
        // All sheets
        createTruckHeadsSheet(workbook.createSheet("Truck Heads"), company);
        createTrailersSheet(workbook.createSheet("Trailers"), company);
        createFleetAssignmentsSheet(workbook.createSheet("Fleet Assignments"), company);
        createInsuranceSheet(workbook.createSheet("Insurance"), company, null, null);
        createTaxClearanceSheet(workbook.createSheet("Tax Clearance"), company, null, null);
        createPermitsSheet(workbook.createSheet("Permits"), company, null, null);
        createComplianceSummarySheet(workbook.createSheet("Compliance Summary"), company);
        
        return writeWorkbookToBytes(workbook);
    }
    
    private void createTruckHeadsSheet(Sheet sheet, Company company) {
        // Create header row
        Row headerRow = sheet.createRow(0);
        String[] headers = {
            "ID", "Registration Number", "Make", "Model", "Year", "Engine Number", 
            "Chassis Number", "Status", "Fuel Type", "Fuel Capacity", "Max Towing Capacity",
            "Current Location", "Has Insurance", "Insurance Expiry", "Has Fitness Certificate",
            "Fitness Expiry", "Has Road Permit", "Road Permit Expiry", "Has Tax Clearance",
            "Tax Clearance Expiry", "Current Driver", "Created At", "Updated At"
        };
        
        CellStyle headerStyle = createHeaderStyle(sheet.getWorkbook());
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }
        
        // Add data rows
        List<TruckHead> truckHeads = truckHeadRepository.findByCompany(company);
        int rowNum = 1;
        for (TruckHead truckHead : truckHeads) {
            Row row = sheet.createRow(rowNum++);
            int colNum = 0;
            
            row.createCell(colNum++).setCellValue(truckHead.getId());
            row.createCell(colNum++).setCellValue(truckHead.getRegistrationNumber());
            row.createCell(colNum++).setCellValue(truckHead.getMake());
            row.createCell(colNum++).setCellValue(truckHead.getModel());
            row.createCell(colNum++).setCellValue(truckHead.getYear() != null ? truckHead.getYear() : 0);
            row.createCell(colNum++).setCellValue(truckHead.getEngineNumber() != null ? truckHead.getEngineNumber() : "");
            row.createCell(colNum++).setCellValue(truckHead.getChassisNumber() != null ? truckHead.getChassisNumber() : "");
            row.createCell(colNum++).setCellValue(truckHead.getStatus().toString());
            row.createCell(colNum++).setCellValue(truckHead.getFuelType() != null ? truckHead.getFuelType().toString() : "");
            row.createCell(colNum++).setCellValue(truckHead.getFuelCapacity() != null ? truckHead.getFuelCapacity().doubleValue() : 0);
            row.createCell(colNum++).setCellValue(truckHead.getMaxTowingCapacity() != null ? truckHead.getMaxTowingCapacity().doubleValue() : 0);
            row.createCell(colNum++).setCellValue(truckHead.getCurrentLocation() != null ? truckHead.getCurrentLocation() : "");
            row.createCell(colNum++).setCellValue(truckHead.getHasInsurance());
            row.createCell(colNum++).setCellValue(truckHead.getInsuranceExpiryDate() != null ? truckHead.getInsuranceExpiryDate().format(DATE_FORMATTER) : "");
            row.createCell(colNum++).setCellValue(truckHead.getHasFitnessCertificate());
            row.createCell(colNum++).setCellValue(truckHead.getFitnessExpiryDate() != null ? truckHead.getFitnessExpiryDate().format(DATE_FORMATTER) : "");
            row.createCell(colNum++).setCellValue(truckHead.getHasRoadPermit());
            row.createCell(colNum++).setCellValue(truckHead.getRoadPermitExpiryDate() != null ? truckHead.getRoadPermitExpiryDate().format(DATE_FORMATTER) : "");
            row.createCell(colNum++).setCellValue(truckHead.getHasTaxClearance());
            row.createCell(colNum++).setCellValue(truckHead.getTaxClearanceExpiryDate() != null ? truckHead.getTaxClearanceExpiryDate().format(DATE_FORMATTER) : "");
            row.createCell(colNum++).setCellValue(truckHead.getCurrentDriver() != null ? truckHead.getCurrentDriver().getFirstName() + " " + truckHead.getCurrentDriver().getLastName() : "");
            row.createCell(colNum++).setCellValue(truckHead.getCreatedAt().format(DATE_FORMATTER));
            row.createCell(colNum++).setCellValue(truckHead.getUpdatedAt().format(DATE_FORMATTER));
        }
        
        // Auto-size columns
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }
    }
    
    private void createTrailersSheet(Sheet sheet, Company company) {
        // Create header row
        Row headerRow = sheet.createRow(0);
        String[] headers = {
            "ID", "Registration Number", "Make", "Model", "Year", "Chassis Number", 
            "Status", "Type", "Max Weight", "Max Volume", "Length", "Width", "Height",
            "Current Location", "Has Insurance", "Insurance Expiry", "Has Fitness Certificate",
            "Fitness Expiry", "Has Road Permit", "Road Permit Expiry", "Has Refrigeration",
            "Has Tail Lift", "Has Side Loader", "Has GPS", "Created At", "Updated At"
        };
        
        CellStyle headerStyle = createHeaderStyle(sheet.getWorkbook());
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }
        
        // Add data rows
        List<Trailer> trailers = trailerRepository.findByCompany(company);
        int rowNum = 1;
        for (Trailer trailer : trailers) {
            Row row = sheet.createRow(rowNum++);
            int colNum = 0;
            
            row.createCell(colNum++).setCellValue(trailer.getId());
            row.createCell(colNum++).setCellValue(trailer.getRegistrationNumber());
            row.createCell(colNum++).setCellValue(trailer.getMake());
            row.createCell(colNum++).setCellValue(trailer.getModel());
            row.createCell(colNum++).setCellValue(trailer.getYear() != null ? trailer.getYear() : 0);
            row.createCell(colNum++).setCellValue(trailer.getChassisNumber() != null ? trailer.getChassisNumber() : "");
            row.createCell(colNum++).setCellValue(trailer.getStatus().toString());
            row.createCell(colNum++).setCellValue(trailer.getType().toString());
            row.createCell(colNum++).setCellValue(trailer.getMaxWeight() != null ? trailer.getMaxWeight().doubleValue() : 0);
            row.createCell(colNum++).setCellValue(trailer.getMaxVolume() != null ? trailer.getMaxVolume().doubleValue() : 0);
            row.createCell(colNum++).setCellValue(trailer.getLength() != null ? trailer.getLength().doubleValue() : 0);
            row.createCell(colNum++).setCellValue(trailer.getWidth() != null ? trailer.getWidth().doubleValue() : 0);
            row.createCell(colNum++).setCellValue(trailer.getHeight() != null ? trailer.getHeight().doubleValue() : 0);
            row.createCell(colNum++).setCellValue(trailer.getCurrentLocation() != null ? trailer.getCurrentLocation() : "");
            row.createCell(colNum++).setCellValue(trailer.getHasInsurance());
            row.createCell(colNum++).setCellValue(trailer.getInsuranceExpiryDate() != null ? trailer.getInsuranceExpiryDate().format(DATE_FORMATTER) : "");
            row.createCell(colNum++).setCellValue(trailer.getHasFitnessCertificate());
            row.createCell(colNum++).setCellValue(trailer.getFitnessExpiryDate() != null ? trailer.getFitnessExpiryDate().format(DATE_FORMATTER) : "");
            row.createCell(colNum++).setCellValue(trailer.getHasRoadPermit());
            row.createCell(colNum++).setCellValue(trailer.getRoadPermitExpiryDate() != null ? trailer.getRoadPermitExpiryDate().format(DATE_FORMATTER) : "");
            row.createCell(colNum++).setCellValue(trailer.getHasRefrigeration());
            row.createCell(colNum++).setCellValue(trailer.getHasTailLift());
            row.createCell(colNum++).setCellValue(trailer.getHasSideLoader());
            row.createCell(colNum++).setCellValue(trailer.getHasGPS());
            row.createCell(colNum++).setCellValue(trailer.getCreatedAt().format(DATE_FORMATTER));
            row.createCell(colNum++).setCellValue(trailer.getUpdatedAt().format(DATE_FORMATTER));
        }
        
        // Auto-size columns
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }
    }
    
    private void createFleetAssignmentsSheet(Sheet sheet, Company company) {
        // Create header row
        Row headerRow = sheet.createRow(0);
        String[] headers = {
            "ID", "Load ID", "Load Title", "Bid ID", "Truck Head", "Trailer 1", "Trailer 2",
            "Primary Driver", "Secondary Driver", "Status", "Assigned At", "Scheduled Start",
            "Actual Start", "Scheduled End", "Actual End", "Notes", "Assigned By", "Created At"
        };
        
        CellStyle headerStyle = createHeaderStyle(sheet.getWorkbook());
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }
        
        // Add data rows
        List<FleetAssignment> assignments = fleetAssignmentRepository.findByCompany(company);
        int rowNum = 1;
        for (FleetAssignment assignment : assignments) {
            Row row = sheet.createRow(rowNum++);
            int colNum = 0;
            
            row.createCell(colNum++).setCellValue(assignment.getId());
            row.createCell(colNum++).setCellValue(assignment.getLoad().getId());
            row.createCell(colNum++).setCellValue(assignment.getLoad().getTitle());
            row.createCell(colNum++).setCellValue(assignment.getBid() != null ? assignment.getBid().getId() : 0);
            row.createCell(colNum++).setCellValue(assignment.getTruckHead() != null ? assignment.getTruckHead().getRegistrationNumber() : "");
            row.createCell(colNum++).setCellValue(assignment.getTrailer1() != null ? assignment.getTrailer1().getRegistrationNumber() : "");
            row.createCell(colNum++).setCellValue(assignment.getTrailer2() != null ? assignment.getTrailer2().getRegistrationNumber() : "");
            row.createCell(colNum++).setCellValue(assignment.getPrimaryDriver() != null ? assignment.getPrimaryDriver().getFirstName() + " " + assignment.getPrimaryDriver().getLastName() : "");
            row.createCell(colNum++).setCellValue(assignment.getSecondaryDriver() != null ? assignment.getSecondaryDriver().getFirstName() + " " + assignment.getSecondaryDriver().getLastName() : "");
            row.createCell(colNum++).setCellValue(assignment.getStatus().toString());
            row.createCell(colNum++).setCellValue(assignment.getAssignedAt() != null ? assignment.getAssignedAt().format(DATE_FORMATTER) : "");
            row.createCell(colNum++).setCellValue(assignment.getScheduledStartTime().format(DATE_FORMATTER));
            row.createCell(colNum++).setCellValue(assignment.getActualStartTime() != null ? assignment.getActualStartTime().format(DATE_FORMATTER) : "");
            row.createCell(colNum++).setCellValue(assignment.getScheduledEndTime().format(DATE_FORMATTER));
            row.createCell(colNum++).setCellValue(assignment.getActualEndTime() != null ? assignment.getActualEndTime().format(DATE_FORMATTER) : "");
            row.createCell(colNum++).setCellValue(assignment.getNotes() != null ? assignment.getNotes() : "");
            row.createCell(colNum++).setCellValue(assignment.getAssignedBy() != null ? assignment.getAssignedBy().getFirstName() + " " + assignment.getAssignedBy().getLastName() : "");
            row.createCell(colNum++).setCellValue(assignment.getCreatedAt().format(DATE_FORMATTER));
        }
        
        // Auto-size columns
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }
    }
    
    private void createInsuranceSheet(Sheet sheet, Company company, LocalDateTime startDate, LocalDateTime endDate) {
        // Implementation for insurance sheet
        // Similar pattern to above methods
    }
    
    private void createTaxClearanceSheet(Sheet sheet, Company company, LocalDateTime startDate, LocalDateTime endDate) {
        // Implementation for tax clearance sheet
        // Similar pattern to above methods
    }
    
    private void createPermitsSheet(Sheet sheet, Company company, LocalDateTime startDate, LocalDateTime endDate) {
        // Implementation for permits sheet
        // Similar pattern to above methods
    }
    
    private void createComplianceSummarySheet(Sheet sheet, Company company) {
        // Implementation for compliance summary
        // This would show counts and statistics
    }
    
    private void createUtilizationSheet(Sheet sheet, Company company, LocalDateTime startDate, LocalDateTime endDate) {
        // Implementation for utilization report
        // This would show utilization statistics and metrics
    }
    
    private void createMaintenanceSheet(Sheet sheet, Company company, LocalDateTime startDate, LocalDateTime endDate) {
        // Implementation for maintenance schedule
        // This would show maintenance schedules and history
    }
    
    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setColor(IndexedColors.WHITE.getIndex());
        style.setFont(font);
        style.setFillForegroundColor(IndexedColors.DARK_BLUE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        return style;
    }
    
    private byte[] writeWorkbookToBytes(Workbook workbook) throws IOException {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            workbook.write(outputStream);
            return outputStream.toByteArray();
        }
    }
    
    private User getUserByUsername(String username) {
        return userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
    }
    
    private Company getCompanyByUser(User user) {
        if (user.getCompany() == null) {
            throw new BusinessException("User must be associated with a company");
        }
        return user.getCompany();
    }
}
