package zw.co.kanjan.logipool.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "loads")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Load {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(unique = true, length = 20)
    private String trackingNumber;

    @NotBlank
    @Size(max = 100)
    private String title;
    
    @Column(columnDefinition = "TEXT")
    private String description;
    
    @NotBlank
    @Size(max = 50)
    private String cargoType;
    
    @Positive
    private BigDecimal weight;
    
    @Size(max = 20)
    private String weightUnit = "kg";
    
    private BigDecimal volume;
    
    @Size(max = 20)
    private String volumeUnit = "m3";
    
    @NotBlank
    @Size(max = 200)
    private String pickupLocation;

    @DecimalMin(value = "-90.0", message = "Pickup latitude must be between -90 and 90")
    @DecimalMax(value = "90.0", message = "Pickup latitude must be between -90 and 90")
    private BigDecimal pickupLatitude;

    @DecimalMin(value = "-180.0", message = "Pickup longitude must be between -180 and 180")
    @DecimalMax(value = "180.0", message = "Pickup longitude must be between -180 and 180")
    private BigDecimal pickupLongitude;

    @NotBlank
    @Size(max = 200)
    private String deliveryLocation;

    @DecimalMin(value = "-90.0", message = "Delivery latitude must be between -90 and 90")
    @DecimalMax(value = "90.0", message = "Delivery latitude must be between -90 and 90")
    private BigDecimal deliveryLatitude;

    @DecimalMin(value = "-180.0", message = "Delivery longitude must be between -180 and 180")
    @DecimalMax(value = "180.0", message = "Delivery longitude must be between -180 and 180")
    private BigDecimal deliveryLongitude;
    
    @NotNull
    private LocalDateTime pickupDate;
    
    @NotNull
    private LocalDateTime deliveryDate;
    
    private BigDecimal estimatedDistance;
    
    @Size(max = 20)
    private String distanceUnit = "km";
    
    @Enumerated(EnumType.STRING)
    @Column(length = 20)
    private LoadType loadType;
    
    @Enumerated(EnumType.STRING)
    @Column(length = 20)
    private PaymentType paymentType;
    
    private BigDecimal paymentRate;
    
    @Size(max = 20)
    private String paymentUnit;
    
    private BigDecimal estimatedValue;
    
    @Enumerated(EnumType.STRING)
    @Column(length = 20)
    private LoadStatus status = LoadStatus.POSTED;
    
    @Enumerated(EnumType.STRING)
    @Column(length = 20)
    private Priority priority = Priority.NORMAL;
    
    @Builder.Default
    private Boolean isVerified = false;
    
    @Builder.Default
    private Boolean requiresInsurance = false;
    
    @Builder.Default
    private Boolean requiresSpecialHandling = false;
    
    @Column(columnDefinition = "TEXT")
    private String specialInstructions;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "client_id")
    private User client;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "assigned_company_id")
    private Company assignedCompany;
    
    @OneToMany(mappedBy = "load", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @Builder.Default
    private List<Bid> bids = new ArrayList<>();
    
    @OneToMany(mappedBy = "load", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @Builder.Default
    private List<LoadTracking> trackingHistory = new ArrayList<>();

    @OneToMany(mappedBy = "load", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @Builder.Default
    private List<FleetAssignment> fleetAssignments = new ArrayList<>();

    @CreationTimestamp
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    private LocalDateTime updatedAt;
    
    private LocalDateTime biddingClosesAt;

    private LocalDateTime deliveredAt;

    private LocalDateTime completedAt;

    @Builder.Default
    private Boolean documentsVerified = false;

    @Builder.Default
    private Boolean requiresDocumentVerification = true;

    public enum LoadType {
        LOCAL, REGIONAL, CONTRACT, ONCE_OFF
    }
    
    public enum PaymentType {
        PER_KM, PER_TONNE, FIXED_RATE, NEGOTIABLE
    }
    
    public enum LoadStatus {
        POSTED, BIDDING_CLOSED, ASSIGNED, IN_TRANSIT, DELIVERED, COMPLETED, CANCELLED
    }
    
    public enum Priority {
        LOW, NORMAL, HIGH, URGENT
    }
}
