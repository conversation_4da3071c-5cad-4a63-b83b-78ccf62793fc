import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../shared/widgets/public_footer.dart';
import '../../../shared/models/load_model.dart';
import '../../../shared/models/paginated_response.dart';
import '../../../shared/services/public_api_service.dart';
import '../../../shared/utils/api_client.dart';

class LoadsBrowseScreen extends StatefulWidget {
  const LoadsBrowseScreen({super.key});

  @override
  State<LoadsBrowseScreen> createState() => _LoadsBrowseScreenState();
}

class _LoadsBrowseScreenState extends State<LoadsBrowseScreen> {
  String _selectedLoadType = 'All';
  String _selectedCargoType = 'All';
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  // API related state
  late PublicApiService _publicApiService;
  List<LoadModel> _loads = [];
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';
  int _currentPage = 0;
  bool _hasMoreData = true;
  final ScrollController _scrollController = ScrollController();

  final List<String> _loadTypes = [
    'All',
    'GENERAL',
    'FRAGILE',
    'HAZARDOUS',
    'REFRIGERATED',
    'OVERSIZED',
    'LIQUID',
    'BULK'
  ];

  final List<String> _cargoTypes = [
    'All',
    'Electronics',
    'Furniture',
    'Food & Beverages',
    'Machinery',
    'Textiles',
    'Chemicals',
    'Construction Materials',
    'Automotive Parts',
    'Medical Supplies',
    'Agricultural Products'
  ];

  @override
  void initState() {
    super.initState();
    _publicApiService = PublicApiService(ApiClient.instance);
    _loadLoads();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent) {
      if (!_isLoading && _hasMoreData) {
        _loadMoreLoads();
      }
    }
  }

  Future<void> _loadLoads() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
      _currentPage = 0;
      _loads.clear();
    });

    try {
      final response = await _publicApiService.browseLoads(
        page: _currentPage,
        size: 20,
        loadType: _selectedLoadType != 'All' ? _selectedLoadType : null,
        cargoType: _selectedCargoType != 'All' ? _selectedCargoType : null,
        search: _searchQuery.isNotEmpty ? _searchQuery : null,
      );

      setState(() {
        _loads = response.content;
        _hasMoreData = !response.last;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _loadMoreLoads() async {
    if (_isLoading || !_hasMoreData) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await _publicApiService.browseLoads(
        page: _currentPage + 1,
        size: 20,
        loadType: _selectedLoadType != 'All' ? _selectedLoadType : null,
        cargoType: _selectedCargoType != 'All' ? _selectedCargoType : null,
        search: _searchQuery.isNotEmpty ? _searchQuery : null,
      );

      setState(() {
        _loads.addAll(response.content);
        _currentPage++;
        _hasMoreData = !response.last;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          _buildHeader(context),
          _buildFilters(context),
          Expanded(
            child: RefreshIndicator(
              onRefresh: _loadLoads,
              child: SingleChildScrollView(
                controller: _scrollController,
                child: Column(
                  children: [
                    _buildLoadGrid(context),
                    const PublicFooter(),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withOpacity(0.8),
          ],
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Available Loads',
            style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'Find transportation opportunities and submit your bids',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Colors.white70,
                ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search loads by title, cargo type, or location...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: IconButton(
                icon: const Icon(Icons.clear),
                onPressed: () {
                  _searchController.clear();
                  setState(() {
                    _searchQuery = '';
                  });
                  _loadLoads();
                },
              ),
              filled: true,
              fillColor: Colors.white,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(30),
                borderSide: BorderSide.none,
              ),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
              // Debounce search to avoid too many API calls
              Future.delayed(const Duration(milliseconds: 500), () {
                if (_searchQuery == value) {
                  _loadLoads();
                }
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildFilters(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Theme.of(context).primaryColor.withOpacity(0.05),
      child: Row(
        children: [
          Expanded(
            child: DropdownButtonFormField<String>(
              value: _selectedLoadType,
              decoration: const InputDecoration(
                labelText: 'Load Type',
                border: OutlineInputBorder(),
              ),
              items: _loadTypes.map((type) {
                return DropdownMenuItem(
                  value: type,
                  child: Text(type == 'All' ? 'All Types' : type.replaceAll('_', ' ')),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedLoadType = value!;
                });
                _loadLoads();
              },
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: DropdownButtonFormField<String>(
              value: _selectedCargoType,
              decoration: const InputDecoration(
                labelText: 'Cargo Type',
                border: OutlineInputBorder(),
              ),
              items: _cargoTypes.map((type) {
                return DropdownMenuItem(
                  value: type,
                  child: Text(type),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedCargoType = value!;
                });
                _loadLoads();
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadGrid(BuildContext context) {
    if (_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Failed to load loads',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadLoads,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_isLoading && _loads.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_loads.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.local_shipping_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No loads found',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'Try adjusting your search criteria or check back later',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    final crossAxisCount = MediaQuery.of(context).size.width > 768 ? 3 : 2;
    final screenWidth = MediaQuery.of(context).size.width;
    final cardWidth = (screenWidth - 48) / crossAxisCount; // 48 = padding + spacing

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Wrap(
            spacing: 16,
            runSpacing: 16,
            children: _loads.map((load) => SizedBox(
              width: cardWidth,
              child: _buildLoadCard(context, load),
            )).toList(),
          ),
          if (_isLoading && _loads.isNotEmpty)
            const Padding(
              padding: EdgeInsets.all(16),
              child: CircularProgressIndicator(),
            ),
        ],
      ),
    );
  }

  Widget _buildLoadCard(BuildContext context, LoadModel load) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () {
          // Show load details dialog
          _showLoadDetailsDialog(context, load);
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      load.title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getPriorityColor(load.priority),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      load.priority.name.toUpperCase(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                load.cargoType,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(
                    Icons.location_on_outlined,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      '${load.pickupLocation} → ${load.deliveryLocation}',
                      style: Theme.of(context).textTheme.bodySmall,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(
                    Icons.scale_outlined,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${load.weight} ${load.weightUnit}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  const Spacer(),
                  if (load.estimatedDistance != null) ...[
                    Icon(
                      Icons.straighten_outlined,
                      size: 16,
                      color: Colors.grey[600],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${load.estimatedDistance?.toStringAsFixed(0)} ${load.distanceUnit}',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(
                    Icons.schedule_outlined,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      'Pickup: ${_formatDate(load.pickupDate)}',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(
                    Icons.flag_outlined,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      'Delivery: ${_formatDate(load.deliveryDate)}',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  if (load.isVerified) ...[
                    Icon(
                      Icons.verified,
                      size: 16,
                      color: Colors.green,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Verified',
                      style: TextStyle(
                        color: Colors.green,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const Spacer(),
                  ],
                  if (load.requiresInsurance) ...[
                    Icon(
                      Icons.security,
                      size: 16,
                      color: Colors.orange,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Insurance Required',
                      style: TextStyle(
                        color: Colors.orange,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getPriorityColor(Priority priority) {
    switch (priority) {
      case Priority.urgent:
        return Colors.red;
      case Priority.high:
        return Colors.orange;
      case Priority.normal:
        return Colors.blue;
      case Priority.low:
        return Colors.grey;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _showLoadDetailsDialog(BuildContext context, LoadModel load) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(load.title),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('Cargo Type', load.cargoType),
              _buildDetailRow('Weight', '${load.weight} ${load.weightUnit}'),
              if (load.volume != null)
                _buildDetailRow('Volume', '${load.volume} ${load.volumeUnit}'),
              _buildDetailRow('Pickup Location', load.pickupLocation),
              _buildDetailRow('Delivery Location', load.deliveryLocation),
              _buildDetailRow('Pickup Date', _formatDate(load.pickupDate)),
              _buildDetailRow('Delivery Date', _formatDate(load.deliveryDate)),
              if (load.estimatedDistance != null)
                _buildDetailRow('Distance', '${load.estimatedDistance?.toStringAsFixed(0)} ${load.distanceUnit}'),
              _buildDetailRow('Priority', load.priority.name.toUpperCase()),
              if (load.description != null && load.description!.isNotEmpty)
                _buildDetailRow('Description', load.description!),
              if (load.requiresSpecialHandling)
                const Text(
                  '⚠️ Requires Special Handling',
                  style: TextStyle(color: Colors.orange, fontWeight: FontWeight.w500),
                ),
              if (load.requiresInsurance)
                const Text(
                  '🛡️ Insurance Required',
                  style: TextStyle(color: Colors.blue, fontWeight: FontWeight.w500),
                ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Navigate to guest load posting with pre-filled data
              context.push('/guest-load-posting');
            },
            child: const Text('Post Similar Load'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }
}
