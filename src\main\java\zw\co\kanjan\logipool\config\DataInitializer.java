package zw.co.kanjan.logipool.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import zw.co.kanjan.logipool.entity.Role;
import zw.co.kanjan.logipool.repository.RoleRepository;

@Component
@RequiredArgsConstructor
@Slf4j
public class DataInitializer implements CommandLineRunner {
    
    private final RoleRepository roleRepository;
    
    @Override
    public void run(String... args) throws Exception {
        initializeRoles();
    }
    
    private void initializeRoles() {
        for (Role.RoleName roleName : Role.RoleName.values()) {
            if (!roleRepository.existsByName(roleName)) {
                Role role = Role.builder()
                        .name(roleName)
                        .description(getDescriptionForRole(roleName))
                        .build();
                roleRepository.save(role);
                log.info("Created role: {}", roleName);
            }
        }
    }
    
    private String getDescriptionForRole(Role.RoleName roleName) {
        return switch (roleName) {
            case ADMIN -> "System administrator with full access";
            case CLIENT -> "Client who posts loads for transportation";
            case TRANSPORTER -> "Logistics company that provides transportation services";
            case DRIVER -> "Driver who operates vehicles for transportation services";
        };
    }
}
