package zw.co.kanjan.logipool.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import zw.co.kanjan.logipool.dto.fleet.FleetAssignmentDto;
import zw.co.kanjan.logipool.entity.FleetAssignment;
import zw.co.kanjan.logipool.service.FleetManagementService;

import java.security.Principal;

@RestController
@RequestMapping("/api/fleet/assignments")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Fleet Assignment Management", description = "APIs for managing fleet assignments to loads")
public class FleetManagementController {
    
    private final FleetManagementService fleetManagementService;
    
    @PostMapping
    @PreAuthorize("hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Create fleet assignment", description = "Assign fleet resources to a load")
    public ResponseEntity<FleetAssignmentDto.FleetAssignmentResponse> createFleetAssignment(
            @Valid @RequestBody FleetAssignmentDto.FleetAssignmentCreateRequest request,
            Principal principal) {
        
        log.info("Creating fleet assignment for load: {} by user: {}", 
                request.getLoadId(), principal.getName());
        
        FleetAssignmentDto.FleetAssignmentResponse response = 
                fleetManagementService.createFleetAssignment(request, principal.getName());
        return ResponseEntity.ok(response);
    }
    
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Update fleet assignment", description = "Update fleet assignment details")
    public ResponseEntity<FleetAssignmentDto.FleetAssignmentResponse> updateFleetAssignment(
            @Parameter(description = "Fleet assignment ID") @PathVariable Long id,
            @Valid @RequestBody FleetAssignmentDto.FleetAssignmentUpdateRequest request,
            Principal principal) {
        
        log.info("Updating fleet assignment: {} by user: {}", id, principal.getName());
        
        FleetAssignmentDto.FleetAssignmentResponse response = 
                fleetManagementService.updateFleetAssignment(id, request, principal.getName());
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('TRANSPORTER') or hasRole('ADMIN') or hasRole('CLIENT')")
    @Operation(summary = "Get fleet assignment", description = "Get fleet assignment details by ID")
    public ResponseEntity<FleetAssignmentDto.FleetAssignmentResponse> getFleetAssignment(
            @Parameter(description = "Fleet assignment ID") @PathVariable Long id,
            Principal principal) {
        
        FleetAssignmentDto.FleetAssignmentResponse response = 
                fleetManagementService.getFleetAssignment(id, principal.getName());
        return ResponseEntity.ok(response);
    }
    
    @GetMapping
    @PreAuthorize("hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Get fleet assignments", description = "Get paginated list of fleet assignments")
    public ResponseEntity<Page<FleetAssignmentDto.FleetAssignmentResponse>> getFleetAssignments(
            @PageableDefault(size = 20, sort = "createdAt") Pageable pageable,
            @Parameter(description = "Filter by status") @RequestParam(required = false) FleetAssignment.AssignmentStatus status,
            Principal principal) {
        
        Page<FleetAssignmentDto.FleetAssignmentResponse> response = 
                fleetManagementService.getFleetAssignments(principal.getName(), pageable, status);
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/by-load/{loadId}")
    @PreAuthorize("hasRole('TRANSPORTER') or hasRole('ADMIN') or hasRole('CLIENT')")
    @Operation(summary = "Get fleet assignment by load", description = "Get fleet assignment for a specific load")
    public ResponseEntity<FleetAssignmentDto.FleetAssignmentResponse> getFleetAssignmentByLoad(
            @Parameter(description = "Load ID") @PathVariable Long loadId,
            Principal principal) {
        
        FleetAssignmentDto.FleetAssignmentResponse response = 
                fleetManagementService.getFleetAssignmentByLoad(loadId, principal.getName());
        
        if (response == null) {
            return ResponseEntity.notFound().build();
        }
        
        return ResponseEntity.ok(response);
    }
    
    @PostMapping("/check-availability/{resourceType}/{resourceId}")
    @PreAuthorize("hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Check fleet resource availability", 
               description = "Check if a fleet resource is available for the specified time period")
    public ResponseEntity<FleetAssignmentDto.FleetAvailabilityResponse> checkFleetAvailability(
            @Parameter(description = "Resource type (truck, trailer, driver)") @PathVariable String resourceType,
            @Parameter(description = "Resource ID") @PathVariable Long resourceId,
            @Valid @RequestBody FleetAssignmentDto.FleetAvailabilityRequest request,
            Principal principal) {
        
        FleetAssignmentDto.FleetAvailabilityResponse response = 
                fleetManagementService.checkFleetAvailability(resourceId, resourceType, request, principal.getName());
        return ResponseEntity.ok(response);
    }
    
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Delete fleet assignment", description = "Remove fleet assignment")
    public ResponseEntity<Void> deleteFleetAssignment(
            @Parameter(description = "Fleet assignment ID") @PathVariable Long id,
            Principal principal) {
        
        log.info("Deleting fleet assignment: {} by user: {}", id, principal.getName());
        
        fleetManagementService.deleteFleetAssignment(id, principal.getName());
        return ResponseEntity.noContent().build();
    }
}
