package zw.co.kanjan.logipool.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import zw.co.kanjan.logipool.entity.Company;
import zw.co.kanjan.logipool.entity.Trailer;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface TrailerRepository extends JpaRepository<Trailer, Long> {
    
    Optional<Trailer> findByRegistrationNumber(String registrationNumber);
    
    List<Trailer> findByCompany(Company company);
    
    Page<Trailer> findByCompany(Company company, Pageable pageable);
    
    List<Trailer> findByCompanyAndStatus(Company company, Trailer.TrailerStatus status);
    
    Page<Trailer> findByCompanyAndStatus(Company company, Trailer.TrailerStatus status, Pageable pageable);
    
    List<Trailer> findByCompanyAndType(Company company, Trailer.TrailerType type);
    
    @Query("SELECT t FROM Trailer t WHERE t.company = :company AND " +
           "(:status IS NULL OR t.status = :status) AND " +
           "(:type IS NULL OR t.type = :type) AND " +
           "(:make IS NULL OR LOWER(t.make) LIKE LOWER(CONCAT('%', :make, '%'))) AND " +
           "(:model IS NULL OR LOWER(t.model) LIKE LOWER(CONCAT('%', :model, '%'))) AND " +
           "(:registrationNumber IS NULL OR LOWER(t.registrationNumber) LIKE LOWER(CONCAT('%', :registrationNumber, '%')))")
    Page<Trailer> findByCompanyWithFilters(
            @Param("company") Company company,
            @Param("status") Trailer.TrailerStatus status,
            @Param("type") Trailer.TrailerType type,
            @Param("make") String make,
            @Param("model") String model,
            @Param("registrationNumber") String registrationNumber,
            Pageable pageable);
    
    @Query("SELECT t FROM Trailer t WHERE t.company = :company AND t.status = 'AVAILABLE'")
    List<Trailer> findAvailableByCompany(@Param("company") Company company);
    
    @Query("SELECT t FROM Trailer t WHERE t.company = :company AND t.status = 'AVAILABLE' AND t.type = :type")
    List<Trailer> findAvailableByCompanyAndType(@Param("company") Company company, @Param("type") Trailer.TrailerType type);
    
    @Query("SELECT t FROM Trailer t WHERE t.insuranceExpiryDate IS NOT NULL AND " +
           "t.insuranceExpiryDate BETWEEN :startDate AND :endDate")
    List<Trailer> findByInsuranceExpiryDateBetween(
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT t FROM Trailer t WHERE t.fitnessExpiryDate IS NOT NULL AND " +
           "t.fitnessExpiryDate BETWEEN :startDate AND :endDate")
    List<Trailer> findByFitnessExpiryDateBetween(
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT t FROM Trailer t WHERE t.roadPermitExpiryDate IS NOT NULL AND " +
           "t.roadPermitExpiryDate BETWEEN :startDate AND :endDate")
    List<Trailer> findByRoadPermitExpiryDateBetween(
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT COUNT(t) FROM Trailer t WHERE t.company = :company")
    long countByCompany(@Param("company") Company company);
    
    @Query("SELECT COUNT(t) FROM Trailer t WHERE t.company = :company AND t.status = :status")
    long countByCompanyAndStatus(@Param("company") Company company, @Param("status") Trailer.TrailerStatus status);
    
    @Query("SELECT COUNT(t) FROM Trailer t WHERE t.company = :company AND t.type = :type")
    long countByCompanyAndType(@Param("company") Company company, @Param("type") Trailer.TrailerType type);
    
    boolean existsByRegistrationNumber(String registrationNumber);
}
