package zw.co.kanjan.logipool.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

@Entity
@Table(name = "documents")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Document {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank
    @Size(max = 100)
    private String name;
    
    @Enumerated(EnumType.STRING)
    @Column(length = 30)
    private DocumentType type;
    
    @NotBlank
    @Size(max = 500)
    private String filePath;
    
    @Size(max = 100)
    private String fileName;
    
    @Size(max = 100)
    private String fileType;
    
    private Long fileSize;
    
    @Enumerated(EnumType.STRING)
    @Column(length = 20)
    private DocumentStatus status = DocumentStatus.PENDING;
    
    @Column(columnDefinition = "TEXT")
    private String description;
    
    private LocalDateTime expiryDate;
    
    @Builder.Default
    private Boolean isRequired = false;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "company_id")
    private Company company;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "vehicle_id")
    private Vehicle vehicle;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "equipment_id")
    private Equipment equipment;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "load_id")
    private Load load;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "truck_head_id")
    private TruckHead truckHead;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "trailer_id")
    private Trailer trailer;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "insurance_id")
    private Insurance insurance;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "tax_clearance_id")
    private TaxClearance taxClearance;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "permit_id")
    private Permit permit;

    @CreationTimestamp
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    private LocalDateTime updatedAt;
    
    private LocalDateTime verifiedAt;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "verified_by")
    private User verifiedBy;
    
    public enum DocumentType {
        // Company Documents
        COMPANY_REGISTRATION,
        TAX_CLEARANCE,
        BUSINESS_LICENSE,
        INSURANCE_CERTIFICATE,

        // Vehicle Documents
        VEHICLE_REGISTRATION,
        FITNESS_CERTIFICATE,
        ROAD_PERMIT,
        ZINARA_PERMIT,
        VEHICLE_INSURANCE,
        VEHICLE_PHOTOS,

        // Truck Head Documents
        TRUCK_REGISTRATION,
        TRUCK_FITNESS_CERTIFICATE,
        TRUCK_INSURANCE,
        TRUCK_ROAD_PERMIT,
        TRUCK_PHOTOS,

        // Trailer Documents
        TRAILER_REGISTRATION,
        TRAILER_FITNESS_CERTIFICATE,
        TRAILER_INSURANCE,
        TRAILER_ROAD_PERMIT,
        TRAILER_PHOTOS,

        // Insurance Documents
        INSURANCE_POLICY,
        INSURANCE_CERTIFICATE_DOC,
        INSURANCE_CLAIM_FORM,

        // Tax Clearance Documents
        TAX_CLEARANCE_CERTIFICATE,
        TAX_PAYMENT_RECEIPT,
        TAX_ASSESSMENT,

        // Permit Documents
        TRANSPORT_PERMIT,
        CROSS_BORDER_PERMIT,
        OVERSIZE_PERMIT,
        DANGEROUS_GOODS_PERMIT,

        // Load Documents
        PROOF_OF_DELIVERY,
        INVOICE,
        CONTRACT,
        WAYBILL,
        CUSTOMS_DECLARATION,

        // Other
        PROFILE_PHOTO,
        OTHER
    }
    
    public enum DocumentStatus {
        PENDING, VERIFIED, REJECTED, EXPIRED
    }
}
