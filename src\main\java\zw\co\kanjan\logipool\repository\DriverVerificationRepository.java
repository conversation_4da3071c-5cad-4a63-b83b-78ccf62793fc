package zw.co.kanjan.logipool.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import zw.co.kanjan.logipool.dto.driver.DriverDto;
import zw.co.kanjan.logipool.dto.driver.DriverVerificationDto;
import zw.co.kanjan.logipool.entity.DriverVerification;
import zw.co.kanjan.logipool.entity.User;

import java.util.List;
import java.util.Optional;

@Repository
public interface DriverVerificationRepository extends JpaRepository<DriverVerification, Long> {
    
    // Find all verifications for a specific driver
    List<DriverVerification> findByDriverOrderByCreatedAtDesc(User driver);
    
    // Find specific verification for a driver
    Optional<DriverVerification> findByDriverAndVerificationType(User driver, DriverVerificationDto.VerificationType verificationType);
    
    // Find verifications by status
    Page<DriverVerification> findByStatus(DriverDto.VerificationStatus status, Pageable pageable);
    
    // Find pending verifications
    Page<DriverVerification> findByStatusOrderByCreatedAtAsc(DriverDto.VerificationStatus status, Pageable pageable);
    
    // Count verifications by status for a driver
    @Query("SELECT COUNT(dv) FROM DriverVerification dv WHERE dv.driver = :driver AND dv.status = :status")
    Long countByDriverAndStatus(@Param("driver") User driver, @Param("status") DriverDto.VerificationStatus status);
    
    // Count total verifications for a driver
    Long countByDriver(User driver);
    
    // Find verifications by company (through driver's company membership)
    @Query("SELECT dv FROM DriverVerification dv " +
           "JOIN dv.driver d " +
           "JOIN d.companyMemberships cm " +
           "WHERE cm.company.id = :companyId AND cm.status = 'ACTIVE'")
    Page<DriverVerification> findByCompanyId(@Param("companyId") Long companyId, Pageable pageable);
    
    // Get verification statistics
    @Query("SELECT dv.status, COUNT(dv) FROM DriverVerification dv GROUP BY dv.status")
    List<Object[]> getVerificationStatusCounts();
    
    // Get verification statistics by type
    @Query("SELECT dv.verificationType, dv.status, COUNT(dv) FROM DriverVerification dv GROUP BY dv.verificationType, dv.status")
    List<Object[]> getVerificationTypeStatusCounts();
    
    // Find drivers with incomplete verifications
    @Query("SELECT DISTINCT dv.driver FROM DriverVerification dv WHERE dv.status IN ('PENDING', 'REJECTED')")
    Page<User> findDriversWithIncompleteVerifications(Pageable pageable);
    
    // Find drivers with all verifications completed
    @Query("SELECT d FROM User d " +
           "WHERE d.id NOT IN (" +
           "    SELECT DISTINCT dv.driver.id FROM DriverVerification dv " +
           "    WHERE dv.status IN ('PENDING', 'REJECTED')" +
           ") " +
           "AND EXISTS (" +
           "    SELECT 1 FROM DriverVerification dv2 " +
           "    WHERE dv2.driver = d AND dv2.status = 'VERIFIED'" +
           ")")
    Page<User> findDriversWithAllVerificationsCompleted(Pageable pageable);
    
    // Check if driver has all required verifications
    @Query("SELECT COUNT(DISTINCT dv.verificationType) FROM DriverVerification dv WHERE dv.driver = :driver")
    Long countVerificationTypesByDriver(@Param("driver") User driver);
    
    // Get latest verification update for a driver
    @Query("SELECT MAX(dv.updatedAt) FROM DriverVerification dv WHERE dv.driver = :driver")
    Optional<java.time.LocalDateTime> findLatestUpdateByDriver(@Param("driver") User driver);
    
    // Find verifications that need attention (pending for more than X days)
    @Query("SELECT dv FROM DriverVerification dv " +
           "WHERE dv.status = 'PENDING' " +
           "AND dv.createdAt < :cutoffDate " +
           "ORDER BY dv.createdAt ASC")
    List<DriverVerification> findStaleVerifications(@Param("cutoffDate") java.time.LocalDateTime cutoffDate);
    
    // Delete all verifications for a driver (for cleanup)
    void deleteByDriver(User driver);
}
