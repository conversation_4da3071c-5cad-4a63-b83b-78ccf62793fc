package zw.co.kanjan.logipool.dto.fleet;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

public class FleetDashboardDto {
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FleetOverviewResponse {
        
        // Truck Head Statistics
        private long totalTruckHeads;
        private long availableTruckHeads;
        private long assignedTruckHeads;
        private long inTransitTruckHeads;
        private long maintenanceTruckHeads;
        
        // Trailer Statistics
        private long totalTrailers;
        private long availableTrailers;
        private long assignedTrailers;
        private long inTransitTrailers;
        private long maintenanceTrailers;
        
        // Assignment Statistics
        private long totalAssignments;
        private long activeAssignments;
        private long plannedAssignments;
        private long completedAssignments;
        
        // Compliance Alerts
        private long truckHeadsWithExpiringInsurance;
        private long truckHeadsWithExpiringFitness;
        private long truckHeadsWithExpiringPermits;
        private long truckHeadsWithExpiringTax;
        private long trailersWithExpiringInsurance;
        private long trailersWithExpiringFitness;
        private long trailersWithExpiringPermits;
        
        // Calculated Metrics
        public double getTruckHeadUtilizationRate() {
            return totalTruckHeads > 0 ? (double) (assignedTruckHeads + inTransitTruckHeads) / totalTruckHeads * 100 : 0;
        }
        
        public double getTrailerUtilizationRate() {
            return totalTrailers > 0 ? (double) (assignedTrailers + inTransitTrailers) / totalTrailers * 100 : 0;
        }
        
        public long getTotalComplianceAlerts() {
            return truckHeadsWithExpiringInsurance + truckHeadsWithExpiringFitness + 
                   truckHeadsWithExpiringPermits + truckHeadsWithExpiringTax +
                   trailersWithExpiringInsurance + trailersWithExpiringFitness + 
                   trailersWithExpiringPermits;
        }
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ComplianceAlertResponse {
        
        private String alertType; // INSURANCE_EXPIRY, TAX_CLEARANCE_EXPIRY, PERMIT_EXPIRY, FITNESS_EXPIRY
        private String severity; // CRITICAL, HIGH, MEDIUM, LOW
        private String title;
        private String message;
        private String resourceType; // TRUCK_HEAD, TRAILER, VEHICLE, EQUIPMENT, COMPANY
        private Long resourceId;
        private String resourceName;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime expiryDate;
        
        private long daysToExpiry;
        
        public String getSeverityColor() {
            switch (severity) {
                case "CRITICAL": return "#FF0000"; // Red
                case "HIGH": return "#FF8C00"; // Dark Orange
                case "MEDIUM": return "#FFD700"; // Gold
                case "LOW": return "#32CD32"; // Lime Green
                default: return "#808080"; // Gray
            }
        }
        
        public String getAlertIcon() {
            switch (alertType) {
                case "INSURANCE_EXPIRY": return "shield-alert";
                case "TAX_CLEARANCE_EXPIRY": return "receipt-tax";
                case "PERMIT_EXPIRY": return "file-certificate";
                case "FITNESS_EXPIRY": return "clipboard-check";
                default: return "alert-triangle";
            }
        }
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FleetUtilizationResponse {
        
        private String resourceType; // TRUCK_HEAD, TRAILER
        private Long resourceId;
        private String resourceName;
        private long totalHours;
        private long assignedHours;
        private double utilizationPercentage;
        private int totalAssignments;
        
        public long getIdleHours() {
            return totalHours - assignedHours;
        }
        
        public String getUtilizationStatus() {
            if (utilizationPercentage >= 80) return "HIGH";
            if (utilizationPercentage >= 50) return "MEDIUM";
            if (utilizationPercentage >= 20) return "LOW";
            return "IDLE";
        }
        
        public String getUtilizationColor() {
            switch (getUtilizationStatus()) {
                case "HIGH": return "#32CD32"; // Lime Green
                case "MEDIUM": return "#FFD700"; // Gold
                case "LOW": return "#FF8C00"; // Dark Orange
                case "IDLE": return "#FF0000"; // Red
                default: return "#808080"; // Gray
            }
        }
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FleetMaintenanceResponse {
        
        private String resourceType;
        private Long resourceId;
        private String resourceName;
        private String maintenanceType; // SCHEDULED, BREAKDOWN, PREVENTIVE
        private String status; // PENDING, IN_PROGRESS, COMPLETED, OVERDUE
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime scheduledDate;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime completedDate;
        
        private String description;
        private String notes;
        private double estimatedCost;
        private double actualCost;
        private String currency;
        
        public long getDaysOverdue() {
            if (scheduledDate != null && completedDate == null) {
                LocalDateTime now = LocalDateTime.now();
                if (now.isAfter(scheduledDate)) {
                    return java.time.temporal.ChronoUnit.DAYS.between(scheduledDate.toLocalDate(), now.toLocalDate());
                }
            }
            return 0;
        }
        
        public boolean isOverdue() {
            return getDaysOverdue() > 0;
        }
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FleetPerformanceResponse {
        
        private String period; // DAILY, WEEKLY, MONTHLY, YEARLY
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime periodStart;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime periodEnd;
        
        private int totalLoadsCompleted;
        private double totalDistanceCovered;
        private String distanceUnit;
        private double totalRevenue;
        private String currency;
        private double averageLoadValue;
        private double fuelConsumption;
        private double maintenanceCosts;
        private int totalBreakdowns;
        private double averageUtilization;
        
        public double getRevenuePerKm() {
            return totalDistanceCovered > 0 ? totalRevenue / totalDistanceCovered : 0;
        }
        
        public double getMaintenanceCostPerKm() {
            return totalDistanceCovered > 0 ? maintenanceCosts / totalDistanceCovered : 0;
        }
        
        public double getProfitMargin() {
            return totalRevenue > 0 ? ((totalRevenue - maintenanceCosts) / totalRevenue) * 100 : 0;
        }
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FleetLocationResponse {
        
        private Long resourceId;
        private String resourceType;
        private String resourceName;
        private double latitude;
        private double longitude;
        private String currentLocation;
        
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime lastUpdated;
        
        private String status; // AVAILABLE, ASSIGNED, IN_TRANSIT, MAINTENANCE
        private Long currentLoadId;
        private String currentLoadTitle;
        private String driverName;
        private boolean isOnline;
        
        public String getStatusColor() {
            switch (status) {
                case "AVAILABLE": return "#32CD32"; // Lime Green
                case "ASSIGNED": return "#FFD700"; // Gold
                case "IN_TRANSIT": return "#1E90FF"; // Dodger Blue
                case "MAINTENANCE": return "#FF8C00"; // Dark Orange
                default: return "#808080"; // Gray
            }
        }
    }
}
