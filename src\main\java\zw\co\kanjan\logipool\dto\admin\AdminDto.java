package zw.co.kanjan.logipool.dto.admin;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import zw.co.kanjan.logipool.entity.User;
import zw.co.kanjan.logipool.entity.Company;
import zw.co.kanjan.logipool.entity.Load;
import zw.co.kanjan.logipool.entity.Role;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public class AdminDto {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DashboardOverview {
        private Long totalUsers;
        private Long totalCompanies;
        private Long totalLoads;
        private Long totalBids;
        private Long activeLoads;
        private Long completedLoads;
        private Long pendingVerifications;
        private BigDecimal totalRevenue;
        private BigDecimal monthlyRevenue;
        private List<RecentActivity> recentActivities;
        private SystemHealth systemHealth;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime generatedAt;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SystemHealth {
        private String status; // HEALTHY, WARNING, CRITICAL
        private Double cpuUsage;
        private Double memoryUsage;
        private Double diskUsage;
        private Long activeConnections;
        private Long databaseConnections;
        private List<String> alerts;
        private Map<String, Object> metrics;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RecentActivity {
        private String type; // USER_REGISTERED, LOAD_POSTED, BID_PLACED, etc.
        private String description;
        private String username;
        private Long entityId;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime timestamp;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserManagementResponse {
        private Long id;
        private String username;
        private String email;
        private String firstName;
        private String lastName;
        private String phoneNumber;
        private User.UserStatus status;
        private List<String> roles;
        private String companyName;
        private Long companyId;
        private Boolean isVerified;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime createdAt;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime lastLoginAt;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserUpdateRequest {
        @NotNull
        private Long userId;
        
        @Email
        private String email;
        
        private String firstName;
        private String lastName;
        private String phoneNumber;
        private User.UserStatus status;
        private List<Role.RoleName> roles;
        private String adminNotes;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CompanyManagementResponse {
        private Long id;
        private String name;
        private String registrationNumber;
        private String email;
        private String phoneNumber;
        private String address;
        private Company.VerificationStatus verificationStatus;
        private String ownerName;
        private String ownerUsername;
        private Long ownerId;
        private Integer totalVehicles;
        private Integer totalLoads;
        private BigDecimal totalRevenue;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime createdAt;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime verifiedAt;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CompanyVerificationRequest {
        @NotNull
        private Long companyId;

        @NotNull
        private Company.VerificationStatus status;

        private String verificationNotes;
        private String rejectionReason;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CompanySuspensionRequest {
        @NotBlank
        private String reason;

        private String notes;
        private Integer suspensionDurationDays; // null for indefinite
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CompanyUnsuspensionRequest {
        private String reason;
        private String notes;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LoadManagementResponse {
        private Long id;
        private String title;
        private String description;
        private String pickupLocation;
        private String deliveryLocation;
        private BigDecimal weight;
        private BigDecimal price;
        private Load.LoadStatus status;
        private String clientName;
        private String clientUsername;
        private Long clientId;
        private String assignedCompanyName;
        private Long assignedCompanyId;
        private Integer totalBids;
        private Boolean isVerified;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime createdAt;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime deliveryDate;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SystemAnalytics {
        private UserAnalytics userAnalytics;
        private LoadAnalytics loadAnalytics;
        private RevenueAnalytics revenueAnalytics;
        private PerformanceAnalytics performanceAnalytics;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime generatedAt;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserAnalytics {
        private Long totalUsers;
        private Long activeUsers;
        private Long newUsersThisMonth;
        private Long clientUsers;
        private Long transporterUsers;
        private Long adminUsers;
        private Map<String, Long> usersByStatus;
        private Map<String, Long> registrationTrends; // Date -> Count
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LoadAnalytics {
        private Long totalLoads;
        private Long activeLoads;
        private Long completedLoads;
        private Long cancelledLoads;
        private Map<String, Long> loadsByStatus;
        private Map<String, Long> loadTrends; // Date -> Count
        private BigDecimal averageLoadValue;
        private BigDecimal totalLoadValue;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RevenueAnalytics {
        private BigDecimal totalRevenue;
        private BigDecimal monthlyRevenue;
        private BigDecimal dailyRevenue;
        private BigDecimal averageCommission;
        private Map<String, BigDecimal> revenueByMonth;
        private Map<String, BigDecimal> revenueByCompany;
        private BigDecimal projectedMonthlyRevenue;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PerformanceAnalytics {
        private Double averageResponseTime;
        private Long totalApiCalls;
        private Long errorCount;
        private Double errorRate;
        private Map<String, Long> endpointUsage;
        private Map<String, Double> performanceMetrics;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SystemConfigurationResponse {
        private Map<String, Object> applicationSettings;
        private Map<String, Object> securitySettings;
        private Map<String, Object> notificationSettings;
        private Map<String, Object> paymentSettings;
        private Map<String, Object> trackingSettings;
        private Map<String, Object> fileSettings;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SystemConfigurationUpdateRequest {
        @NotBlank
        private String category; // application, security, notification, payment, tracking, file
        
        @NotNull
        private Map<String, Object> settings;
        
        private String updateReason;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AuditLogResponse {
        private Long id;
        private String action;
        private String entityType;
        private Long entityId;
        private String adminUsername;
        private String adminName;
        private String details;
        private String ipAddress;
        private String userAgent;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime timestamp;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SystemAlertResponse {
        private Long id;
        private String type; // SECURITY, PERFORMANCE, BUSINESS, SYSTEM
        private String severity; // LOW, MEDIUM, HIGH, CRITICAL
        private String title;
        private String message;
        private String source;
        private Boolean isResolved;
        private String resolvedBy;
        private String resolution;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime createdAt;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime resolvedAt;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AlertResolutionRequest {
        @NotNull
        private Long alertId;
        
        @NotBlank
        private String resolution;
        
        private String notes;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BulkActionRequest {
        @NotNull
        @Size(min = 1)
        private List<Long> entityIds;
        
        @NotBlank
        private String action; // ACTIVATE, DEACTIVATE, DELETE, VERIFY, REJECT
        
        private String reason;
        private Map<String, Object> parameters;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BulkActionResponse {
        private Integer totalRequested;
        private Integer successful;
        private Integer failed;
        private List<String> errors;
        private List<Long> processedIds;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime processedAt;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SystemBackupResponse {
        private String backupId;
        private String type; // FULL, INCREMENTAL, CONFIGURATION
        private String status; // PENDING, IN_PROGRESS, COMPLETED, FAILED
        private Long sizeInBytes;
        private String location;
        private String initiatedBy;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime initiatedAt;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime completedAt;
    }
}
