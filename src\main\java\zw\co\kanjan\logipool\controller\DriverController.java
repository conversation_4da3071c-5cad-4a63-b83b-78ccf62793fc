package zw.co.kanjan.logipool.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import zw.co.kanjan.logipool.dto.driver.DriverDto;
import zw.co.kanjan.logipool.dto.driver.DriverVerificationDto;
import zw.co.kanjan.logipool.service.DriverService;

import java.security.Principal;

@RestController
@RequestMapping("/api/drivers")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Drivers", description = "Driver management APIs")
public class DriverController {
    
    private final DriverService driverService;
    
    @PostMapping("/register")
    @Operation(summary = "Register new driver", description = "Register a new driver in the system")
    @PreAuthorize("hasRole('ADMIN') or hasRole('COMPANY_OWNER') or hasRole('COMPANY_ADMIN')")
    public ResponseEntity<DriverDto.DriverResponse> registerDriver(
            @Valid @RequestBody DriverDto.DriverRegistrationRequest request,
            Principal principal) {
        
        log.info("Registering new driver with email: {} by user: {}", request.getEmail(), principal.getName());
        DriverDto.DriverResponse response = driverService.registerDriver(request, principal.getName());
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }
    
    @PostMapping("/self-register")
    @Operation(summary = "Driver self-registration", description = "Allow drivers to register themselves")
    public ResponseEntity<DriverDto.DriverResponse> selfRegisterDriver(
            @Valid @RequestBody DriverDto.DriverRegistrationRequest request) {
        
        log.info("Driver self-registering with email: {}", request.getEmail());
        DriverDto.DriverResponse response = driverService.selfRegisterDriver(request);
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }
    
    @GetMapping
    @Operation(summary = "Get all drivers", description = "Get paginated list of all drivers")
    @PreAuthorize("hasRole('ADMIN') or hasRole('COMPANY_OWNER') or hasRole('COMPANY_ADMIN')")
    public ResponseEntity<Page<DriverDto.DriverResponse>> getAllDrivers(
            Pageable pageable,
            Principal principal) {
        
        log.info("Getting all drivers for user: {}", principal.getName());
        Page<DriverDto.DriverResponse> response = driverService.getAllDrivers(pageable, principal.getName());
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/{id}")
    @Operation(summary = "Get driver by ID", description = "Get driver details by ID")
    @PreAuthorize("hasRole('ADMIN') or hasRole('COMPANY_OWNER') or hasRole('COMPANY_ADMIN') or hasRole('DRIVER')")
    public ResponseEntity<DriverDto.DriverResponse> getDriverById(
            @Parameter(description = "Driver ID") @PathVariable Long id,
            Principal principal) {
        
        log.info("Getting driver {} for user: {}", id, principal.getName());
        DriverDto.DriverResponse response = driverService.getDriverById(id, principal.getName());
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/profile")
    @Operation(summary = "Get current driver profile", description = "Get the profile of the currently authenticated driver")
    @PreAuthorize("hasRole('DRIVER')")
    public ResponseEntity<DriverDto.DriverResponse> getCurrentDriverProfile(Principal principal) {
        
        log.info("Getting current driver profile for user: {}", principal.getName());
        DriverDto.DriverResponse response = driverService.getCurrentDriverProfile(principal.getName());
        return ResponseEntity.ok(response);
    }
    
    @PutMapping("/{id}")
    @Operation(summary = "Update driver profile", description = "Update driver profile information")
    @PreAuthorize("hasRole('ADMIN') or hasRole('COMPANY_OWNER') or hasRole('COMPANY_ADMIN') or (hasRole('DRIVER') and #id == authentication.principal.id)")
    public ResponseEntity<DriverDto.DriverResponse> updateDriver(
            @Parameter(description = "Driver ID") @PathVariable Long id,
            @Valid @RequestBody DriverDto.DriverUpdateRequest request,
            Principal principal) {
        
        log.info("Updating driver {} by user: {}", id, principal.getName());
        DriverDto.DriverResponse response = driverService.updateDriver(id, request, principal.getName());
        return ResponseEntity.ok(response);
    }
    
    @PutMapping("/profile")
    @Operation(summary = "Update current driver profile", description = "Update the profile of the currently authenticated driver")
    @PreAuthorize("hasRole('DRIVER')")
    public ResponseEntity<DriverDto.DriverResponse> updateCurrentDriverProfile(
            @Valid @RequestBody DriverDto.DriverUpdateRequest request,
            Principal principal) {
        
        log.info("Updating current driver profile for user: {}", principal.getName());
        DriverDto.DriverResponse response = driverService.updateCurrentDriverProfile(request, principal.getName());
        return ResponseEntity.ok(response);
    }
    
    @PostMapping("/assign-to-company")
    @Operation(summary = "Assign driver to company", description = "Assign a driver to a company as a member")
    @PreAuthorize("hasRole('ADMIN') or hasRole('COMPANY_OWNER') or hasRole('COMPANY_ADMIN')")
    public ResponseEntity<DriverDto.DriverResponse> assignDriverToCompany(
            @Valid @RequestBody DriverDto.AssignToCompanyRequest request,
            Principal principal) {
        
        log.info("Assigning driver {} to company {} by user: {}", 
                request.getDriverId(), request.getCompanyId(), principal.getName());
        DriverDto.DriverResponse response = driverService.assignDriverToCompany(request, principal.getName());
        return ResponseEntity.ok(response);
    }
    
    @DeleteMapping("/{id}/company")
    @Operation(summary = "Remove driver from company", description = "Remove a driver from their current company")
    @PreAuthorize("hasRole('ADMIN') or hasRole('COMPANY_OWNER') or hasRole('COMPANY_ADMIN')")
    public ResponseEntity<Void> removeDriverFromCompany(
            @Parameter(description = "Driver ID") @PathVariable Long id,
            Principal principal) {
        
        log.info("Removing driver {} from company by user: {}", id, principal.getName());
        driverService.removeDriverFromCompany(id, principal.getName());
        return ResponseEntity.noContent().build();
    }
    
    @GetMapping("/company/{companyId}")
    @Operation(summary = "Get drivers by company", description = "Get all drivers for a specific company")
    @PreAuthorize("hasRole('ADMIN') or hasRole('COMPANY_OWNER') or hasRole('COMPANY_ADMIN')")
    public ResponseEntity<Page<DriverDto.DriverResponse>> getDriversByCompany(
            @Parameter(description = "Company ID") @PathVariable Long companyId,
            Pageable pageable,
            Principal principal) {
        
        log.info("Getting drivers for company {} by user: {}", companyId, principal.getName());
        Page<DriverDto.DriverResponse> response = driverService.getDriversByCompany(companyId, pageable, principal.getName());
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/unassigned")
    @Operation(summary = "Get unassigned drivers", description = "Get all drivers not assigned to any company")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Page<DriverDto.DriverResponse>> getUnassignedDrivers(
            Pageable pageable,
            Principal principal) {
        
        log.info("Getting unassigned drivers for user: {}", principal.getName());
        Page<DriverDto.DriverResponse> response = driverService.getUnassignedDrivers(pageable, principal.getName());
        return ResponseEntity.ok(response);
    }
    
    @DeleteMapping("/{id}")
    @Operation(summary = "Delete driver", description = "Delete a driver from the system")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteDriver(
            @Parameter(description = "Driver ID") @PathVariable Long id,
            Principal principal) {
        
        log.info("Deleting driver {} by user: {}", id, principal.getName());
        driverService.deleteDriver(id, principal.getName());
        return ResponseEntity.noContent().build();
    }
    
    // Verification endpoints
    
    @GetMapping("/{id}/verification")
    @Operation(summary = "Get driver verification status", description = "Get verification status for a specific driver")
    @PreAuthorize("hasRole('ADMIN') or hasRole('COMPANY_OWNER') or hasRole('COMPANY_ADMIN') or (hasRole('DRIVER') and #id == authentication.principal.id)")
    public ResponseEntity<DriverVerificationDto.VerificationSummaryResponse> getDriverVerificationStatus(
            @Parameter(description = "Driver ID") @PathVariable Long id,
            Principal principal) {
        
        log.info("Getting verification status for driver {} by user: {}", id, principal.getName());
        DriverVerificationDto.VerificationSummaryResponse response = driverService.getDriverVerificationStatus(id, principal.getName());
        return ResponseEntity.ok(response);
    }
    
    @PutMapping("/{id}/verification")
    @Operation(summary = "Update driver verification", description = "Update verification status for a driver")
    @PreAuthorize("hasRole('ADMIN') or hasRole('COMPANY_OWNER') or hasRole('COMPANY_ADMIN')")
    public ResponseEntity<DriverVerificationDto.VerificationSummaryResponse> updateDriverVerification(
            @Parameter(description = "Driver ID") @PathVariable Long id,
            @Valid @RequestBody DriverVerificationDto.UpdateVerificationRequest request,
            Principal principal) {
        
        log.info("Updating verification for driver {} by user: {}", id, principal.getName());
        request.setDriverId(id); // Ensure consistency
        DriverVerificationDto.VerificationSummaryResponse response = driverService.updateDriverVerification(request, principal.getName());
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/verification/stats")
    @Operation(summary = "Get verification statistics", description = "Get overall verification statistics")
    @PreAuthorize("hasRole('ADMIN') or hasRole('COMPANY_OWNER') or hasRole('COMPANY_ADMIN')")
    public ResponseEntity<DriverVerificationDto.VerificationStatsResponse> getVerificationStats(
            Principal principal) {
        
        log.info("Getting verification statistics for user: {}", principal.getName());
        DriverVerificationDto.VerificationStatsResponse response = driverService.getVerificationStats(principal.getName());
        return ResponseEntity.ok(response);
    }
}
