package zw.co.kanjan.logipool.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import zw.co.kanjan.logipool.dto.load.LoadCreateRequest;
import zw.co.kanjan.logipool.dto.load.LoadResponse;
import zw.co.kanjan.logipool.entity.Load;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface LoadMapper {
    
    Load toEntity(LoadCreateRequest request);
    
    @Mapping(source = "client.id", target = "clientId")
    @Mapping(source = "client.firstName", target = "clientName")
    @Mapping(source = "assignedCompany.id", target = "assignedCompanyId")
    @Mapping(source = "assignedCompany.name", target = "assignedCompanyName")
    @Mapping(expression = "java(load.getBids() != null ? load.getBids().size() : 0)", target = "bidCount")
    @Mapping(expression = "java(getActiveFleetAssignment(load))", target = "fleetAssignmentId")
    @Mapping(expression = "java(getActiveFleetAssignmentTruckHeadId(load))", target = "assignedTruckHeadId")
    @Mapping(expression = "java(getActiveFleetAssignmentTruckHeadRegistration(load))", target = "assignedTruckHeadRegistration")
    @Mapping(expression = "java(getActiveFleetAssignmentTruckHeadMake(load))", target = "assignedTruckHeadMake")
    @Mapping(expression = "java(getActiveFleetAssignmentTruckHeadModel(load))", target = "assignedTruckHeadModel")
    @Mapping(expression = "java(getActiveFleetAssignmentTrailer1Id(load))", target = "assignedTrailer1Id")
    @Mapping(expression = "java(getActiveFleetAssignmentTrailer1Registration(load))", target = "assignedTrailer1Registration")
    @Mapping(expression = "java(getActiveFleetAssignmentTrailer1Type(load))", target = "assignedTrailer1Type")
    @Mapping(expression = "java(getActiveFleetAssignmentTrailer2Id(load))", target = "assignedTrailer2Id")
    @Mapping(expression = "java(getActiveFleetAssignmentTrailer2Registration(load))", target = "assignedTrailer2Registration")
    @Mapping(expression = "java(getActiveFleetAssignmentTrailer2Type(load))", target = "assignedTrailer2Type")
    @Mapping(expression = "java(getActiveFleetAssignmentPrimaryDriverId(load))", target = "assignedPrimaryDriverId")
    @Mapping(expression = "java(getActiveFleetAssignmentPrimaryDriverName(load))", target = "assignedPrimaryDriverName")
    @Mapping(expression = "java(getActiveFleetAssignmentSecondaryDriverId(load))", target = "assignedSecondaryDriverId")
    @Mapping(expression = "java(getActiveFleetAssignmentSecondaryDriverName(load))", target = "assignedSecondaryDriverName")
    @Mapping(expression = "java(getActiveFleetAssignmentStatus(load))", target = "fleetAssignmentStatus")
    @Mapping(expression = "java(getActiveFleetAssignmentScheduledStartTime(load))", target = "fleetAssignmentScheduledStartTime")
    @Mapping(expression = "java(getActiveFleetAssignmentScheduledEndTime(load))", target = "fleetAssignmentScheduledEndTime")
    LoadResponse toResponse(Load load);

    // Helper methods for fleet assignment mapping
    default Long getActiveFleetAssignment(Load load) {
        return load.getFleetAssignments().stream()
                .filter(assignment -> assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.ACTIVE ||
                                    assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.PLANNED ||
                                    assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.IN_PROGRESS)
                .findFirst()
                .map(zw.co.kanjan.logipool.entity.FleetAssignment::getId)
                .orElse(null);
    }

    default Long getActiveFleetAssignmentTruckHeadId(Load load) {
        return load.getFleetAssignments().stream()
                .filter(assignment -> assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.ACTIVE ||
                                    assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.PLANNED ||
                                    assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.IN_PROGRESS)
                .findFirst()
                .map(assignment -> assignment.getTruckHead() != null ? assignment.getTruckHead().getId() : null)
                .orElse(null);
    }

    default String getActiveFleetAssignmentTruckHeadRegistration(Load load) {
        return load.getFleetAssignments().stream()
                .filter(assignment -> assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.ACTIVE ||
                                    assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.PLANNED ||
                                    assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.IN_PROGRESS)
                .findFirst()
                .map(assignment -> assignment.getTruckHead() != null ? assignment.getTruckHead().getRegistrationNumber() : null)
                .orElse(null);
    }

    default String getActiveFleetAssignmentTruckHeadMake(Load load) {
        return load.getFleetAssignments().stream()
                .filter(assignment -> assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.ACTIVE ||
                                    assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.PLANNED ||
                                    assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.IN_PROGRESS)
                .findFirst()
                .map(assignment -> assignment.getTruckHead() != null ? assignment.getTruckHead().getMake() : null)
                .orElse(null);
    }

    default String getActiveFleetAssignmentTruckHeadModel(Load load) {
        return load.getFleetAssignments().stream()
                .filter(assignment -> assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.ACTIVE ||
                                    assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.PLANNED ||
                                    assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.IN_PROGRESS)
                .findFirst()
                .map(assignment -> assignment.getTruckHead() != null ? assignment.getTruckHead().getModel() : null)
                .orElse(null);
    }

    default Long getActiveFleetAssignmentTrailer1Id(Load load) {
        return load.getFleetAssignments().stream()
                .filter(assignment -> assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.ACTIVE ||
                                    assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.PLANNED ||
                                    assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.IN_PROGRESS)
                .findFirst()
                .map(assignment -> assignment.getTrailer1() != null ? assignment.getTrailer1().getId() : null)
                .orElse(null);
    }

    default String getActiveFleetAssignmentTrailer1Registration(Load load) {
        return load.getFleetAssignments().stream()
                .filter(assignment -> assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.ACTIVE ||
                                    assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.PLANNED ||
                                    assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.IN_PROGRESS)
                .findFirst()
                .map(assignment -> assignment.getTrailer1() != null ? assignment.getTrailer1().getRegistrationNumber() : null)
                .orElse(null);
    }

    default String getActiveFleetAssignmentTrailer1Type(Load load) {
        return load.getFleetAssignments().stream()
                .filter(assignment -> assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.ACTIVE ||
                                    assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.PLANNED ||
                                    assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.IN_PROGRESS)
                .findFirst()
                .map(assignment -> assignment.getTrailer1() != null ? assignment.getTrailer1().getType().toString() : null)
                .orElse(null);
    }

    default Long getActiveFleetAssignmentTrailer2Id(Load load) {
        return load.getFleetAssignments().stream()
                .filter(assignment -> assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.ACTIVE ||
                                    assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.PLANNED ||
                                    assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.IN_PROGRESS)
                .findFirst()
                .map(assignment -> assignment.getTrailer2() != null ? assignment.getTrailer2().getId() : null)
                .orElse(null);
    }

    default String getActiveFleetAssignmentTrailer2Registration(Load load) {
        return load.getFleetAssignments().stream()
                .filter(assignment -> assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.ACTIVE ||
                                    assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.PLANNED ||
                                    assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.IN_PROGRESS)
                .findFirst()
                .map(assignment -> assignment.getTrailer2() != null ? assignment.getTrailer2().getRegistrationNumber() : null)
                .orElse(null);
    }

    default String getActiveFleetAssignmentTrailer2Type(Load load) {
        return load.getFleetAssignments().stream()
                .filter(assignment -> assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.ACTIVE ||
                                    assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.PLANNED ||
                                    assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.IN_PROGRESS)
                .findFirst()
                .map(assignment -> assignment.getTrailer2() != null ? assignment.getTrailer2().getType().toString() : null)
                .orElse(null);
    }

    default Long getActiveFleetAssignmentPrimaryDriverId(Load load) {
        return load.getFleetAssignments().stream()
                .filter(assignment -> assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.ACTIVE ||
                                    assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.PLANNED ||
                                    assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.IN_PROGRESS)
                .findFirst()
                .map(assignment -> assignment.getPrimaryDriver() != null ? assignment.getPrimaryDriver().getId() : null)
                .orElse(null);
    }

    default String getActiveFleetAssignmentPrimaryDriverName(Load load) {
        return load.getFleetAssignments().stream()
                .filter(assignment -> assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.ACTIVE ||
                                    assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.PLANNED ||
                                    assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.IN_PROGRESS)
                .findFirst()
                .map(assignment -> assignment.getPrimaryDriver() != null ?
                    assignment.getPrimaryDriver().getFirstName() + " " + assignment.getPrimaryDriver().getLastName() : null)
                .orElse(null);
    }

    default Long getActiveFleetAssignmentSecondaryDriverId(Load load) {
        return load.getFleetAssignments().stream()
                .filter(assignment -> assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.ACTIVE ||
                                    assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.PLANNED ||
                                    assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.IN_PROGRESS)
                .findFirst()
                .map(assignment -> assignment.getSecondaryDriver() != null ? assignment.getSecondaryDriver().getId() : null)
                .orElse(null);
    }

    default String getActiveFleetAssignmentSecondaryDriverName(Load load) {
        return load.getFleetAssignments().stream()
                .filter(assignment -> assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.ACTIVE ||
                                    assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.PLANNED ||
                                    assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.IN_PROGRESS)
                .findFirst()
                .map(assignment -> assignment.getSecondaryDriver() != null ?
                    assignment.getSecondaryDriver().getFirstName() + " " + assignment.getSecondaryDriver().getLastName() : null)
                .orElse(null);
    }

    default String getActiveFleetAssignmentStatus(Load load) {
        return load.getFleetAssignments().stream()
                .filter(assignment -> assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.ACTIVE ||
                                    assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.PLANNED ||
                                    assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.IN_PROGRESS)
                .findFirst()
                .map(assignment -> assignment.getStatus().toString())
                .orElse(null);
    }

    default java.time.LocalDateTime getActiveFleetAssignmentScheduledStartTime(Load load) {
        return load.getFleetAssignments().stream()
                .filter(assignment -> assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.ACTIVE ||
                                    assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.PLANNED ||
                                    assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.IN_PROGRESS)
                .findFirst()
                .map(zw.co.kanjan.logipool.entity.FleetAssignment::getScheduledStartTime)
                .orElse(null);
    }

    default java.time.LocalDateTime getActiveFleetAssignmentScheduledEndTime(Load load) {
        return load.getFleetAssignments().stream()
                .filter(assignment -> assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.ACTIVE ||
                                    assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.PLANNED ||
                                    assignment.getStatus() == zw.co.kanjan.logipool.entity.FleetAssignment.AssignmentStatus.IN_PROGRESS)
                .findFirst()
                .map(zw.co.kanjan.logipool.entity.FleetAssignment::getScheduledEndTime)
                .orElse(null);
    }
}
