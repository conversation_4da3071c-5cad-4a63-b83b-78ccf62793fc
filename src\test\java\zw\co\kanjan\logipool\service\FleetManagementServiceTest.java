package zw.co.kanjan.logipool.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import zw.co.kanjan.logipool.dto.fleet.FleetAssignmentDto;
import zw.co.kanjan.logipool.entity.*;
import zw.co.kanjan.logipool.exception.BusinessException;
import zw.co.kanjan.logipool.exception.ResourceNotFoundException;
import zw.co.kanjan.logipool.mapper.fleet.FleetAssignmentMapper;
import zw.co.kanjan.logipool.repository.*;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class FleetManagementServiceTest {

    @Mock
    private FleetAssignmentRepository fleetAssignmentRepository;

    @Mock
    private LoadRepository loadRepository;

    @Mock
    private BidRepository bidRepository;

    @Mock
    private TruckHeadRepository truckHeadRepository;

    @Mock
    private TrailerRepository trailerRepository;

    @Mock
    private UserRepository userRepository;

    @Mock
    private CompanyRepository companyRepository;

    @Mock
    private FleetAssignmentMapper fleetAssignmentMapper;

    @InjectMocks
    private FleetManagementService fleetManagementService;

    private User testUser;
    private Company testCompany;
    private Load testLoad;
    private Bid testBid;
    private TruckHead testTruckHead;
    private Trailer testTrailer;
    private FleetAssignment testFleetAssignment;
    private FleetAssignmentDto.FleetAssignmentCreateRequest createRequest;
    private FleetAssignmentDto.FleetAssignmentUpdateRequest updateRequest;
    private FleetAssignmentDto.FleetAssignmentResponse response;

    @BeforeEach
    void setUp() {
        testUser = User.builder()
                .id(1L)
                .username("testuser")
                .firstName("Test")
                .lastName("User")
                .build();

        testCompany = Company.builder()
                .id(1L)
                .name("Test Transport Company")
                .user(testUser) // Set the user for the company
                .build();

        // Update the user's company reference
        testUser.setCompany(testCompany);

        User client = User.builder()
                .id(2L)
                .username("client")
                .firstName("Client")
                .lastName("User")
                .build();

        testLoad = Load.builder()
                .id(1L)
                .title("Test Load")
                .trackingNumber("TL001")
                .client(client)
                .assignedCompany(testCompany)
                .build();

        testBid = Bid.builder()
                .id(1L)
                .load(testLoad)
                .company(testCompany)
                .build();

        testTruckHead = TruckHead.builder()
                .id(1L)
                .registrationNumber("ABC123")
                .make("Volvo")
                .model("FH16")
                .status(TruckHead.TruckStatus.AVAILABLE)
                .company(testCompany)
                .build();

        testTrailer = Trailer.builder()
                .id(1L)
                .registrationNumber("TRL001")
                .make("Schmitz")
                .type(Trailer.TrailerType.CURTAIN_SIDE)
                .status(Trailer.TrailerStatus.AVAILABLE)
                .company(testCompany)
                .build();

        testFleetAssignment = FleetAssignment.builder()
                .id(1L)
                .load(testLoad)
                .bid(testBid)
                .truckHead(testTruckHead)
                .trailer1(testTrailer)
                .primaryDriver(testUser)
                .company(testCompany)
                .status(FleetAssignment.AssignmentStatus.PLANNED)
                .scheduledStartTime(LocalDateTime.now().plusDays(1))
                .scheduledEndTime(LocalDateTime.now().plusDays(2))
                .assignedBy(testUser)
                .assignedAt(LocalDateTime.now())
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        createRequest = FleetAssignmentDto.FleetAssignmentCreateRequest.builder()
                .loadId(1L)
                .bidId(1L)
                .truckHeadId(1L)
                .trailer1Id(1L)
                .primaryDriverId(1L)
                .scheduledStartTime(LocalDateTime.now().plusDays(1))
                .scheduledEndTime(LocalDateTime.now().plusDays(2))
                .notes("Test assignment")
                .build();

        updateRequest = FleetAssignmentDto.FleetAssignmentUpdateRequest.builder()
                .status(FleetAssignment.AssignmentStatus.ACTIVE)
                .actualStartTime(LocalDateTime.now())
                .notes("Updated assignment")
                .build();

        response = FleetAssignmentDto.FleetAssignmentResponse.builder()
                .id(1L)
                .loadId(1L)
                .loadTitle("Test Load")
                .loadTrackingNumber("TL001")
                .bidId(1L)
                .truckHeadId(1L)
                .truckHeadRegistration("ABC123")
                .trailer1Id(1L)
                .trailer1Registration("TRL001")
                .primaryDriverId(1L)
                .primaryDriverName("Test User")
                .companyId(1L)
                .companyName("Test Transport Company")
                .status(FleetAssignment.AssignmentStatus.PLANNED)
                .build();
    }

    @Test
    void createFleetAssignment_Success() {
        // Given
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));
        when(loadRepository.findById(1L)).thenReturn(Optional.of(testLoad));
        when(bidRepository.findById(1L)).thenReturn(Optional.of(testBid));
        when(truckHeadRepository.findById(1L)).thenReturn(Optional.of(testTruckHead));
        when(trailerRepository.findById(1L)).thenReturn(Optional.of(testTrailer));
        when(userRepository.findById(1L)).thenReturn(Optional.of(testUser));
        when(fleetAssignmentRepository.findConflictingTruckHeadAssignments(any(), any(), any()))
                .thenReturn(Arrays.asList());
        when(fleetAssignmentMapper.toEntity(createRequest)).thenReturn(testFleetAssignment);
        when(fleetAssignmentRepository.save(any(FleetAssignment.class))).thenReturn(testFleetAssignment);
        when(fleetAssignmentMapper.toResponse(testFleetAssignment)).thenReturn(response);

        // When
        FleetAssignmentDto.FleetAssignmentResponse result = fleetManagementService.createFleetAssignment(
                createRequest, "testuser");

        // Then
        assertNotNull(result);
        assertEquals(1L, result.getId());
        assertEquals("Test Load", result.getLoadTitle());
        verify(fleetAssignmentRepository).save(any(FleetAssignment.class));
        verify(truckHeadRepository).save(testTruckHead);
        verify(trailerRepository).save(testTrailer);
    }

    @Test
    void createFleetAssignment_LoadNotFound_ThrowsException() {
        // Given
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));
        when(loadRepository.findById(1L)).thenReturn(Optional.empty());

        // When & Then
        ResourceNotFoundException exception = assertThrows(ResourceNotFoundException.class,
                () -> fleetManagementService.createFleetAssignment(createRequest, "testuser"));

        assertEquals("Load not found with id: 1", exception.getMessage());
    }

    @Test
    void createFleetAssignment_LoadNotAssignedToCompany_ThrowsException() {
        // Given
        testLoad.setAssignedCompany(null);
        
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));
        when(loadRepository.findById(1L)).thenReturn(Optional.of(testLoad));

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> fleetManagementService.createFleetAssignment(createRequest, "testuser"));

        assertEquals("Load must be assigned to your company before creating fleet assignment", exception.getMessage());
    }

    @Test
    void updateFleetAssignment_Success() {
        // Given
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));
        when(fleetAssignmentRepository.findById(1L)).thenReturn(Optional.of(testFleetAssignment));
        when(fleetAssignmentRepository.save(any(FleetAssignment.class))).thenReturn(testFleetAssignment);
        when(fleetAssignmentMapper.toResponse(testFleetAssignment)).thenReturn(response);

        // When
        FleetAssignmentDto.FleetAssignmentResponse result = fleetManagementService.updateFleetAssignment(
                1L, updateRequest, "testuser");

        // Then
        assertNotNull(result);
        verify(fleetAssignmentRepository).save(testFleetAssignment);
    }

    @Test
    void updateFleetAssignment_NotFound_ThrowsException() {
        // Given
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));
        when(fleetAssignmentRepository.findById(1L)).thenReturn(Optional.empty());

        // When & Then
        ResourceNotFoundException exception = assertThrows(ResourceNotFoundException.class,
                () -> fleetManagementService.updateFleetAssignment(1L, updateRequest, "testuser"));

        assertEquals("Fleet assignment not found with id: 1", exception.getMessage());
    }

    @Test
    void updateFleetAssignment_WrongCompany_ThrowsException() {
        // Given
        Company otherCompany = Company.builder().id(2L).name("Other Company").build();
        testFleetAssignment.setCompany(otherCompany);
        
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));
        when(fleetAssignmentRepository.findById(1L)).thenReturn(Optional.of(testFleetAssignment));

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> fleetManagementService.updateFleetAssignment(1L, updateRequest, "testuser"));

        assertEquals("You can only update your own fleet assignments", exception.getMessage());
    }

    @Test
    void getFleetAssignment_Success() {
        // Given
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));
        when(fleetAssignmentRepository.findById(1L)).thenReturn(Optional.of(testFleetAssignment));
        when(fleetAssignmentMapper.toResponse(testFleetAssignment)).thenReturn(response);

        // When
        FleetAssignmentDto.FleetAssignmentResponse result = fleetManagementService.getFleetAssignment(1L, "testuser");

        // Then
        assertNotNull(result);
        assertEquals(1L, result.getId());
    }

    @Test
    void getFleetAssignments_Success() {
        // Given
        List<FleetAssignment> assignments = Arrays.asList(testFleetAssignment);
        Page<FleetAssignment> page = new PageImpl<>(assignments);
        Pageable pageable = PageRequest.of(0, 10);
        
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));
        when(fleetAssignmentRepository.findByCompany(testCompany, pageable)).thenReturn(page);
        when(fleetAssignmentMapper.toResponse(testFleetAssignment)).thenReturn(response);

        // When
        Page<FleetAssignmentDto.FleetAssignmentResponse> result = fleetManagementService.getFleetAssignments(
                "testuser", pageable, null);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getContent().size());
    }

    @Test
    void getFleetAssignmentByLoad_Success() {
        // Given
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));
        when(loadRepository.findById(1L)).thenReturn(Optional.of(testLoad));
        when(fleetAssignmentRepository.findByLoadAndStatus(testLoad, FleetAssignment.AssignmentStatus.ACTIVE))
                .thenReturn(Optional.of(testFleetAssignment));
        when(fleetAssignmentMapper.toResponse(testFleetAssignment)).thenReturn(response);

        // When
        FleetAssignmentDto.FleetAssignmentResponse result = fleetManagementService.getFleetAssignmentByLoad(1L, "testuser");

        // Then
        assertNotNull(result);
        assertEquals(1L, result.getId());
    }

    @Test
    void checkFleetAvailability_Available_Success() {
        // Given
        FleetAssignmentDto.FleetAvailabilityRequest availabilityRequest = 
                FleetAssignmentDto.FleetAvailabilityRequest.builder()
                        .startTime(LocalDateTime.now().plusDays(1))
                        .endTime(LocalDateTime.now().plusDays(2))
                        .build();
        
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));
        when(truckHeadRepository.findById(1L)).thenReturn(Optional.of(testTruckHead));
        when(fleetAssignmentRepository.findConflictingTruckHeadAssignments(any(), any(), any()))
                .thenReturn(Arrays.asList());

        // When
        FleetAssignmentDto.FleetAvailabilityResponse result = fleetManagementService.checkFleetAvailability(
                1L, "truck", availabilityRequest, "testuser");

        // Then
        assertNotNull(result);
        assertTrue(result.isAvailable());
    }

    @Test
    void checkFleetAvailability_NotAvailable_Success() {
        // Given
        FleetAssignmentDto.FleetAvailabilityRequest availabilityRequest = 
                FleetAssignmentDto.FleetAvailabilityRequest.builder()
                        .startTime(LocalDateTime.now().plusDays(1))
                        .endTime(LocalDateTime.now().plusDays(2))
                        .build();
        
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));
        when(truckHeadRepository.findById(1L)).thenReturn(Optional.of(testTruckHead));
        when(fleetAssignmentRepository.findConflictingTruckHeadAssignments(any(), any(), any()))
                .thenReturn(Arrays.asList(testFleetAssignment));

        // When
        FleetAssignmentDto.FleetAvailabilityResponse result = fleetManagementService.checkFleetAvailability(
                1L, "truck", availabilityRequest, "testuser");

        // Then
        assertNotNull(result);
        assertFalse(result.isAvailable());
        assertNotNull(result.getReason());
    }

    @Test
    void deleteFleetAssignment_Success() {
        // Given
        testFleetAssignment.setStatus(FleetAssignment.AssignmentStatus.PLANNED);
        
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));
        when(fleetAssignmentRepository.findById(1L)).thenReturn(Optional.of(testFleetAssignment));

        // When
        fleetManagementService.deleteFleetAssignment(1L, "testuser");

        // Then
        verify(fleetAssignmentRepository).delete(testFleetAssignment);
        verify(truckHeadRepository).save(testTruckHead);
        verify(trailerRepository).save(testTrailer);
    }

    @Test
    void deleteFleetAssignment_ActiveAssignment_ThrowsException() {
        // Given
        testFleetAssignment.setStatus(FleetAssignment.AssignmentStatus.ACTIVE);
        
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));
        when(fleetAssignmentRepository.findById(1L)).thenReturn(Optional.of(testFleetAssignment));

        // When & Then
        BusinessException exception = assertThrows(BusinessException.class,
                () -> fleetManagementService.deleteFleetAssignment(1L, "testuser"));

        assertEquals("Cannot delete active or in-progress fleet assignment", exception.getMessage());
        verify(fleetAssignmentRepository, never()).delete(any(FleetAssignment.class));
    }
}
