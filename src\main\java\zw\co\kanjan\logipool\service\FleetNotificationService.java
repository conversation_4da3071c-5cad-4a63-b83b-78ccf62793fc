package zw.co.kanjan.logipool.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import zw.co.kanjan.logipool.entity.*;
import zw.co.kanjan.logipool.repository.*;

import java.time.LocalDateTime;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class FleetNotificationService {
    
    private final TruckHeadRepository truckHeadRepository;
    private final TrailerRepository trailerRepository;
    private final InsuranceRepository insuranceRepository;
    private final TaxClearanceRepository taxClearanceRepository;
    private final PermitRepository permitRepository;
    private final NotificationService notificationService;
    
    /**
     * Daily check for expiring documents - runs every day at 8:00 AM
     */
    @Scheduled(cron = "0 0 8 * * *")
    public void checkExpiringDocuments() {
        log.info("Starting daily check for expiring fleet documents");
        
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime sevenDaysFromNow = now.plusDays(7);
        LocalDateTime thirtyDaysFromNow = now.plusDays(30);
        
        // Check for documents expiring in 7 days (critical alerts)
        checkCriticalExpiryAlerts(now, sevenDaysFromNow);
        
        // Check for documents expiring in 30 days (warning alerts)
        checkWarningExpiryAlerts(sevenDaysFromNow, thirtyDaysFromNow);
        
        log.info("Completed daily check for expiring fleet documents");
    }
    
    /**
     * Weekly fleet maintenance reminder - runs every Monday at 9:00 AM
     */
    @Scheduled(cron = "0 0 9 * * MON")
    public void sendWeeklyMaintenanceReminders() {
        log.info("Sending weekly fleet maintenance reminders");
        
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime nextWeek = now.plusDays(7);
        
        // Check truck heads needing maintenance
        List<TruckHead> truckHeadsNeedingMaintenance = truckHeadRepository.findAll().stream()
                .filter(truck -> truck.getNextMaintenanceDate() != null && 
                               truck.getNextMaintenanceDate().isBefore(nextWeek))
                .toList();
        
        for (TruckHead truckHead : truckHeadsNeedingMaintenance) {
            sendMaintenanceReminderNotification(truckHead);
        }
        
        // Check trailers needing maintenance
        List<Trailer> trailersNeedingMaintenance = trailerRepository.findAll().stream()
                .filter(trailer -> trailer.getNextMaintenanceDate() != null && 
                                 trailer.getNextMaintenanceDate().isBefore(nextWeek))
                .toList();
        
        for (Trailer trailer : trailersNeedingMaintenance) {
            sendMaintenanceReminderNotification(trailer);
        }
        
        log.info("Completed sending weekly fleet maintenance reminders");
    }
    
    /**
     * Monthly compliance report - runs on the 1st of every month at 10:00 AM
     */
    @Scheduled(cron = "0 0 10 1 * *")
    public void sendMonthlyComplianceReport() {
        log.info("Generating monthly fleet compliance reports");
        
        // This would generate and send comprehensive compliance reports
        // Implementation would depend on specific reporting requirements
        
        log.info("Completed monthly fleet compliance reports");
    }
    
    private void checkCriticalExpiryAlerts(LocalDateTime now, LocalDateTime sevenDaysFromNow) {
        // Insurance expiry alerts
        List<Insurance> expiringInsurances = insuranceRepository.findByExpiryDateBetween(now, sevenDaysFromNow);
        for (Insurance insurance : expiringInsurances) {
            sendInsuranceExpiryAlert(insurance, "CRITICAL");
        }
        
        // Tax clearance expiry alerts
        List<TaxClearance> expiringTaxClearances = taxClearanceRepository.findByExpiryDateBetween(now, sevenDaysFromNow);
        for (TaxClearance taxClearance : expiringTaxClearances) {
            sendTaxClearanceExpiryAlert(taxClearance, "CRITICAL");
        }
        
        // Permit expiry alerts
        List<Permit> expiringPermits = permitRepository.findByExpiryDateBetween(now, sevenDaysFromNow);
        for (Permit permit : expiringPermits) {
            sendPermitExpiryAlert(permit, "CRITICAL");
        }
        
        // Truck head fitness certificate expiry
        List<TruckHead> truckHeadsWithExpiringFitness = truckHeadRepository.findByFitnessExpiryDateBetween(now, sevenDaysFromNow);
        for (TruckHead truckHead : truckHeadsWithExpiringFitness) {
            sendFitnessExpiryAlert(truckHead, "CRITICAL");
        }
        
        // Trailer fitness certificate expiry
        List<Trailer> trailersWithExpiringFitness = trailerRepository.findByFitnessExpiryDateBetween(now, sevenDaysFromNow);
        for (Trailer trailer : trailersWithExpiringFitness) {
            sendFitnessExpiryAlert(trailer, "CRITICAL");
        }
    }
    
    private void checkWarningExpiryAlerts(LocalDateTime sevenDaysFromNow, LocalDateTime thirtyDaysFromNow) {
        // Insurance expiry warnings
        List<Insurance> expiringInsurances = insuranceRepository.findByExpiryDateBetween(sevenDaysFromNow, thirtyDaysFromNow);
        for (Insurance insurance : expiringInsurances) {
            sendInsuranceExpiryAlert(insurance, "WARNING");
        }
        
        // Tax clearance expiry warnings
        List<TaxClearance> expiringTaxClearances = taxClearanceRepository.findByExpiryDateBetween(sevenDaysFromNow, thirtyDaysFromNow);
        for (TaxClearance taxClearance : expiringTaxClearances) {
            sendTaxClearanceExpiryAlert(taxClearance, "WARNING");
        }
        
        // Permit expiry warnings
        List<Permit> expiringPermits = permitRepository.findByExpiryDateBetween(sevenDaysFromNow, thirtyDaysFromNow);
        for (Permit permit : expiringPermits) {
            sendPermitExpiryAlert(permit, "WARNING");
        }
    }
    
    private void sendInsuranceExpiryAlert(Insurance insurance, String severity) {
        String resourceName = getInsuranceResourceName(insurance);
        String title = severity.equals("CRITICAL") ? "URGENT: Insurance Expiring Soon" : "Insurance Renewal Reminder";
        String message = String.format("Insurance policy %s for %s expires on %s. Please renew immediately.",
                insurance.getPolicyNumber(), resourceName, insurance.getExpiryDate().toLocalDate());
        
        sendNotificationToCompany(insurance.getCompany(), title, message, "INSURANCE_EXPIRY", severity);
    }
    
    private void sendTaxClearanceExpiryAlert(TaxClearance taxClearance, String severity) {
        String resourceName = getTaxClearanceResourceName(taxClearance);
        String title = severity.equals("CRITICAL") ? "URGENT: Tax Clearance Expiring Soon" : "Tax Clearance Renewal Reminder";
        String message = String.format("Tax clearance %s for %s expires on %s. Please renew to maintain compliance.",
                taxClearance.getCertificateNumber(), resourceName, taxClearance.getExpiryDate().toLocalDate());
        
        sendNotificationToCompany(taxClearance.getCompany(), title, message, "TAX_CLEARANCE_EXPIRY", severity);
    }
    
    private void sendPermitExpiryAlert(Permit permit, String severity) {
        String resourceName = getPermitResourceName(permit);
        String title = severity.equals("CRITICAL") ? "URGENT: Permit Expiring Soon" : "Permit Renewal Reminder";
        String message = String.format("Permit %s for %s expires on %s. Please renew to continue operations.",
                permit.getPermitNumber(), resourceName, permit.getExpiryDate().toLocalDate());
        
        sendNotificationToCompany(permit.getCompany(), title, message, "PERMIT_EXPIRY", severity);
    }
    
    private void sendFitnessExpiryAlert(TruckHead truckHead, String severity) {
        String title = severity.equals("CRITICAL") ? "URGENT: Fitness Certificate Expiring Soon" : "Fitness Certificate Renewal Reminder";
        String message = String.format("Fitness certificate for truck head %s expires on %s. Please renew to maintain roadworthiness.",
                truckHead.getRegistrationNumber(), truckHead.getFitnessExpiryDate().toLocalDate());
        
        sendNotificationToCompany(truckHead.getCompany(), title, message, "FITNESS_EXPIRY", severity);
    }
    
    private void sendFitnessExpiryAlert(Trailer trailer, String severity) {
        String title = severity.equals("CRITICAL") ? "URGENT: Fitness Certificate Expiring Soon" : "Fitness Certificate Renewal Reminder";
        String message = String.format("Fitness certificate for trailer %s expires on %s. Please renew to maintain roadworthiness.",
                trailer.getRegistrationNumber(), trailer.getFitnessExpiryDate().toLocalDate());
        
        sendNotificationToCompany(trailer.getCompany(), title, message, "FITNESS_EXPIRY", severity);
    }
    
    private void sendMaintenanceReminderNotification(TruckHead truckHead) {
        String title = "Scheduled Maintenance Due";
        String message = String.format("Truck head %s is due for maintenance on %s. Please schedule service to prevent breakdowns.",
                truckHead.getRegistrationNumber(), truckHead.getNextMaintenanceDate().toLocalDate());
        
        sendNotificationToCompany(truckHead.getCompany(), title, message, "MAINTENANCE_DUE", "MEDIUM");
    }
    
    private void sendMaintenanceReminderNotification(Trailer trailer) {
        String title = "Scheduled Maintenance Due";
        String message = String.format("Trailer %s is due for maintenance on %s. Please schedule service to prevent breakdowns.",
                trailer.getRegistrationNumber(), trailer.getNextMaintenanceDate().toLocalDate());
        
        sendNotificationToCompany(trailer.getCompany(), title, message, "MAINTENANCE_DUE", "MEDIUM");
    }
    
    private void sendNotificationToCompany(Company company, String title, String message, String type, String severity) {
        try {
            // Send notification to company owner
            if (company.getUser() != null) {
                notificationService.sendFleetNotification(company.getUser(), title, message, type, severity);
            }
            
            // Send notification to company admins and fleet managers
            // This would require additional logic to identify fleet managers
            
            log.info("Sent {} notification to company {}: {}", severity, company.getName(), title);
        } catch (Exception e) {
            log.error("Failed to send notification to company {}: {}", company.getName(), e.getMessage());
        }
    }
    
    private String getInsuranceResourceName(Insurance insurance) {
        if (insurance.getTruckHead() != null) return "Truck Head " + insurance.getTruckHead().getRegistrationNumber();
        if (insurance.getTrailer() != null) return "Trailer " + insurance.getTrailer().getRegistrationNumber();
        if (insurance.getVehicle() != null) return "Vehicle " + insurance.getVehicle().getRegistrationNumber();
        if (insurance.getEquipment() != null) return "Equipment " + insurance.getEquipment().getName();
        return "Company " + insurance.getCompany().getName();
    }
    
    private String getTaxClearanceResourceName(TaxClearance taxClearance) {
        if (taxClearance.getTruckHead() != null) return "Truck Head " + taxClearance.getTruckHead().getRegistrationNumber();
        if (taxClearance.getVehicle() != null) return "Vehicle " + taxClearance.getVehicle().getRegistrationNumber();
        return "Company " + taxClearance.getCompany().getName();
    }
    
    private String getPermitResourceName(Permit permit) {
        if (permit.getTruckHead() != null) return "Truck Head " + permit.getTruckHead().getRegistrationNumber();
        if (permit.getTrailer() != null) return "Trailer " + permit.getTrailer().getRegistrationNumber();
        if (permit.getVehicle() != null) return "Vehicle " + permit.getVehicle().getRegistrationNumber();
        return "Company " + permit.getCompany().getName();
    }
}
